package com.dyd.common.datascope.aspect;

import java.util.*;
import java.util.stream.Collectors;

import com.dyd.common.datascope.annotation.DataScope;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.vo.SysRoleMenuScopeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.dyd.common.core.context.SecurityContextHolder;
import com.dyd.common.core.text.Convert;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.core.web.domain.BaseEntity;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.system.api.domain.SysRole;
import com.dyd.system.api.domain.SysUser;
import com.dyd.system.api.model.LoginUser;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 菜单数据权限
     */
    public static final String DATA_SCOPE_MENU = "6";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Autowired(required = false)
    private RemoteUserService userService;

    @Autowired(required = false)
    private RemoteSysService permissionsCacheService;

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser)) {
            SysUser currentUser = loginUser.getSysUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin()) {
                if (userService != null) {
                    SysUser realm = userService.querySysUser(currentUser.getUserId());
                    if (realm != null) {
                        currentUser = realm;
                    }
                }
                String permission = StringUtils.defaultIfEmpty(controllerDataScope.permission(), SecurityContextHolder.getPermission());
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(),
                        controllerDataScope.userAlias(), permission, controllerDataScope.menu());
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint  切点
     * @param user       用户
     * @param deptAlias  部门别名
     * @param userAlias  用户别名
     * @param permission 权限字符
     * @param menu       菜单标识
     */
    public void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias, String permission, String menu) {
        StringBuilder sqlString = new StringBuilder();

        // 当传入menu参数时，只匹配配置了该menu的角色
        if (StringUtils.isNotBlank(menu)) {
            // 找到具有该菜单权限的角色
            boolean foundRoleWithMenuPerm = false;
            List<Long> roleIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(user.getRoles())) {
                roleIds = user.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList());
            }
            Map<Long, SysRoleMenuScopeVO> roleMenuScopeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(roleIds)) {
                roleMenuScopeMap = permissionsCacheService.getSysUserDataScope(menu, roleIds);
            }
            for (SysRole role : user.getRoles()) {
                // 跳过没有权限的角色
                if (StringUtils.isNotEmpty(permission) && StringUtils.isNotEmpty(role.getPermissions())
                        && !StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission))) {
                    continue;
                }

                // 处理菜单数据权限，只处理拥有菜单数据权限的角色
                if (DATA_SCOPE_MENU.equals(role.getDataScope())) {
                    // 查询角色是否有此菜单的权限配置，后面优化批量查询
                    SysRoleMenuScopeVO menuScope = roleMenuScopeMap.get(role.getRoleId());
                    if (menuScope != null && StringUtils.isNotBlank(menuScope.getDataScope())) {
                        // 找到了配置此菜单的角色，使用此角色的权限
                        boolean all = handleMenuDataScopeByMenu(sqlString, role, user, deptAlias, userAlias, menu,menuScope);
                        foundRoleWithMenuPerm = true;
                        if (all) {
                            sqlString.setLength(0);
                            break;
                        }
                    }
                }
            }

            // 如果没有找到配置该菜单的角色，回退到最大权限策略
            if (!foundRoleWithMenuPerm) {
                applyMaximumPermissionStrategy(sqlString, user, deptAlias, userAlias, permission);
            }
        }
        // 当menu参数为空时，采用最大权限策略
        else {
            applyMaximumPermissionStrategy(sqlString, user, deptAlias, userAlias, permission);
        }

        // 如果SQL条件不为空，添加到查询参数中
        if (StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 应用最大权限策略，找出用户所有角色中权限最大的
     *
     * @param sqlString  SQL字符串
     * @param user       用户信息
     * @param deptAlias  部门别名
     * @param userAlias  用户别名
     * @param permission 权限字符
     */
    private void applyMaximumPermissionStrategy(StringBuilder sqlString, SysUser user, String deptAlias, String userAlias, String permission) {
        // 记录角色的数据权限，用于判断是否重复
        List<String> conditions = new ArrayList<String>();

        // 记录是否发现全部数据权限
        boolean hasAllDataScope = false;

        // 遍历用户的所有角色，寻找最大权限
        for (SysRole role : user.getRoles()) {
            String dataScope = role.getDataScope();

            // 跳过重复的非自定义数据权限
            if (!DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope)) {
                continue;
            }

            // 跳过没有权限的角色
            if (StringUtils.isNotEmpty(permission) && StringUtils.isNotEmpty(role.getPermissions())
                    && !StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission))) {
                continue;
            }

            // 如果发现全部数据权限，直接清空条件并退出，这是最大权限
            if (DATA_SCOPE_ALL.equals(dataScope)) {
                sqlString.setLength(0);
                hasAllDataScope = true;
                break;
            }

            // 处理其他类型的数据权限，构建OR条件
            handleDataScope(sqlString, dataScope, role, user, deptAlias, userAlias);
            conditions.add(dataScope);
        }

        // 如果没有找到全部数据权限，但有多个其他权限，则使用OR连接，形成权限的并集
        // 这是已经在handleDataScope中通过OR关键字实现的
    }

    /**
     * 处理数据权限
     *
     * @param sqlString SQL字符串
     * @param dataScope 数据范围
     * @param role      角色信息
     * @param user      用户信息
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     * @return 是否中断当前循环
     */
    private boolean handleDataScope(StringBuilder sqlString, String dataScope, SysRole role,
                                    SysUser user, String deptAlias, String userAlias) {
        if (DATA_SCOPE_ALL.equals(dataScope)) {
            sqlString.setLength(0); // 清空StringBuilder
            return true; // 中断当前循环
        } else if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
            sqlString.append(StringUtils.format(
                    " OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ",
                    deptAlias, role.getRoleId()));
        } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
            sqlString.append(StringUtils.format(" OR {}.dept_id = {} ",
                    deptAlias, user.getDeptId()));
        } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
            sqlString.append(StringUtils.format(
                    " OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                    deptAlias, user.getDeptId(), user.getDeptId()));
        } else if (DATA_SCOPE_SELF.equals(dataScope)) {
            if (StringUtils.isNotBlank(userAlias)) {
                sqlString.append(StringUtils.format(" OR {}.user_id = {} ",
                        userAlias, user.getUserId()));
            } else {
                sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
            }
        }
        return false;
    }

    /**
     * 处理数据权限
     *
     * @param sqlString SQL字符串
     * @param dataScope 数据范围
     * @param role      角色信息
     * @param user      用户信息
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     * @return 是否中断当前循环
     */
    private boolean handleDataScopeByMenu(StringBuilder sqlString, String dataScope, SysRole role,
                                          SysUser user, String deptAlias, String userAlias, List<Long> deptIds) {
        if (DATA_SCOPE_ALL.equals(dataScope)) {
            sqlString.setLength(0); // 清空StringBuilder
            return true; // 中断当前循环
        } else if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
            sqlString.append(StringUtils.format(
                    " OR {}.dept_id IN ( " + deptIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + "  ) ",
                    deptAlias, role.getRoleId()));
        } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
            sqlString.append(StringUtils.format(" OR {}.dept_id = {} ",
                    deptAlias, user.getDeptId()));
        } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
            sqlString.append(StringUtils.format(
                    " OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
                    deptAlias, user.getDeptId(), user.getDeptId()));
        } else if (DATA_SCOPE_SELF.equals(dataScope)) {
            if (StringUtils.isNotBlank(userAlias)) {
                sqlString.append(StringUtils.format(" OR {}.user_id = {} ",
                        userAlias, user.getUserId()));
            } else {
                sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
            }
        }
        return false;
    }

    /**
     * 处理菜单数据权限
     *
     * @param sqlString SQL字符串
     * @param role      角色信息
     * @param user      用户信息
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     * @param menu      菜单权限字符
     */
    private boolean handleMenuDataScopeByMenu(StringBuilder sqlString, SysRole role,
                                              SysUser user, String deptAlias, String userAlias, String menu,SysRoleMenuScopeVO menuScope) {
        if (StringUtils.isBlank(menu)) {
            return true;
        }


        // 获取角色菜单的数据权限配置
//        Map<Long, SysRoleMenuScopeVO> roleMenuScopeMap = permissionsCacheService.getSysUserDataScope(menu, Arrays.asList(role.getRoleId()));
//        SysRoleMenuScopeVO menuScope = roleMenuScopeMap.get(role.getRoleId());
        if (menuScope == null || StringUtils.isBlank(menuScope.getDataScope())) {
            return true;
        }

        // 处理菜单数据权限
        String dataScopeType = menuScope.getDataScope();

        // 对于全部数据权限，需要特殊处理
        if (DATA_SCOPE_ALL.equals(dataScopeType)) {
            sqlString.setLength(0); // 清空已有SQL语句
            return true; // 中断当前循环
        } else {
            // 创建临时SQL字符串对象，避免直接修改原始sqlString
            StringBuilder tempSql = new StringBuilder();
            boolean has = handleDataScopeByMenu(tempSql, dataScopeType, role, user, deptAlias, userAlias, menuScope.getDeptList());
            // 将临时SQL拼接到主SQL中
            if (tempSql.length() > 0) {
                sqlString.append(tempSql);
                return false;
            } else {
                return true;
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}
