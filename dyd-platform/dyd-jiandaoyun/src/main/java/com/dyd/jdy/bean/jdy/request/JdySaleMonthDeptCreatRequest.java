package com.dyd.jdy.bean.jdy.request;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.common.ValueList;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.jdy.response.JdyOrderBaseUpdateResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * V2 销售市场中心月度绩效目标导入表（事业部维度）
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class JdySaleMonthDeptCreatRequest extends JdyRequestV5<JdyOrderBaseUpdateResponse> {

    private String transaction_id;

    private JdySaleMonthDeptCreat data;

    private String data_id;

    @Data
    public static class JdySaleMonthDeptCreat {

        /**
         * 所属事业部
         */
        private String _widget_1718093251976;

        /**
         * 年度
         */
        private String _widget_1718093251977;

        /**
         * 月份
         */
        private String _widget_1718093251978;

        /**
         * 日期时间
         */
        private String _widget_1721805811440;

        /**
         * 周
         */
        private String _widget_1721805811446;

        /**
         * 实际毛利额
         */
        private String _widget_1732237918128;

        /**
         * 实际毛利率
         */
        private String _widget_1732237918129;

        /**
         * 实际新增客户数
         */
        private String _widget_1732237918130;

        /**
         * 实际客户及商机跟进数
         */
        private String _widget_1732237918131;

        /**
         * 实际销售自拓商机数-项目类
         */
        private String _widget_1732237918132;

        /**
         * 实际销售自拓商机数-贸易类
         */
        private String _widget_1732237918133;

        /**
         * 实际销售自拓商机数-备件类
         */
        private String _widget_1732237918134;

        /**
         * 实际每月新增商机额-项目类
         */
        private String _widget_1732237918135;

        /**
         * 实际每月新增商机额-贸易类
         */
        private String _widget_1732237918136;

        /**
         * 实际每月新增商机额-备件类
         */
        private String _widget_1732237918137;

        /**
         * 实际商机->合同订单转化率
         */
        private String _widget_1732237918138;

        /**
         * 实际应收回款
         */
        private String _widget_1732237918139;

        /**
         * 实际业绩额
         */
        private String _widget_1732237918127;



        //renyuan
        /**
         * 实际毛利额
         */
        private String _widget_1732151176585;

        /**
         * 实际毛利率
         */
        private String _widget_1732151176586;

        /**
         * 实际新增客户数
         */
        private String _widget_1732151176587;

        /**
         * 实际客户及商机跟进数
         */
        private String _widget_1732151176594;

        /**
         * 实际销售自拓商机数-项目类
         */
        private String _widget_1732151176588;

        /**
         * 实际销售自拓商机数-贸易类
         */
        private String _widget_1732151176595;

        /**
         * 实际销售自拓商机数-备件类
         */
        private String _widget_1732151176589;

        /**
         * 实际每月新增商机额-项目类
         */
        private String _widget_1732151176593;

        /**
         * 实际每月新增商机额-贸易类
         */
        private String _widget_1732151176596;

        /**
         * 实际每月新增商机额-备件类
         */
        private String _widget_1732151176597;

        /**
         * 实际商机->合同订单转化率
         */
        private String _widget_1732151176598;

        /**
         * 实际应收回款
         */
        private String _widget_1732151176599;

        /**
         * 实际业绩额
         */
        private String _widget_1732151176578;

        /**
         * 事业部Gp1
         */
        private String _widget_1736486741291;

        /**
         * 事业部GP3
         */
        private String _widget_1736486741292;

        /**
         * 实际出货额
         */
        private String _widget_1748228535666;

        /**
         * 销售员gp1
         */
        private String _widget_1736486880393;

        /**
         * 销售员gp3
         */
        private String _widget_1736486880394;


        /**
         * 实际出货额
         */
        private String _widget_1748325430540;

    }


    @Override
    public Class<JdyOrderBaseUpdateResponse> getResponseClass() {
        return JdyOrderBaseUpdateResponse.class;
    }
}
