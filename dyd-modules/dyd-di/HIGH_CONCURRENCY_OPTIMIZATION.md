# 营销版图映射服务高并发优化方案

## 问题背景

在高并发场景下（一直add、del不停切换调用），即使有事务保护，仍然会出现唯一索引违反的问题。这是因为：

1. **数据库唯一索引限制**：`udex` 索引为 `(territory_id, ref_id)`，不包含 `product_id`
2. **高并发竞争条件**：多个线程同时操作同一个 `refId + territoryId` 组合
3. **事务隔离级别不足**：默认的事务隔离级别无法完全避免并发冲突
4. **删除和插入的时间窗口**：即使在事务中，仍可能有微小的时间窗口导致冲突

## 高并发优化方案

### 1. 核心策略：同步锁 + 事务控制

采用 **JVM级别的同步锁** + **数据库事务** 的双重保护机制：

```java
@Override
public void add(MarketingTerritoryMappingVO reqVO) {
    // 使用 refId + territoryId 作为锁的key，确保同一个业务版图的操作串行化
    String lockKey = generateLockKey(reqVO.getRefId(), reqVO.getTerritoryId());
    
    synchronized (lockKey.intern()) {
        try {
            addWithTransaction(reqVO);
        } catch (Exception e) {
            log.error("添加营销版图映射失败，refId: {}, territoryId: {}, 错误: {}", 
                reqVO.getRefId(), reqVO.getTerritoryId(), e.getMessage(), e);
            throw e;
        }
    }
}
```

### 2. 锁粒度设计

**锁的粒度**：`refId + territoryId`
- ✅ **精确控制**：只对相同的业务版图加锁，不影响其他业务版图的并发操作
- ✅ **性能优化**：避免全局锁，最大化并发性能
- ✅ **死锁避免**：单一锁key，避免多锁导致的死锁问题

**锁key生成**：
```java
private String generateLockKey(String refId, Long territoryId) {
    return "territory_mapping_lock:" + refId + ":" + territoryId;
}
```

### 3. 安全插入机制

针对批量插入场景，采用 **先检查再插入** 的安全机制：

```java
private void insertSingleMappingWithRetry(DiTerritoryProductMapping mapping) {
    // 先检查是否已存在
    Long existingCount = diTerritoryProductMappingMapper.selectCount(
        Wrappers.<DiTerritoryProductMapping>lambdaQuery()
            .eq(DiTerritoryProductMapping::getRefId, mapping.getRefId())
            .eq(DiTerritoryProductMapping::getTerritoryId, mapping.getTerritoryId())
            .eq(DiTerritoryProductMapping::getProductId, mapping.getProductId())
    );
    
    if (existingCount == 0) {
        diTerritoryProductMappingMapper.insert(mapping);
    } else {
        log.debug("产品映射已存在，跳过插入：refId={}, territoryId={}, productId={}", 
            mapping.getRefId(), mapping.getTerritoryId(), mapping.getProductId());
    }
}
```

### 4. 异常处理和降级

**多层异常处理**：
1. **插入失败** → 尝试更新操作
2. **更新失败** → 记录错误日志，不中断整个流程
3. **关键异常** → 向上抛出，触发事务回滚

```java
try {
    insertSingleMappingWithRetry(mapping);
} catch (Exception e) {
    log.warn("插入产品映射失败，尝试更新操作, 错误: {}", e.getMessage());
    
    try {
        updateExistingMapping(mapping);
    } catch (Exception updateEx) {
        log.error("更新产品映射也失败，错误: {}", updateEx.getMessage());
    }
}
```

## 技术实现细节

### 1. 同步锁实现

**使用 `synchronized` + `String.intern()`**：
- `synchronized`：JVM级别的互斥锁
- `String.intern()`：确保相同字符串使用同一个对象实例
- 优点：简单可靠，无需引入额外依赖
- 缺点：仅限单JVM，不支持分布式场景

### 2. 事务控制

**分离锁控制和事务控制**：
```java
// 外层：锁控制
public void add(MarketingTerritoryMappingVO reqVO) {
    synchronized (lockKey.intern()) {
        addWithTransaction(reqVO);
    }
}

// 内层：事务控制
@Transactional(rollbackFor = Exception.class)
public void addWithTransaction(MarketingTerritoryMappingVO reqVO) {
    // 具体的数据库操作
}
```

### 3. 性能优化

**批量操作优化**：
- 保留删除操作的批量性
- 插入操作改为逐个处理，增加安全性
- 在锁保护下，性能影响可接受

**日志级别控制**：
- 正常情况：DEBUG级别
- 异常情况：WARN/ERROR级别
- 避免过多日志影响性能

## 测试验证

### 1. 高并发测试

```java
@Test
void testHighConcurrencyAddAndDel() throws InterruptedException {
    int threadCount = 10;
    int operationsPerThread = 5;
    
    // 启动多个线程同时执行add和del操作
    // 验证同步锁机制的有效性
}
```

### 2. 压力测试建议

**生产环境验证**：
1. **并发线程数**：50-100个线程
2. **操作频率**：每秒100-500次操作
3. **测试时长**：持续30分钟以上
4. **监控指标**：
   - 异常率：应为0%
   - 响应时间：P99 < 500ms
   - 数据一致性：无重复或丢失数据

## 适用场景和限制

### 适用场景
- ✅ **单体应用**：JVM级别的锁机制
- ✅ **中等并发**：QPS < 1000的场景
- ✅ **数据一致性要求高**：金融、电商等业务

### 限制和注意事项
- ❌ **分布式环境**：需要升级为分布式锁（Redis/Zookeeper）
- ❌ **极高并发**：QPS > 5000时需要考虑其他方案
- ⚠️ **锁粒度**：当前按 `refId + territoryId` 加锁，需要评估业务影响

## 后续优化方向

### 1. 分布式锁升级
如果需要支持分布式环境，可以升级为Redis分布式锁：
```java
@Component
public class DistributedLockService {
    public void executeWithLock(String lockKey, Runnable task) {
        // Redis分布式锁实现
    }
}
```

### 2. 数据库层面优化
- **修改唯一索引**：将 `product_id` 包含到唯一索引中
- **使用乐观锁**：添加版本号字段
- **读写分离**：查询操作使用从库

### 3. 缓存机制
- **本地缓存**：缓存热点数据，减少数据库查询
- **分布式缓存**：Redis缓存，提高查询性能

## 总结

此次高并发优化采用了 **同步锁 + 事务控制 + 安全插入** 的综合方案，能够有效解决高并发场景下的唯一约束违反问题。该方案具有以下特点：

- 🛡️ **安全性**：双重保护机制，确保数据一致性
- ⚡ **性能**：精确锁粒度，最大化并发性能  
- 🔧 **可维护性**：代码结构清晰，易于理解和维护
- 📊 **可监控性**：完善的日志记录，便于问题排查

该方案适用于大多数单体应用的高并发场景，为业务的稳定运行提供了可靠保障。
