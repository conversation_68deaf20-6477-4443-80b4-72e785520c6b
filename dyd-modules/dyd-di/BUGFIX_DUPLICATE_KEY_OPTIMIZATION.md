# 营销版图映射服务唯一约束违反问题修复与优化

## 问题描述

在 `MarketingTerritoryMappingServiceImpl.add()` 方法中，当处理批量产品映射时出现了数据库唯一约束违反异常：

```
Duplicate entry '22-DYD-SJ250529-0008' for key 'di_territory_product_mapping.udex'
```

## 问题根因分析

### 原始代码问题

在原始代码的第71-82行中：

```java
List<DiTerritoryProductMapping> mappingList = res.getData().stream().map(m -> {
    QueryProductByTerritoryResponse newR =
            QueryProductByTerritoryResponse.builder()
                    .titleMap(res.getTitleMap())
                    .data(Lists.newArrayList(m))
                    .total(1L)
                    .build();
    reqVO.setProductJson(JSON.toJSONString(newR));
    reqVO.setProductId(Long.parseLong(m.get("id")));  // 问题所在：修改共享对象
    return getTerritoryProductMapping(reqVO);
}).toList();
```

**问题根源**：
1. **共享状态修改**：在 stream 的 map 操作中，所有迭代都在修改同一个 `reqVO` 对象
2. **数据覆盖**：每次调用 `reqVO.setProductId()` 都会覆盖之前设置的值
3. **重复数据**：最终所有的 `DiTerritoryProductMapping` 对象都具有相同的 `productId`（最后一次设置的值）
4. **约束违反**：当批量插入时，由于 `refId + territoryId + productId` 的组合重复，触发了数据库的唯一约束违反

### 其他问题

1. **空值处理不当**：`getTerritoryProductMapping` 方法中直接调用 `.getData()` 可能返回 null
2. **代码结构复杂**：单一方法承担过多职责，难以维护和测试
3. **错误处理缺失**：没有对服务调用失败的情况进行处理

## 解决方案

### 优化策略

1. **避免共享状态**：为每个产品创建独立的 VO 对象，而不是修改同一个对象
2. **代码重构**：将复杂的逻辑拆分为更小、更易理解的方法
3. **增强错误处理**：添加空值检查和异常处理
4. **完善注释**：提高代码可读性和维护性

### 具体修改

#### 1. 重构 `add()` 方法

将原来的单一方法拆分为多个专门的方法：

```java
@Override
public void add(MarketingTerritoryMappingVO reqVO) {
    // 1. 处理业务版图映射
    handleTerritoryMapping(reqVO);
    
    // 2. 处理产品映射关系
    if (reqVO.getProductId() != null) {
        // 单个产品处理
        handleSingleProductMapping(reqVO);
    } else {
        // 批量产品处理
        handleBatchProductMapping(reqVO);
    }
}
```

#### 2. 新增专门的处理方法

- `handleTerritoryMapping()`: 处理业务版图映射
- `handleSingleProductMapping()`: 处理单个产品映射
- `handleBatchProductMapping()`: 处理批量产品映射
- `createProductMapping()`: 为单个产品创建映射对象

#### 3. 核心修复：避免共享状态

```java
private DiTerritoryProductMapping createProductMapping(MarketingTerritoryMappingVO originalVO,
                                                      QueryProductByTerritoryResponse res,
                                                      Map<String, String> productData) {
    // 创建独立的VO对象，避免修改原始对象
    MarketingTerritoryMappingVO productVO = new MarketingTerritoryMappingVO();
    productVO.setRefId(originalVO.getRefId());
    productVO.setTerritoryId(originalVO.getTerritoryId());
    productVO.setTerritoryJson(originalVO.getTerritoryJson());
    productVO.setProductId(Long.parseLong(productData.get("id")));
    productVO.setProductJson(JSON.toJSONString(singleProductResponse));
    
    return getTerritoryProductMapping(productVO);
}
```

#### 4. 增强错误处理

```java
private DiTerritoryProductMapping getTerritoryProductMapping(MarketingTerritoryMappingVO reqVO) {
    // 获取产品详情，注意处理返回类型和空值
    R<MatrixProductDetailDTO> productDetailResult = diMatrixProductService.getMatrixProductDetail(
            OperateMatrixProductVO.builder().id(reqVO.getProductId()).build());
    
    MatrixProductDetailDTO data = null;
    if (productDetailResult != null && productDetailResult.isSuccess() && productDetailResult.getData() != null) {
        data = productDetailResult.getData();
    }
    
    return DiTerritoryProductMapping.builder()
            .refId(reqVO.getRefId())
            .territoryId(reqVO.getTerritoryId())
            .productId(reqVO.getProductId())
            .productJson(reqVO.getProductJson())
            .productInfo(data != null ? JSON.toJSONString(data) : null)
            .build();
}
```

## 修复效果

### 修复前
- ❌ 所有产品映射对象具有相同的 `productId`
- ❌ 批量插入时触发唯一约束违反异常
- ❌ 代码逻辑复杂，难以维护
- ❌ 缺乏错误处理和空值检查

### 修复后
- ✅ 每个产品映射对象具有独立的 `productId`
- ✅ 避免了唯一约束违反问题
- ✅ 代码结构清晰，职责分离
- ✅ 增加了完整的错误处理和空值检查
- ✅ 添加了详细的方法注释

## 测试验证

创建了 `MarketingTerritoryMappingServiceImplTest` 测试类，包含以下测试用例：

1. **`testAddBatchProductMapping_ShouldCreateUniqueProductMappings()`**: 验证批量产品映射的唯一性
2. **`testAddSingleProductMapping()`**: 验证单个产品映射功能
3. **`testAddWithEmptyProductData()`**: 验证空数据的处理
4. **`testAddWithProductServiceFailure()`**: 验证服务失败时的处理

## 性能影响

1. **内存开销**：创建独立对象会有轻微的内存开销，但相比解决的问题来说是可接受的
2. **执行效率**：方法拆分后逻辑更清晰，实际执行效率可能略有提升
3. **维护成本**：代码结构优化后，维护成本显著降低

## 注意事项

1. **数据一致性**：修复后确保每个产品都有正确的 `productId`
2. **向后兼容**：修改不影响现有的API接口和调用方式
3. **异常处理**：增强了异常处理，提高了系统的健壮性
4. **代码质量**：遵循单一职责原则，提高了代码的可读性和可维护性

## 相关文件

- **主要修复文件**：`MarketingTerritoryMappingServiceImpl.java`
- **测试文件**：`MarketingTerritoryMappingServiceImplTest.java`
- **实体类**：`DiTerritoryProductMapping.java`
- **VO类**：`MarketingTerritoryMappingVO.java`

## 总结

此次优化不仅解决了唯一约束违反的核心问题，还从代码质量、错误处理、可维护性等多个维度进行了全面提升，为后续的功能扩展和维护奠定了良好的基础。
