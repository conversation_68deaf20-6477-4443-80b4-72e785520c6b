<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.marketing.mapper.DiMarketingNicheMapper">

    <resultMap type="com.dyd.di.marketing.domain.DiMarketingNiche" id="DiMarketingNicheResult">
        <result property="id" column="id"/>
        <result property="nicheNo" column="niche_no"/>
        <result property="nicheStatus" column="niche_status"/>
        <result property="nicheStage" column="niche_stage"/>
        <result property="nicheSource" column="niche_source"/>
        <result property="otherSource" column="other_source"/>
        <result property="clueNo" column="clue_no"/>
        <result property="customerNo" column="customer_no"/>
        <result property="importance" column="importance"/>
        <result property="orderType" column="order_type"/>
        <result property="industryCategories" column="industry_categories"/>
        <result property="applicationCategories" column="application_categories"/>
        <result property="applicationSubcategories" column="application_subcategories"/>
        <result property="fourLevelClassification" column="four_level_classification"/>
        <result property="fourLevelClassificationOther" column="four_level_classification_other"/>


        <result property="industryCategoriesOther" column="industry_categories_other"/>
        <result property="applicationCategoriesOther" column="application_categories_other"/>
        <result property="applicationSubcategoriesOther" column="application_subcategories_other"/>
        <result property="expectedDeliveryDate" column="expected_delivery_date"/>
        <result property="expectedTransactionAmount" column="expected_transaction_amount"/>
        <result property="burnerBrand" column="burner_brand"/>
        <result property="valveBrand" column="valve_brand"/>
        <result property="fuel" column="fuel"/>
        <result property="onSiteConstruction" column="on_site_construction"/>
        <result property="scopeOfSupply" column="scope_of_supply"/>
        <result property="technicalDifficulty" column="technical_difficulty"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="needPostSaleInstall" column="need_post_sale_install"/>
        <result property="needTechSupport" column="need_tech_support"/>
        <result property="sharedBy" column="shared_by"/>
        <result property="projectName" column="project_name"/>

        <result property="nicheDetails" column="niche_details"/>
        <result property="remarks" column="remarks"/>
        <result property="nicheOwner" column="niche_owner"/>
        <result property="nicheOwnerDept" column="niche_owner_dept"/>
        <result property="nicheFile" column="niche_file"/>
        <result property="projectApproved" column="project_approved"/>
        <result property="delFlag" column="del_flag"/>

        <result property="feasibilityVerifyState" column="feasibility_verify_state"/>
        <result property="feasibilityVerifyProcessId" column="feasibility_verify_process_id"/>

        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="traceId" column="trace_id"/>
        <result property="invalidReason" column="invalid_reason"/>
        <result property="determineCause" column="determine_cause"/>
        <result property="expectationDeliveryWeek" column="expectation_delivery_week"/>

        <result property="mechanicalDifficulty" column="mechanical_difficulty"/>
        <result property="electricalDifficulty" column="electrical_difficulty"/>
        <result property="productionDifficulty" column="production_difficulty"/>
        <result property="afterSalesDifficulty" column="after_sales_difficulty"/>
        <result property="nicheRemarks" column="niche_remarks"/>
        <result property="submitProposalDate" column="submit_proposal_date"/>
        <result property="applyForTechnicalSupport" column="apply_for_technical_support"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="oldNicheStatus" column="old_niche_status"/>
        <result property="approvalBys" column="approval_bys"/>
        <result property="approvalUserIdBys" column="approval_user_id_bys"/>
    </resultMap>

    <sql id="selectDiMarketingNicheVo">
        select id,apply_for_technical_support,invalid_reason,determine_cause, niche_no ,four_level_classification,four_level_classification_other , project_name, niche_file,
        shared_by, technical_difficulty, project_approved, niche_status, niche_stage, niche_source, other_source,
        clue_no, customer_no, importance, order_type, industry_categories, application_categories,
        application_subcategories, industry_categories_other, application_categories_other,
        application_subcategories_other,expected_delivery_date, expected_transaction_amount, burner_brand, valve_brand,
        fuel, on_site_construction, scope_of_supply, niche_details, remarks, niche_owner, niche_owner_dept, del_flag,
        feasibility_verify_state, feasibility_verify_process_id, create_by, create_time, update_by, update_time,
        trace_id, difficulty_level, need_post_sale_install, need_tech_support,expectation_delivery_week,
        mechanical_difficulty,electrical_difficulty,production_difficulty,after_sales_difficulty,niche_remarks,submit_proposal_date
        ,process_instance_id,approval_status,old_niche_status,approval_bys,approval_user_id_bys
        from di_marketing_niche
    </sql>

    <select id="selectDiMarketingNicheList" parameterType="com.dyd.di.marketing.domain.DiMarketingNiche"
            resultMap="DiMarketingNicheResult">
        SELECT
        c.*,r.company_name companyName
        FROM
        di_marketing_niche c
        LEFT JOIN sys_user u ON c.create_by = u.user_name
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN (select customer_no,company_name,create_by,customer_owner from di_marketing_customer) r ON
        r.customer_no = c.customer_no
        where 1 = 1
        <if test="nicheNo != null  and nicheNo != ''">and niche_no like concat('%', #{nicheNo}, '%')</if>
        <if test="nicheStatus != null  and nicheStatus != ''">and niche_status = #{nicheStatus}</if>
        <if test="nicheStage != null  and nicheStage != ''">and niche_stage = #{nicheStage}</if>
        <if test="nicheSource != null  and nicheSource != ''">and niche_source = #{nicheSource}</if>
        <if test="otherSource != null  and otherSource != ''">and other_source = #{otherSource}</if>
        <if test="clueNo != null  and clueNo != ''">and clue_no like concat('%', #{clueNo}, '%')</if>
        <if test="companyName != null  and companyName != ''">and r.company_name like concat('%', #{companyName}, '%')
        </if>
        <if test="customerNo != null  and customerNo != ''">and customer_no like concat('%', #{customerNo}, '%')</if>
        <if test="importance != null  and importance != ''">and importance = #{importance}</if>
        <if test="orderType != null  and orderType != ''">and order_type = #{orderType}</if>
        <if test="industryCategories != null  and industryCategories != ''">and industry_categories =
            #{industryCategories}
        </if>
        <if test="applicationCategories != null  and applicationCategories != ''">and application_categories =
            #{applicationCategories}
        </if>
        <if test="applicationSubcategories != null  and applicationSubcategories != ''">and application_subcategories =
            #{applicationSubcategories}
        </if>
        <if test="nicheDetails != null  and nicheDetails != ''">and niche_details like concat('%', #{nicheDetails},
            '%')
        </if>
        <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
        <if test="nicheOwner != null  and nicheOwner != ''">and niche_owner = #{nicheOwner}</if>
        <if test="nicheOwnerDept != null  and nicheOwnerDept != ''">and niche_owner_dept = #{nicheOwnerDept}</if>
        <if test="nicheFile != null  and nicheFile != ''">and niche_file = #{nicheFile}</if>
        <if test="projectApproved != null  and projectApproved != ''">and project_approved = #{projectApproved}</if>
        <if test="traceId != null  and traceId != ''">and trace_id = #{traceId}</if>
        <if test="difficultyLevel!=null">and difficulty_Level=#{difficultyLevel}</if>
        <if test="needPostSaleInstall!=null">and need_post_sale_install=#{needPostSaleInstall}</if>
        <if test="needTechSupport!=null">and need_tech_support=#{needTechSupport}</if>
        <!-- 数据范围过滤 -->
        AND (c.niche_owner = ${params.loginUser} OR r.customer_owner = ${params.loginUser} OR c.shared_by like
        concat('%', ${params.loginUser}, '%') ${params.dataScope} )
    </select>

    <select id="selectDiMarketingNicheListNoAuth" parameterType="com.dyd.di.marketing.domain.DiMarketingNiche"
            resultMap="DiMarketingNicheResult">
        SELECT
        c.*,r.company_name companyName
        FROM
        di_marketing_niche c
        LEFT JOIN sys_user u ON c.create_by = u.user_name
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN (select customer_no,company_name,create_by,customer_owner from di_marketing_customer) r ON
        r.customer_no = c.customer_no
        where 1 = 1
        <if test="nicheNo != null  and nicheNo != ''">and niche_no like concat('%', #{nicheNo}, '%')</if>
        <if test="nicheStatus != null  and nicheStatus != ''">and niche_status = #{nicheStatus}</if>
        <if test="nicheStage != null  and nicheStage != ''">and niche_stage = #{nicheStage}</if>
        <if test="nicheSource != null  and nicheSource != ''">and niche_source = #{nicheSource}</if>
        <if test="otherSource != null  and otherSource != ''">and other_source = #{otherSource}</if>
        <if test="clueNo != null  and clueNo != ''">and clue_no like concat('%', #{clueNo}, '%')</if>
        <if test="companyName != null  and companyName != ''">and r.company_name like concat('%', #{companyName}, '%')
        </if>
        <if test="customerNo != null  and customerNo != ''">and customer_no like concat('%', #{customerNo}, '%')</if>
        <if test="importance != null  and importance != ''">and importance = #{importance}</if>
        <if test="orderType != null  and orderType != ''">and order_type = #{orderType}</if>
        <if test="industryCategories != null  and industryCategories != ''">and industry_categories =
            #{industryCategories}
        </if>
        <if test="applicationCategories != null  and applicationCategories != ''">and application_categories =
            #{applicationCategories}
        </if>
        <if test="applicationSubcategories != null  and applicationSubcategories != ''">and application_subcategories =
            #{applicationSubcategories}
        </if>
        <if test="nicheDetails != null  and nicheDetails != ''">and niche_details like concat('%', #{nicheDetails},
            '%')
        </if>
        <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
        <if test="nicheOwner != null  and nicheOwner != ''">and niche_owner = #{nicheOwner}</if>
        <if test="nicheOwnerDept != null  and nicheOwnerDept != ''">and niche_owner_dept = #{nicheOwnerDept}</if>
        <if test="nicheFile != null  and nicheFile != ''">and niche_file = #{nicheFile}</if>
        <if test="projectApproved != null  and projectApproved != ''">and project_approved = #{projectApproved}</if>
        <if test="traceId != null  and traceId != ''">and trace_id = #{traceId}</if>
        <if test="difficultyLevel!=null">and difficulty_Level=#{difficultyLevel}</if>
        <if test="needPostSaleInstall!=null">and need_post_sale_install=#{needPostSaleInstall}</if>
        <if test="needTechSupport!=null">and need_tech_support=#{needTechSupport}</if>

    </select>

    <select id="selectDiMarketingNicheById" parameterType="String" resultMap="DiMarketingNicheResult">
        <include refid="selectDiMarketingNicheVo"/>
        where id = #{id} or niche_no = #{id}
    </select>

    <insert id="insertDiMarketingNiche" parameterType="com.dyd.di.marketing.domain.DiMarketingNiche"
            useGeneratedKeys="true" keyProperty="id">
        insert into di_marketing_niche
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nicheNo != null and nicheNo != ''">niche_no,</if>
            <if test="nicheStatus != null and nicheStatus != ''">niche_status,</if>
            <if test="nicheStage != null and nicheStage != ''">niche_stage,</if>
            <if test="nicheSource != null and nicheSource != ''">niche_source,</if>
            <if test="otherSource != null and otherSource != ''">other_source,</if>
            <if test="clueNo != null and clueNo != ''">clue_no,</if>
            <if test="customerNo != null and customerNo != ''">customer_no,</if>
            <if test="importance != null and importance != ''">importance,</if>
            <if test="orderType != null and orderType != ''">order_type,</if>
            <if test="industryCategories != null and industryCategories != ''">industry_categories,</if>
            <if test="applicationCategories != null and applicationCategories != ''">application_categories,</if>
            <if test="applicationSubcategories != null and applicationSubcategories != ''">application_subcategories,
            </if>
            <if test="fourLevelClassification != null and fourLevelClassification != ''">four_level_classification,</if>
            <if test="fourLevelClassificationOther != null and fourLevelClassificationOther != ''">
                four_level_classification_other,
            </if>

            <if test="industryCategoriesOther != null and industryCategoriesOther != ''">industry_categories_other,</if>
            <if test="applicationCategoriesOther != null and applicationCategoriesOther != ''">
                application_categories_other,
            </if>
            <if test="applicationSubcategoriesOther != null and applicationSubcategoriesOther != ''">
                application_subcategories_other,
            </if>
            <if test="expectedDeliveryDate != null">expected_delivery_date,</if>
            <if test="expectedTransactionAmount != null">expected_transaction_amount,</if>
            <if test="burnerBrand != null and burnerBrand != ''">burner_brand,</if>
            <if test="valveBrand != null and valveBrand != ''">valve_brand,</if>
            <if test="fuel != null and fuel != ''">fuel,</if>
            <if test="onSiteConstruction != null and onSiteConstruction != ''">on_site_construction,</if>
            <if test="scopeOfSupply != null and scopeOfSupply != ''">scope_of_supply,</if>
            <if test="technicalDifficulty != null and technicalDifficulty != ''">technical_difficulty,</if>
            <if test="sharedBy != null and sharedBy != ''">shared_by,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>

            <if test="nicheDetails != null and nicheDetails != ''">niche_details,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            <if test="nicheOwner != null and nicheOwner != ''">niche_owner,</if>
            <if test="nicheOwnerDept != null and nicheOwnerDept != ''">niche_owner_dept,</if>
            <if test="nicheFile != null and nicheFile != ''">niche_file,</if>
            <if test="projectApproved != null and projectApproved != ''">project_approved,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="traceId != null and traceId != ''">trace_id,</if>
            <if test="difficultyLevel!=null">difficulty_level,</if>
            <if test="needPostSaleInstall!=null">need_post_sale_install,</if>
            <if test="needTechSupport!=null">need_tech_support,</if>
            <if test="invalidReason!=null">invalid_reason,</if>
            <if test="determineCause!=null">determine_cause,</if>
            <if test="expectationDeliveryWeek!=null">expectation_delivery_week,</if>
            <if test="mechanicalDifficulty!=null">mechanical_difficulty,</if>
            <if test="electricalDifficulty!=null">electrical_difficulty,</if>
            <if test="productionDifficulty!=null">production_difficulty,</if>
            <if test="afterSalesDifficulty!=null">after_sales_difficulty,</if>
            <if test="nicheRemarks!=null">niche_remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nicheNo != null and nicheNo != ''">#{nicheNo},</if>
            <if test="nicheStatus != null and nicheStatus != ''">#{nicheStatus},</if>
            <if test="nicheStage != null and nicheStage != ''">#{nicheStage},</if>
            <if test="nicheSource != null and nicheSource != ''">#{nicheSource},</if>
            <if test="otherSource != null and otherSource != ''">#{otherSource},</if>
            <if test="clueNo != null and clueNo != ''">#{clueNo},</if>
            <if test="customerNo != null and customerNo != ''">#{customerNo},</if>
            <if test="importance != null and importance != ''">#{importance},</if>
            <if test="orderType != null and orderType != ''">#{orderType},</if>
            <if test="industryCategories != null and industryCategories != ''">#{industryCategories},</if>
            <if test="applicationCategories != null and applicationCategories != ''">#{applicationCategories},</if>
            <if test="applicationSubcategories != null and applicationSubcategories != ''">
                #{applicationSubcategories},
            </if>
            <if test="fourLevelClassification != null and fourLevelClassification != ''">#{fourLevelClassification},
            </if>
            <if test="fourLevelClassificationOther != null and fourLevelClassificationOther != ''">
                #{fourLevelClassificationOther},
            </if>

            <if test="industryCategoriesOther != null and industryCategoriesOther != ''">#{industryCategoriesOther},
            </if>
            <if test="applicationCategoriesOther != null and applicationCategoriesOther != ''">
                #{applicationCategoriesOther},
            </if>
            <if test="applicationSubcategoriesOther != null and applicationSubcategoriesOther != ''">
                #{applicationSubcategoriesOther},
            </if>
            <if test="expectedDeliveryDate != null">#{expectedDeliveryDate},</if>
            <if test="expectedTransactionAmount != null">#{expectedTransactionAmount},</if>
            <if test="burnerBrand != null and burnerBrand != ''">#{burnerBrand},</if>
            <if test="valveBrand != null and valveBrand != ''">#{valveBrand},</if>
            <if test="fuel != null and fuel != ''">#{fuel},</if>
            <if test="onSiteConstruction != null and onSiteConstruction != ''">#{onSiteConstruction},</if>
            <if test="scopeOfSupply != null and scopeOfSupply != ''">#{scopeOfSupply},</if>
            <if test="technicalDifficulty != null and technicalDifficulty != ''">#{technicalDifficulty},</if>
            <if test="sharedBy != null and sharedBy != ''">#{sharedBy},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>

            <if test="nicheDetails != null and nicheDetails != ''">#{nicheDetails},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            <if test="nicheOwner != null and nicheOwner != ''">#{nicheOwner},</if>
            <if test="nicheOwnerDept != null and nicheOwnerDept != ''">#{nicheOwnerDept},</if>
            <if test="nicheFile != null and nicheFile != ''">#{nicheFile},</if>
            <if test="projectApproved != null and projectApproved != ''">#{projectApproved},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="traceId != null and traceId != ''">#{traceId},</if>
            <if test="difficultyLevel!=null">#{difficultyLevel},</if>
            <if test="needPostSaleInstall!=null">#{needPostSaleInstall},</if>
            <if test="needTechSupport!=null">#{needTechSupport},</if>
            <if test="invalidReason!=null">#{invalidReason},</if>
            <if test="determineCause!=null">#{determineCause},</if>
            <if test="expectationDeliveryWeek!=null">#{expectationDeliveryWeek},</if>
            <if test="mechanicalDifficulty!=null">#{mechanicalDifficulty},</if>
            <if test="electricalDifficulty!=null">#{electricalDifficulty},</if>
            <if test="productionDifficulty!=null">#{productionDifficulty},</if>
            <if test="afterSalesDifficulty!=null">#{afterSalesDifficulty},</if>
            <if test="nicheRemarks!=null">#{nicheRemarks},</if>
        </trim>
    </insert>

    <update id="updateDiMarketingNiche" parameterType="com.dyd.di.marketing.domain.DiMarketingNiche">
        update di_marketing_niche
        <trim prefix="SET" suffixOverrides=",">
            <if test="nicheNo != null">niche_no = #{nicheNo},</if>
            <if test="nicheStatus != null">niche_status = #{nicheStatus},</if>
            <if test="nicheStage != null">niche_stage = #{nicheStage},</if>
            <if test="nicheSource != null">niche_source = #{nicheSource},</if>
            <if test="otherSource != null">other_source = #{otherSource},</if>
            <if test="clueNo != null">clue_no = #{clueNo},</if>
            <if test="customerNo != null">customer_no = #{customerNo},</if>
            <if test="importance != null">importance = #{importance},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="industryCategories != null">industry_categories = #{industryCategories},</if>
            <if test="applicationCategories != null">application_categories = #{applicationCategories},</if>
            <if test="applicationSubcategories != null">application_subcategories = #{applicationSubcategories},</if>
            <if test="fourLevelClassification != null">four_level_classification = #{fourLevelClassification},</if>
            <if test="fourLevelClassificationOther != null">four_level_classification_other =
                #{fourLevelClassificationOther},
            </if>

            <if test="industryCategoriesOther != null">industry_categories_other = #{industryCategoriesOther},</if>
            <if test="applicationCategoriesOther != null">application_categories_other =
                #{applicationCategoriesOther},
            </if>
            <if test="applicationSubcategoriesOther != null">application_subcategories_other =
                #{applicationSubcategoriesOther},
            </if>
            <if test="expectedDeliveryDate != null">expected_delivery_date = #{expectedDeliveryDate},</if>
            <if test="expectedTransactionAmount != null">expected_transaction_amount = #{expectedTransactionAmount},
            </if>
            <if test="burnerBrand != null">burner_brand = #{burnerBrand},</if>
            <if test="valveBrand != null">valve_brand = #{valveBrand},</if>
            <if test="fuel != null">fuel = #{fuel},</if>
            <if test="onSiteConstruction != null">on_site_construction = #{onSiteConstruction},</if>
            <if test="scopeOfSupply != null">scope_of_supply = #{scopeOfSupply},</if>
            <if test="technicalDifficulty != null">technical_difficulty = #{technicalDifficulty},</if>
            <if test="sharedBy != null">shared_by = #{sharedBy},</if>
            <if test="projectName != null">project_name = #{projectName},</if>

            <if test="nicheDetails != null">niche_details = #{nicheDetails},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="nicheOwner != null">niche_owner = #{nicheOwner},</if>
            <if test="nicheOwnerDept != null">niche_owner_dept = #{nicheOwnerDept},</if>
            <if test="nicheFile != null">niche_file = #{nicheFile},</if>
            <if test="projectApproved != null">project_approved = #{projectApproved},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="traceId != null">trace_id = #{traceId},</if>
            <if test="difficultyLevel!=null">difficulty_level=#{difficultyLevel},</if>
            <if test="needPostSaleInstall!=null">need_post_sale_install=#{needPostSaleInstall},</if>
            <if test="needTechSupport!=null">need_tech_support=#{needTechSupport},</if>
            <if test="invalidReason!=null">invalid_reason=#{invalidReason},</if>
            <if test="determineCause!=null">determine_cause=#{determineCause},</if>
            <if test="expectationDeliveryWeek!=null">expectation_delivery_week=#{expectationDeliveryWeek},</if>
            <if test="mechanicalDifficulty!=null">mechanical_difficulty=#{mechanicalDifficulty},</if>
            <if test="electricalDifficulty!=null">electrical_difficulty=#{electricalDifficulty},</if>
            <if test="productionDifficulty!=null">production_difficulty=#{productionDifficulty},</if>
            <if test="afterSalesDifficulty!=null">after_sales_difficulty=#{afterSalesDifficulty},</if>
            <if test="nicheRemarks!=null">niche_remarks=#{nicheRemarks},</if>
            <if test="submitProposalDate!=null">submit_proposal_date=#{submitProposalDate},</if>
            <if test="processInstanceId!=null">process_instance_id=#{processInstanceId},</if>
            <if test="approvalStatus!=null">approval_status=#{approvalStatus},</if>
            <if test="oldNicheStatus!=null">old_niche_status=#{oldNicheStatus},</if>
            <if test="approvalBys!=null">approval_bys=#{approvalBys},</if>
            <if test="approvalUserIdBys!=null">approval_user_id_bys=#{approvalUserIdBys},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiMarketingNicheById" parameterType="String">
        delete from di_marketing_niche where id = #{id}
    </delete>

    <delete id="deleteDiMarketingNicheByIds" parameterType="String">
        delete from di_marketing_niche where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDiMarketingNicheByNoCustomerNo" parameterType="String" resultMap="DiMarketingNicheResult">
        <include refid="selectDiMarketingNicheVo"/>
        where customer_no = #{customerNo}
    </select>

    <select id="getDictPersonChargeName" resultType="java.lang.String">
        SELECT
            (select user_name from sys_user where user_name = SUBSTRING_INDEX(d.dict_label, ',', 1)) userName
        FROM
            sys_dict_data d
        WHERE
            dict_type = #{dictType}
          AND dict_value = #{dictValue}
    </select>

</mapper>
