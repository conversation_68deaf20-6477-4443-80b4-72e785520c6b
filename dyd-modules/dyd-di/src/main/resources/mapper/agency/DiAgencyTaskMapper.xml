<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.agency.mapper.DiAgencyTaskMapper">

    <resultMap type="com.dyd.di.agency.entity.DiAgencyTask" id="DiAgencyTaskResult">
        <result property="id" column="id"/>
        <result property="businessKey" column="business_key"/>
        <result property="jumpKey" column="jump_key"/>
        <result property="taskId" column="task_id"/>
        <result property="taskType" column="task_type"/>
        <result property="taskTypeCode" column="task_type_code"/>
        <result property="taskName" column="task_name"/>
        <result property="expectDeliverDate" column="expect_deliver_date"/>
        <result property="taskStateDesc" column="task_state_desc"/>
        <result property="liabilityBy" column="liability_by"/>
        <result property="isRead" column="is_read"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="traceId" column="trace_id"/>
    </resultMap>

    <sql id="selectDiAgencyTaskVo">
        select * from di_agency_task
    </sql>

    <resultMap type="com.dyd.di.agency.domain.vo.AgencyTaskListVO" id="AgencyTaskListVOResult">
        <result property="id" column="id"/>
        <result property="businessKey" column="business_key"/>
        <result property="jumpKey" column="jump_key"/>
        <result property="taskId" column="task_id"/>
        <result property="taskType" column="task_type"/>
        <result property="taskTypeCode" column="task_type_code"/>
        <result property="taskName" column="task_name"/>
        <result property="expectDeliverDate" column="expect_deliver_date"/>
        <result property="taskStateDesc" column="task_state_desc"/>
        <result property="liabilityBy" column="liability_by"/>
        <result property="isRead" column="is_read"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="traceId" column="trace_id"/>
    </resultMap>

    <select id="findAgencyTaskList" resultMap="AgencyTaskListVOResult">
        <include refid="selectDiAgencyTaskVo"/>
        where is_deleted = '0'
        <if test="liabilityBy != null and liabilityBy != ''">
            and liability_by = #{liabilityBy}
        </if>
        <if test="taskTypeCode != null and taskTypeCode != ''">
            and task_type_code = #{taskTypeCode}
        </if>
        order by create_time desc
    </select>

</mapper>