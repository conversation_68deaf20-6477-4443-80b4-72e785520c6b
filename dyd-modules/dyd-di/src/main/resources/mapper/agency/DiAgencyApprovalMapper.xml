<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.agency.mapper.DiAgencyApprovalMapper">

    <resultMap type="com.dyd.di.agency.entity.DiAgencyApproval" id="DiAgencyApprovalResult">
        <result property="id" column="id"/>
        <result property="businessKey" column="business_key"/>
        <result property="jumpKey" column="jump_key"/>
        <result property="approvalType" column="approval_type"/>
        <result property="approvalTypeCode" column="approval_type_code"/>
        <result property="launchBy" column="launch_by"/>
        <result property="liabilityBy" column="liability_by"/>
        <result property="liabilityRoleId" column="liability_role_id"/>
        <result property="isRead" column="is_read"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="camundaTaskId" column="camunda_task_id"/>
        <result property="camundaProcessInstanceId" column="camunda_process_instance_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="traceId" column="trace_id"/>
    </resultMap>

    <sql id="selectDiAgencyApprovalVo">
        select * from di_agency_approval
    </sql>

    <resultMap type="com.dyd.di.agency.domain.vo.AgencyApprovalListVO" id="AgencyApprovalListVOResult">
        <result property="id" column="id"/>
        <result property="businessKey" column="business_key"/>
        <result property="jumpKey" column="jump_key"/>
        <result property="approvalType" column="approval_type"/>
        <result property="approvalTypeCode" column="approval_type_code"/>
        <result property="launchBy" column="launch_by"/>
        <result property="liabilityBy" column="liability_by"/>
        <result property="liabilityRoleId" column="liability_role_id"/>
        <result property="isRead" column="is_read"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="camundaTaskId" column="camunda_task_id"/>
        <result property="camundaProcessInstanceId" column="camunda_process_instance_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="traceId" column="trace_id"/>
    </resultMap>

    <select id="findAgencyApprovalList" resultMap="AgencyApprovalListVOResult">
        <include refid="selectDiAgencyApprovalVo"/>
        where is_deleted = '0'
        <if test="liabilityBy != null and liabilityBy != ''">
            and liability_by = #{liabilityBy}
        </if>
        <if test="taskTypeCode != null and taskTypeCode != ''">
            and task_type_code = #{taskTypeCode}
        </if>
        order by create_time desc
    </select>

</mapper>