package com.dyd.di.pre.domain.response;

import com.dyd.di.common.DiHelper;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

import static com.dyd.common.core.utils.MoneyUtils.sum;

/**
 * 费用及各交付周期
 */
@Data
public class FeeItem {


    /**
     * 有效期止
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    /**
     * 费用合计
     */
    private BigDecimal feeTotal;
    private BigDecimal bomDesignDay;

    public BigDecimal getFeeTotal() {
        feeTotal = sum(techSupportFee, mechineDesignFee, electricDesignFee, materialFee, guideProductionCosts,
                temporaryMaterialCosts, temporaryProductionCosts, null, deliveryDebugFee, packageFee, riskFee);
        return feeTotal;
    }

    /**
     * 技术支持费用
     */
    private BigDecimal techSupportFee;

    /**
     * 机械设计费用
     */
    private BigDecimal mechineDesignFee;

    /**
     * 电气设计费用
     */
    private BigDecimal electricDesignFee;

    /**
     * 物料费用
     */
    private BigDecimal materialFee;


    /**
     * 物料制作费
     */
    private BigDecimal guideProductionCosts;

    /**
     * 临时物料费
     */
    private BigDecimal temporaryMaterialCosts;

    /**
     * 临时制作费
     */
    private BigDecimal temporaryProductionCosts;

    /**
     * 生产费用
     */
    //确认删除
    //private BigDecimal productFee;

    /**
     * 交付调试费用
     */
    private BigDecimal deliveryDebugFee;

    /**
     * 包装费用
     */
    private BigDecimal packageFee;

    /**
     * 风险费用
     */
    private BigDecimal riskFee;

//    /**
//     * 现场安装杂费
//     */
//    private BigDecimal sceneInstallFee;

    /**
     * 杂项小计
     */
    private BigDecimal otherFee;

    public BigDecimal getOtherFee() {
        otherFee = (Objects.nonNull(this.getDeliveryDebugFee()) ? this.getDeliveryDebugFee() : BigDecimal.ZERO)
                .add(Objects.nonNull(this.getPackageFee()) ? this.getPackageFee() : BigDecimal.ZERO)
                .add(Objects.nonNull(this.getRiskFee()) ? this.getRiskFee() : BigDecimal.ZERO)
//                .add(Objects.nonNull(this.getSceneInstallFee()) ? this.getSceneInstallFee() : BigDecimal.ZERO)
//                .add(Objects.nonNull(this.getGuideOtherCosts()) ? this.getGuideOtherCosts() : BigDecimal.ZERO)
        ;
        return otherFee;
    }

//    /**
//     * 其他费用
//     */
//    private BigDecimal guideOtherCosts;

    /**
     * 技术支持周期(天)
     */
    private BigDecimal techSupportDay;

    /**
     * 机械方案周期(天)
     */
    private BigDecimal mechineDay;


    /**
     * 电气方案周期(天)
     */
    private BigDecimal electricDay;

    /**
     * 供应货期(天)
     */
    private BigDecimal supplyDay;

    /**
     * 生产周期(天)
     */
    private BigDecimal productDay;

    /**
     * 指导工期
     */
    private BigDecimal guideDuration;

    /**
     * 交付调试周期(天)
     */
    private BigDecimal deliveryDebugDay;

    /**
     * 数量
     */
    private Integer quantity;

    public BigDecimal computeGuideDurationForProject() {
        return DiHelper.computeGuideDurationForProject(getTechSupportDay(),
                getElectricDay(),
                getMechineDay(),
                getSupplyDay(),
                getProductDay(),
                getDeliveryDebugDay(),
                getBomDesignDay());
    }

}