package com.dyd.di.matrix.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 星阵文件分类
 * @TableName di_matrix_file_type
 */
@TableName(value ="di_matrix_file_type")
@Data
public class DiMatrixFileType implements Serializable {
    /**
     *
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 所属业务：
     * /**
     * 所属业务：
     * 图纸资料 :0
     * 产品素材: 1
     * 外形尺寸: 2
     * 操作说明:3
     * 工作范围：4
     * 故障手册：5
     * 版图介绍：6
     * 业务照片：7
     * 烧嘴介绍：8
     *
     */
    private String belonging;

    /**
     * 关联ID
     */
    private Long belongingId;

    /**
     * 分类开关（0关闭 1打开）
     */
    private Integer typeSwitch;

    /**
     * 2删除 0 正常
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 链路id
     */
    private String traceId;

    /**
     * 文件对象
     */
    @TableField(exist = false)
    private List<DiMatrixFileExtend> fileVo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
