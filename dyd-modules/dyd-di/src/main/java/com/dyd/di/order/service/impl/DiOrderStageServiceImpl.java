package com.dyd.di.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.eventbus.utils.EventBusUtils;
import com.dyd.di.order.domain.*;
import com.dyd.di.order.enums.OrderScheduleStatusEnum;
import com.dyd.di.order.enums.OrderStageEnum;
import com.dyd.di.order.event.OrderStageChangedEvent;
import com.dyd.di.order.mapper.*;
import com.dyd.di.order.service.DiOrderStageService;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.entity.DiPreSaleQuoteDetail;
import com.dyd.di.pre.mapper.DiPreSaleMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteDetailMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DiOrderStageServiceImpl implements DiOrderStageService {

    @Resource
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Resource
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Resource
    private DiPreSaleMapper diPreSaleMapper;

    @Resource
    private DiOrderBillPeriodMapper diOrderBillPeriodMapper;

    @Resource
    private DiOrderMapper diOrderMapper;

    @Resource
    private RemoteDbcService remoteDbcService;

    @Resource
    private DiOrderMachineDesignMapper diOrderMachineDesignMapper;

    @Resource
    private DiOrderMaterielPlanMapper diOrderMaterielPlanMapper;

    @Resource
    private DiOrderProduceMapper diOrderProduceMapper;

    @Resource
    private DiOrderQualityMapper diOrderQualityMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private DiOrderSupportCheckMapper diOrderSupportCheckMapper;

    @Override
    public String updateOrderStageCheck(DiOrder diOrder, OrderStageEnum orderStageEnum) {
        List<DiPreSaleQuote> preSaleQuoteList = diPreSaleQuoteMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, diOrder.getPreSaleQuoteNo()));
        if (CollectionUtil.isEmpty(preSaleQuoteList)) {
            return "销售报价单不存在";
        }
        if (diOrder.getIsAdvanceExecution().equals("2")) {
            log.info("DiOrderStageServiceImpl---updateOrderStageCheck()---提前执行");
            return null;
        }
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>()
                .eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, preSaleQuoteList.get(0).getId()));
        if (CollectionUtil.isEmpty(preSaleQuoteDetailList)) {
            return "销售报价单明细不存在";
        }
        List<Long> preSaleIdList = preSaleQuoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).distinct().toList();
        List<DiPreSale> preSaleList = diPreSaleMapper.selectBatchIds(preSaleIdList);
        if (CollectionUtil.isEmpty(preSaleList)) {
            return "产品方案不存在";
        }
        List<DiOrderBillPeriod> orderBillPeriodList = diOrderBillPeriodMapper.selectList(new LambdaQueryWrapper<DiOrderBillPeriod>().eq(DiOrderBillPeriod::getOrderNo, diOrder.getOrderNo()));
        if (CollectionUtil.isEmpty(orderBillPeriodList)) {
            return "账期为空";
        }
        if (!"C04".equals(preSaleQuoteList.get(0).getCountryCode())
        ) {
            //国外
            if ((OrderStageEnum.FIVE.getCode().equals(orderStageEnum.getCode()) || OrderStageEnum.SIX.getCode().equals(orderStageEnum.getCode()))) {
                return checkRealPayDate(orderBillPeriodList, diOrder, orderStageEnum);
            } else {
                return null;
            }
        } else {
            List<Integer> preSaleTypeList = preSaleList.stream().map(DiPreSale::getPreSaleType).distinct().toList();
            if (preSaleTypeList.contains(4)) {
                if (OrderStageEnum.FOUR.getCode().equals(orderStageEnum.getCode()) || OrderStageEnum.FIVE.getCode().equals(orderStageEnum.getCode()) || OrderStageEnum.SIX.getCode().equals(orderStageEnum.getCode())) {
                    return checkRealPayDate(orderBillPeriodList, diOrder, orderStageEnum);
                }
                return null;
            }
            if ((preSaleTypeList.contains(1) || preSaleTypeList.contains(2))) {
                if (OrderStageEnum.FIVE.getCode().equals(orderStageEnum.getCode()) || OrderStageEnum.SIX.getCode().equals(orderStageEnum.getCode())) {
                    return checkRealPayDate(orderBillPeriodList, diOrder, orderStageEnum);
                }
                return null;
            }
            if (preSaleTypeList.contains(3)) {
                if (OrderStageEnum.FOUR.getCode().equals(orderStageEnum.getCode())) {
                    return checkRealPayDate(orderBillPeriodList, diOrder, orderStageEnum);
                }
                return null;
            }
        }
        return null;
    }

    private String checkRealPayDate(List<DiOrderBillPeriod> orderBillPeriodList, DiOrder diOrder, OrderStageEnum orderStageEnum) {
        boolean isUpdateOrderStage = orderBillPeriodList.stream().anyMatch(orderBillPeriod -> orderBillPeriod.getRealPayDate() != null);
        if (!isUpdateOrderStage) {
            String projectManagerUserIdName = diOrder.getProjectManagerUserId();
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Collections.singletonList(projectManagerUserIdName)).build());
            if (userListResult.isSuccess()) {
                Map<String, String> userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                projectManagerUserIdName = userNameMap.get(diOrder.getProjectManagerUserId());
            }
            return String.format("该项目未绑定预付款，%s任务暂禁止开始，请联系项目经理：%s", orderStageEnum.getTabName(), projectManagerUserIdName);
        }
        return null;
    }

    @Override
    public void updateOrderStage(DiOrder diOrder, OrderStageEnum orderStageEnum) {
        //订单必须排期,并且修改阶段必须大于当前阶段
        if (OrderScheduleStatusEnum.COMPLETED.getCode().equals(diOrder.getScheduleStatus()) && orderStageEnum.getCode() > diOrder.getOrderStage()) {
            LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
            updateWrapper.eq(DiOrder::getId, diOrder.getId());
            updateWrapper.set(DiOrder::getUpdateBy, SecurityUtils.getUsername());
            updateWrapper.set(DiOrder::getOrderStage, orderStageEnum.getCode());
            updateWrapper.update();
//            OrderStageChangedEvent event = new OrderStageChangedEvent();
//            event.setOrderId(diOrder.getId());
//            event.setOrderNo(diOrder.getOrderNo());
//            event.setOrderStage(orderStageEnum);
//            EventBusUtils.publishEvent(event);
        }
    }

//    @Override
//    public OrderStageEnum schedulingOrderStageEnum(Long preSaleId, Integer orderStage) {
//        //查询技术支持复核
//        DiOrderSupportCheck supportCheck = getSupportCheck(preSaleId);
//        if (Objects.nonNull(supportCheck) && 1 == supportCheck.getScheduleStatus()) {
//            return OrderStageEnum.ONE;
//        }
//        //获取质检计划任务
//        DiOrderQuality quality = getQuality(preSaleId);
//        if (Objects.nonNull(quality) && 1 == quality.getScheduleStatus()) {
//            return OrderStageEnum.SIX;
//        }
//        //获取生产计划任务
//        DiOrderProduce produce = getProduce(preSaleId);
//        if (Objects.nonNull(produce) && 1 == produce.getScheduleStatus()) {
//            return OrderStageEnum.FIVE;
//        }
//        //获取物料计划任务
//        DiOrderMaterielPlan materielPlan = getMaterielPlan(preSaleId);
//        if (Objects.nonNull(materielPlan) && 1 == materielPlan.getScheduleStatus()) {
//            return OrderStageEnum.FOUR;
//        }
//        //获取机械设计任务
//        DiOrderMachineDesign machineDesign = getMachineDesign(preSaleId);
//        if (Objects.nonNull(machineDesign) && null == machineDesign.getBomRealEndTime()) {
//            return OrderStageEnum.TWO;
//        }
//        return OrderStageEnum.SEVEN;
//    }

//    @Override
//    public OrderStageEnum getOrderStageEnum(Long preSaleId, Integer orderStage) {
//        //查询技术支持复核
//        DiOrderSupportCheck supportCheck = getSupportCheck(preSaleId);
//        //获取质检计划任务
//        DiOrderQuality quality = getQuality(preSaleId);
//        //获取生产计划任务
//        DiOrderProduce produce = getProduce(preSaleId);
//        //获取物料计划任务
//        DiOrderMaterielPlan materielPlan = getMaterielPlan(preSaleId);
//        //获取机械设计任务
//        DiOrderMachineDesign machineDesign = getMachineDesign(preSaleId);
//        if (OrderStageEnum.ONE.getCode().equals(orderStage)) {
//            if (Objects.nonNull(supportCheck) && 1 == supportCheck.getScheduleStatus()) {
//                return OrderStageEnum.ONE;
//            }
//            if (Objects.nonNull(quality) && 1 == quality.getScheduleStatus()) {
//                return OrderStageEnum.SIX;
//            }
//            if (Objects.nonNull(produce) && 1 == produce.getScheduleStatus()) {
//                return OrderStageEnum.FIVE;
//            }
//            if (Objects.nonNull(materielPlan) && 1 == materielPlan.getScheduleStatus()) {
//                return OrderStageEnum.FOUR;
//            }
//            if (Objects.nonNull(machineDesign) && null == machineDesign.getBomRealEndTime()) {
//                return OrderStageEnum.TWO;
//            }
//        }
//        if (OrderStageEnum.TWO.getCode().equals(orderStage)) {
//            if (Objects.nonNull(quality) && 1 == quality.getScheduleStatus()) {
//                return OrderStageEnum.SIX;
//            }
//            if (Objects.nonNull(produce) && 1 == produce.getScheduleStatus()) {
//                return OrderStageEnum.FIVE;
//            }
//            if (Objects.nonNull(materielPlan) && 1 == materielPlan.getScheduleStatus()) {
//                return OrderStageEnum.FOUR;
//            }
//        }
//        if (OrderStageEnum.FOUR.getCode().equals(orderStage)) {
//            if (Objects.nonNull(quality) && 1 == quality.getScheduleStatus()) {
//                return OrderStageEnum.SIX;
//            }
//            if (Objects.nonNull(produce) && 1 == produce.getScheduleStatus()) {
//                return OrderStageEnum.FIVE;
//            }
//        }
//        if (OrderStageEnum.FIVE.getCode().equals(orderStage)) {
//            if (Objects.nonNull(quality) && 1 == quality.getScheduleStatus()) {
//                return OrderStageEnum.SIX;
//            }
//        }
//        return OrderStageEnum.SEVEN;
//    }
//
//    private DiOrderSupportCheck getSupportCheck(Long preSaleId) {
//        return diOrderSupportCheckMapper.selectOne(new LambdaQueryWrapper<DiOrderSupportCheck>()
//                .eq(DiOrderSupportCheck::getPreSaleId, preSaleId)
//                .eq(DiOrderSupportCheck::getDelFlag, 0));
//    }
//
//    private DiOrderMachineDesign getMachineDesign(Long preSaleId) {
//        return diOrderMachineDesignMapper.selectOne(new LambdaQueryWrapper<DiOrderMachineDesign>()
//                .eq(DiOrderMachineDesign::getPreSaleId, preSaleId)
//                .eq(DiOrderMachineDesign::getDelFlag, 0));
//    }
//
//    private DiOrderMaterielPlan getMaterielPlan(Long preSaleId) {
//        return diOrderMaterielPlanMapper.selectOne(new LambdaQueryWrapper<DiOrderMaterielPlan>()
//                .eq(DiOrderMaterielPlan::getPreSaleId, preSaleId)
//                .eq(DiOrderMaterielPlan::getDelFlag, 0));
//    }
//
//    private DiOrderProduce getProduce(Long preSaleId) {
//        return diOrderProduceMapper.selectOne(new LambdaQueryWrapper<DiOrderProduce>()
//                .eq(DiOrderProduce::getPreSaleId, preSaleId)
//                .eq(DiOrderProduce::getDelFlag, 0));
//    }
//
//    private DiOrderQuality getQuality(Long preSaleId) {
//        return diOrderQualityMapper.selectOne(new LambdaQueryWrapper<DiOrderQuality>()
//                .eq(DiOrderQuality::getPreSaleId, preSaleId)
//                .eq(DiOrderQuality::getDelFlag, 0));
//    }
}
