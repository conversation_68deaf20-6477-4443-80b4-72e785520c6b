package com.dyd.di.agency.controller;

import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.ChangeLiabilityByDTO;
import com.dyd.di.agency.domain.dto.CommonDTO;
import com.dyd.di.agency.domain.dto.FindAgencyTaskListDTO;
import com.dyd.di.agency.domain.vo.AgencyTaskListVO;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.storage.redisson.RedissonLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 代办任务
 */
@RestController
@RequestMapping("/agencyTask")
public class DiAgencyTaskController {

    @Resource
    private DiAgencyTaskService diAgencyTaskService;

    @Resource
    private RedissonLock redissonLock;

    /**
     * 查询代办任务列表(无分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @PostMapping("/findAgencyTaskList")
    public R<List<AgencyTaskListVO>> findAgencyTaskList(@RequestBody FindAgencyTaskListDTO dto) {
        return R.ok(diAgencyTaskService.findAgencyTaskList(dto));
    }

    /**
     * 查询代办任务列表(分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @PostMapping("/findAgencyTaskListPage")
    public R<PageWrapper<List<AgencyTaskListVO>>> findAgencyTaskListPage(@RequestBody FindAgencyTaskListDTO dto) {
        return R.ok(diAgencyTaskService.findAgencyTaskListPage(dto));
    }

    /**
     * 更新是否已读
     *
     * @param dto 更新参数
     */
    @PostMapping("/updateIsReadById")
    public R<Void> updateIsReadById(@Validated @RequestBody CommonDTO dto) {
        diAgencyTaskService.updateIsReadById(dto);
        return R.ok();
    }

    /**
     * 删除代办任务
     *
     * @param dto 删除参数
     */
    @PostMapping("/deleteAgencyTask")
    public R<Void> deleteAgencyTask(@Validated @RequestBody CommonDTO dto) {
        diAgencyTaskService.deleteAgencyTask(dto);
        return R.ok();
    }

    /**
     * 变更代办任务责任人
     *
     * @param dto 变更参数
     */
    @PostMapping("/changeLiabilityBy")
    public R<String> changeLiabilityBy(@Validated @RequestBody ChangeLiabilityByDTO dto) {
        //加锁
        String keyLock = AgencyConstants.UPDATE_AGENCY_TASK_LOCK + AgencyConstants.DELIMITER_BAR + dto.getId();
        return redissonLock.tryLocK(keyLock, () -> {
            String result = diAgencyTaskService.changeLiabilityBy(dto);
            if (StringUtils.isNotBlank(result)) {
                return R.fail(result);
            }
            return R.ok();
        }, () -> {
            throw new ServiceException("更在变更代办任务责任人，请刷新界面重新查看");
        }, 3);
    }

}
