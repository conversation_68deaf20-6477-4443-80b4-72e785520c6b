package com.dyd.di.matrix.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 版图元数据保存请求
 */
@Data
public class MatrixTerritoryMetaSaveRequest {

    /**
     *版图传入集合
     */
    private List<Meta> matrixMetas;

    @Data
    public static class Meta{

        /**
         * 类型：1:领域、2:行业，3:应用,4:对象，5工艺 6:配件 7:配件品类 8:组件品类
         */
        private Integer metaType;

        /**
         * id，为空标识新增
         */
        private Long id;

        /**
         * 版图名称
         */
        private String territoryName;

        /**
         * 配件品类所属配件id
         */
        private Long partsParentId;

        /**
         * 配件品类所属配件名
         */
        private String partsParentName;

        /**
         * 是否删除 true:是   false:否
         */
        private Boolean isDelete;
    }
}
