package com.dyd.di.pre.domain.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class UpdateManifestQuantityRequest {

    /**
     * 方案ID
     */
    private Long preSaleId;

    /**
     * 物料清单
     */
    private List<Manifest> manifestList;

    /**
     * 物料清单
     */
    @Data
    public static class Manifest {
        /**
         * 物料ID
         */
        private Long materielId;
        /**
         * 物料数量
         */
        private Integer useNum;

        /**
         * 物料数量
         */
        private Integer quantity;
        /**
         * 报价总额
         */
        private BigDecimal quoteTotal;
    }

}
