package com.dyd.di.marketing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.api.model.DiMarketingNicheListResponse;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.domain.DiMarketingNicheDemand;
import com.dyd.di.marketing.domain.DictPersonCharge;
import com.dyd.di.marketing.domain.DictPersonChargeVo;
import com.dyd.jdy.bean.stockUp.StockUpDataResponse;
import com.dyd.system.api.vo.BpmTaskApproveReqVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 商机Service接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiMarketingNicheService extends IService<DiMarketingNiche> {
    /**
     * 查询商机
     *
     * @param id 商机主键
     * @return 商机
     */
    public DiMarketingNiche selectDiMarketingNicheById(String id, Boolean isAuth);

    /**
     * 批量查询商机
     *
     * @param ids
     * @return
     */
    List<DiMarketingNiche> selectDiMarketingNicheListByIds(List<Integer> ids);

    /**
     * 查询商机列表
     *
     * @param diMarketingNiche 商机
     * @return 商机集合
     */
    public List<DiMarketingNiche> selectDiMarketingNicheList(DiMarketingNiche diMarketingNiche);

    /**
     * 新增商机
     *
     * @param diMarketingNiche 商机
     * @return 结果
     */
    public Map<String, String> insertDiMarketingNiche(DiMarketingNiche diMarketingNiche);

    /**
     * 修改商机
     *
     * @param diMarketingNiche 商机
     * @return 结果
     */
    public int updateDiMarketingNiche(DiMarketingNiche diMarketingNiche);

    /**
     * 批量删除商机
     *
     * @param ids 需要删除的商机主键集合
     * @return 结果
     */
    public int deleteDiMarketingNicheByIds(String[] ids);

    PageWrapper<List<DiMarketingNicheListResponse>> getNichePage(Integer pageNum, Integer pageSize);

    /**
     * 获取字典值的负责人
     *
     * @param dictPersonCharge
     * @return
     */
    DictPersonChargeVo getDictPersonChargeName(DictPersonCharge dictPersonCharge);

    /**
     * 删除商机信息
     *
     * @param id 商机主键
     * @return 结果
     */
    public int deleteDiMarketingNicheById(String id);

    /**
     * 商机查详情
     *
     * @param nicheNo
     * @return
     */
    DiMarketingNiche selectDiMarketingNicheByNo(String nicheNo);

    List<DiMarketingNiche> selectDiMarketingNicheByNoCustomerNo(String customerNo);

    /**
     * 修改商机状态
     *
     * @param nicheNo 商机号
     * @param status  状态
     */
    void updateNicheStatus(String nicheNo, String status);

    /**
     * 根据id获取编号获取商机
     *
     * @param noOrId 商机ID或编号
     * @return 商机信息
     */
    DiMarketingNiche getMarketingNicheByNoRoId(String noOrId);

    /**
     * 修改状态发送钉钉消息
     *
     * @param diMarketingNiche 商机信息
     * @param content          发送内容
     */
    void updateNicheStatusSendDingTalk(DiMarketingNiche diMarketingNiche, String content);

    /**
     * code转id
     *
     * @param code
     * @return
     */
    String convertCode2Id(String code);

    /**
     * 获取 01无合同提前备物料&成品
     *
     * @return
     */
    List<? extends Object> getStockUpList(String dataId);

    /**
     * 根据商机编号获取商机需求
     *
     * @param nicheCode
     * @return
     */
    DiMarketingNicheDemand selectNicheDemandByNo(String nicheCode);

    void saveAgencyTaskSendMessage(List<String> byList, DiMarketingNiche diMarketingNiche, Integer type);

    default DiMarketingNiche queryByCode(String nicheCode) {
        List<DiMarketingNiche> list = this.lambdaQuery().eq(DiMarketingNiche::getNicheNo, nicheCode).eq(DiMarketingNiche::getDelFlag, 0).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    default DiMarketingNiche queryByNicheId(Integer nicheId){
        List<DiMarketingNiche> list = this.lambdaQuery().eq(DiMarketingNiche::getId, nicheId.toString()).eq(DiMarketingNiche::getDelFlag, 0).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 退回需求阶段
     *
     * @param nicheId
     */
    void rollbackRequirementStage(Long nicheId);

    /**
     * 回滚到方案阶段
     *
     * @param nicheId
     */
    void rollback2solutionStage(Long nicheId);

    void dataMaintenance();

}
