package com.dyd.di.oss;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FileAndBusinessVo {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件key
     */
    private String fileKey;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 预览url
     */
    private String fileUrl;

    /**
     * 图片 1是图片  0不是
     */
    private Integer isPic;

    /**
     * 创建者名字
     */
    private String createByName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
