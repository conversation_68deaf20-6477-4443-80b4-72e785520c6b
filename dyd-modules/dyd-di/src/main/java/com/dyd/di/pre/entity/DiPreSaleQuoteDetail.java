package com.dyd.di.pre.entity;


import java.io.Serializable;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 销售报价单明细
 *
 * @TableName di_pre_sale_quote_detail
 */
@Data
public class DiPreSaleQuoteDetail implements Serializable {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 销售报价单编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long preSaleId;

    /**
     * 产品方案编码
     */
    private String preSaleCode;

    /**
     * 销售报价id
     */
    private Integer preSaleQuoteId;
    /**
     * 售价报价
     */
    private BigDecimal saleQuote;

    /**
     * 单套理论成本
     */
    private BigDecimal costFee;

    /**
     * 交期报价(天)
     */
    private BigDecimal deliveryQuote;

    /**
     * 毛利额
     */
    private BigDecimal grossProfit;

    /**
     * 毛利率
     */
    private BigDecimal grossMargin;

    /**
     * 单套理论服务费->单套售后施工费
     */
    private BigDecimal systemServiceFee;

    /**
     * 单套服务理论周期->单套售后施工周期（天）
     */
    private BigDecimal systemServiceCycle;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String province;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市
     */
    private String city;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区
     */
    private String area;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 施工费报价
     */
    private BigDecimal saleServiceQuote;

    /**
     * 施工周期报价（人/天）
     */
    private BigDecimal saleServiceCycle;
}
