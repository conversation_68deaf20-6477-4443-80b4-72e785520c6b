package com.dyd.di.marketing.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.marketing.domain.DiTerritoryMapping;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import com.dyd.di.marketing.domain.vo.MarketingTerritoryMappingVO;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper;
import com.dyd.di.marketing.service.MarketingTerritoryMappingService;
import com.dyd.di.matrix.dto.MatrixProductDetailDTO;
import com.dyd.di.matrix.pojo.response.QueryProductByTerritoryResponse;
import com.dyd.di.matrix.service.DiMatrixProductService;
import com.dyd.di.matrix.service.DiMatrixTerritoryService;
import com.dyd.di.matrix.vo.OperateMatrixProductVO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Service
public class MarketingTerritoryMappingServiceImpl implements MarketingTerritoryMappingService {

    @Autowired
    private DiTerritoryMappingMapper territoryMappingMapper;
    @Autowired
    private DiTerritoryProductMappingMapper diTerritoryProductMappingMapper;
    @Autowired
    private DiMatrixProductService diMatrixProductService;
    @Autowired
    private DiMatrixTerritoryService diMatrixTerritoryService;

    /**
     * 添加营销版图映射关系
     * @param reqVO 请求参数
     */
    @Override
    public void add(MarketingTerritoryMappingVO reqVO) {
        // 1. 处理业务版图映射
        handleTerritoryMapping(reqVO);

        // 2. 处理产品映射关系
        if (reqVO.getProductId() != null) {
            // 单个产品处理
            handleSingleProductMapping(reqVO);
        } else {
            // 批量产品处理
            handleBatchProductMapping(reqVO);
        }
    }

    /**
     * 处理业务版图映射
     * @param reqVO 请求参数
     */
    private void handleTerritoryMapping(MarketingTerritoryMappingVO reqVO) {
        Long count = territoryMappingMapper.selectCount(Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryMapping::getTerritoryId, reqVO.getTerritoryId())
        );

        if (count <= 0) {
            DiTerritoryMapping territoryMapping = DiTerritoryMapping.builder()
                    .refId(reqVO.getRefId())
                    .territoryId(reqVO.getTerritoryId())
                    .territoryJson(reqVO.getTerritoryJson())
                    .territoryInfo(JSON.toJSONString(diMatrixTerritoryService.getMatrixTerritoryInfo(reqVO.getTerritoryId())))
                    .build();
            territoryMappingMapper.insert(territoryMapping);
        }
    }

    /**
     * 处理单个产品映射
     * @param reqVO 请求参数
     */
    private void handleSingleProductMapping(MarketingTerritoryMappingVO reqVO) {
        // 删除已存在的相同映射
        diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                .eq(DiTerritoryProductMapping::getProductId, reqVO.getProductId())
        );

        // 插入新的映射关系
        DiTerritoryProductMapping territoryProductMapping = getTerritoryProductMapping(reqVO);
        diTerritoryProductMappingMapper.insert(territoryProductMapping);
    }

    /**
     * 处理批量产品映射
     * @param reqVO 请求参数
     */
    private void handleBatchProductMapping(MarketingTerritoryMappingVO reqVO) {
        QueryProductByTerritoryResponse res = JSON.parseObject(reqVO.getProductJson(), QueryProductByTerritoryResponse.class);
        if (res == null || CollectionUtils.isEmpty(res.getData())) {
            return;
        }

        // 删除已存在的相同refId和territoryId的所有映射
        diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
        );

        // 为每个产品创建独立的映射对象，避免共享状态导致的数据重复问题
        List<DiTerritoryProductMapping> mappingList = res.getData().stream()
                .map(productData -> createProductMapping(reqVO, res, productData))
                .toList();

        // 逐个插入映射关系，避免批量插入时的唯一约束冲突
        insertProductMappingsWithRetry(mappingList);
    }

    /**
     * 安全地插入产品映射，处理唯一约束冲突
     * @param mappingList 映射列表
     */
    private void insertProductMappingsWithRetry(List<DiTerritoryProductMapping> mappingList) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }

        for (DiTerritoryProductMapping mapping : mappingList) {
            try {
                // 先检查是否已存在相同的记录
                Long existingCount = diTerritoryProductMappingMapper.selectCount(
                    Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                        .eq(DiTerritoryProductMapping::getRefId, mapping.getRefId())
                        .eq(DiTerritoryProductMapping::getTerritoryId, mapping.getTerritoryId())
                        .eq(DiTerritoryProductMapping::getProductId, mapping.getProductId())
                );

                if (existingCount == 0) {
                    diTerritoryProductMappingMapper.insert(mapping);
                }
            } catch (Exception e) {
                // 记录日志但不中断整个流程
                log.warn("插入产品映射失败，refId: {}, territoryId: {}, productId: {}, 错误: {}",
                    mapping.getRefId(), mapping.getTerritoryId(), mapping.getProductId(), e.getMessage());
            }
        }
    }

    /**
     * 为单个产品创建映射对象
     * @param originalVO 原始VO对象
     * @param res 产品响应数据
     * @param productData 单个产品数据
     * @return 产品映射对象
     */
    private DiTerritoryProductMapping createProductMapping(MarketingTerritoryMappingVO originalVO,
                                                          QueryProductByTerritoryResponse res,
                                                          Map<String, String> productData) {
        // 创建单个产品的响应对象
        QueryProductByTerritoryResponse singleProductResponse = QueryProductByTerritoryResponse.builder()
                .titleMap(res.getTitleMap())
                .data(Lists.newArrayList(productData))
                .total(1L)
                .build();

        // 创建独立的VO对象，避免修改原始对象
        MarketingTerritoryMappingVO productVO = new MarketingTerritoryMappingVO();
        productVO.setRefId(originalVO.getRefId());
        productVO.setTerritoryId(originalVO.getTerritoryId());
        productVO.setTerritoryJson(originalVO.getTerritoryJson());
        productVO.setProductId(Long.parseLong(productData.get("id")));
        productVO.setProductJson(JSON.toJSONString(singleProductResponse));

        return getTerritoryProductMapping(productVO);
    }

    /**
     * 根据VO创建产品映射对象
     * @param reqVO 请求参数
     * @return 产品映射对象
     */
    private DiTerritoryProductMapping getTerritoryProductMapping(MarketingTerritoryMappingVO reqVO) {
        // 获取产品详情，注意处理返回类型和空值
        R<MatrixProductDetailDTO> productDetailResult = diMatrixProductService.getMatrixProductDetail(
                OperateMatrixProductVO.builder().id(reqVO.getProductId()).build());

        MatrixProductDetailDTO data = null;
        if (productDetailResult != null && productDetailResult.isSuccess() && productDetailResult.getData() != null) {
            data = productDetailResult.getData();
        }

        return DiTerritoryProductMapping.builder()
                .refId(reqVO.getRefId())
                .territoryId(reqVO.getTerritoryId())
                .productId(reqVO.getProductId())
                .productJson(reqVO.getProductJson())
                .productInfo(data != null ? JSON.toJSONString(data) : null)
                .build();
    }

    @Override
    public void del(MarketingTerritoryMappingVO reqVO) {
        if (Objects.nonNull(reqVO.getProductId())) {
            diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                    .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                    .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                    .eq(DiTerritoryProductMapping::getProductId, reqVO.getProductId())
            );
        } else if (StringUtils.hasText(reqVO.getProductIds())) {
            List<Long> ids = Arrays.stream(reqVO.getProductIds().split(",")).map(Long::parseLong).toList();
            diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                    .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                    .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                    .in(DiTerritoryProductMapping::getProductId, ids)
            );
        } else {
            territoryMappingMapper.delete(Wrappers.<DiTerritoryMapping>lambdaQuery()
                    .eq(DiTerritoryMapping::getRefId, reqVO.getRefId())
                    .eq(DiTerritoryMapping::getTerritoryId, reqVO.getTerritoryId()));
            diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                    .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                    .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clueToNiche(String clueNo, String nicheNo) {
        List<DiTerritoryMapping> territoryMappingList =
                territoryMappingMapper.selectList(Wrappers.<DiTerritoryMapping>lambdaQuery().eq(DiTerritoryMapping::getRefId, clueNo))
                .stream().map(mapping -> new DiTerritoryMapping(mapping, nicheNo)).toList();
        List<DiTerritoryProductMapping> territoryProductMappingList =
                diTerritoryProductMappingMapper.selectList(Wrappers.<DiTerritoryProductMapping>lambdaQuery().eq(DiTerritoryProductMapping::getRefId, clueNo))
                        .stream().map(mapping -> new DiTerritoryProductMapping(mapping, nicheNo)).toList();
        territoryMappingMapper.insert(territoryMappingList);
        diTerritoryProductMappingMapper.insert(territoryProductMappingList);
    }

}