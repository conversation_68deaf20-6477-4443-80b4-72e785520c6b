package com.dyd.di.tools.dto.query;

import com.dyd.di.pre.enums.PreSaleTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class MultiFeeCalcQry {
    /**
     * 商务费用
     */
    private BigDecimal businessCost;


    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 是否是出口核价
     */
    private boolean export;

    /**
     * 所属BU
     */
    private String bu;

    /**
     * 需要售后安装
     */
    private Boolean needPostSaleInstall;

    /**
     * 方案列表
     */
    List<PreSale> preSaleList = new ArrayList<>();

    /**
     * 方案
     */
    @Data
    public static class PreSale {
        /**
         * 包装方式
         */
        private String packageType;
        /**
         * 包材明细
         */
        List<MaterielItem> materielItems = new ArrayList<>();

        /**
         * 需求套数
         */
        private BigDecimal num = BigDecimal.ONE;

        /**
         * 所属行业
         */
        private String industry;

        /**
         * 技术难度，枚举
         */
        private String technicalDifficulty;

        /**
         * 机械难度
         */
        private String mechanicalDifficulty;

        /**
         * 电气难度
         */
        private String electricalDifficulty;

        /**
         * 生产难度
         */
        private String productionDifficulty;

        /**
         * 售后难度
         */
        private String afterSalesDifficulty;

        private String preSaleCode;

        /**
         * - 技术支持费用，根据下表行业+技术难度得出的系数*1000
         */
        private BigDecimal technicalSupportCost;
        /**
         * - 电气设计费用，根据下表行业+技术难度得出的系数*1000
         */
        private BigDecimal electricalDesignCost;
        /**
         * -机械设计费用，根据下表行业+技术难度得出的系数*1000
         */
        private BigDecimal mechanicalDesignCost;
        /**
         * - 售后施工费用，根据上表的行业+技术难度得出的系数*1000
         */
        private BigDecimal afterSaleCost;
        /**
         * 方案类型
         */
        private PreSaleTypeEnum preSaleType;
        /**
         * 贸易类物料费合计
         */
        private BigDecimal tradeMaterialCosts;
        /**
         * 交付调试周期
         */
        private BigDecimal afterSaleRate;
        /**
         * 包装费用
         */
        private BigDecimal packageCost;

    }

    @Data
    public static class MaterielItem {
        /**
         * 物料id
         */
        private Long materielId;
        /**
         * materiel_length
         */
        private BigDecimal materielLength;
        /**
         * materiel_width
         */
        private BigDecimal materielWidth;
        /**
         * materiel_height
         */
        private BigDecimal materielHeight;

        /**
         * materiel_weight
         */
        private BigDecimal materielWeight;
        /**
         * quantity
         */
        private BigDecimal quantity = BigDecimal.ONE;
        /**
         * guide_production_costs
         */
        private BigDecimal guideProductionCosts;
        private BigDecimal guideMaterialCosts;
        private BigDecimal historyPackageCost;

        private Integer hasBom;

    }

}
