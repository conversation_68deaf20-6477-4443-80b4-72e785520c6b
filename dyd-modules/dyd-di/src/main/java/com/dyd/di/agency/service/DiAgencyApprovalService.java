package com.dyd.di.agency.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.agency.domain.dto.ChangeLiabilityByDTO;
import com.dyd.di.agency.domain.dto.CommonDTO;
import com.dyd.di.agency.domain.dto.FindAgencyApprovalListDTO;
import com.dyd.di.agency.domain.vo.AgencyApprovalListVO;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.marketing.domain.DiMarketingNiche;

import java.util.List;

/**
 * 代办审批Service接口
 */
public interface DiAgencyApprovalService extends IService<DiAgencyApproval> {

    /**
     * 查询代办审批列表(无分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    List<AgencyApprovalListVO> findAgencyApprovalList(FindAgencyApprovalListDTO dto);

    /**
     * 查询代办审批列表(分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    PageWrapper<List<AgencyApprovalListVO>> findAgencyApprovalListPage(FindAgencyApprovalListDTO dto);

    /**
     * 更新是否已读
     *
     * @param dto 更新参数
     */
    void updateIsReadById(CommonDTO dto);

    /**
     * 删除代办审批
     *
     * @param dto 删除参数
     */
    void deleteAgencyApproval(CommonDTO dto);

    /**
     * 获取当前登录者的未读代办任务数量
     *
     * @return 数量
     */
    Integer getUnReadCountByLogin();

    void deleteAgencyApproval(DiMarketingNiche diMarketingNiche, String status);

    String changeLiabilityBy(ChangeLiabilityByDTO dto);

   default void cleanApproval(ApprovalTypeEnum approvalTypeEnum, String businessKey){
       this.lambdaUpdate()
               .set(DiAgencyApproval::getIsDeleted, 1)
               .eq(DiAgencyApproval::getApprovalTypeCode, approvalTypeEnum.getTypeCode())
               .eq(DiAgencyApproval::getBusinessKey, businessKey)
               .eq(DiAgencyApproval::getIsDeleted, 0)
               .update();
   }

    /**
     * 清空审批
     *
     * @param bizKey
     */
    default void clearApproval(String bizKey) {
        this.lambdaUpdate().eq(DiAgencyApproval::getBusinessKey, bizKey)
                .eq(DiAgencyApproval::getIsDeleted, 0)
                .set(DiAgencyApproval::getIsDeleted, 1)
                .update();
    }
}
