package com.dyd.di.pre.domain.response;

import com.dyd.di.pre.domain.DiPreSaleQuoteEstimatingGp;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 报价单详情
 */
@Data
public class PreSaleQuoteInfoResponse {

    /**
     * 报价单id
     */
    private Integer id;

    /**
     * 报价状态 0:未报价 1：已报价
     */
    private Integer quoteStatus;

    /**
     * 报价单编码
     */
    private String preSaleQuoteCode;

    /**
     * 客户名称
     */
    private String companyName;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系方式
     */
    private String contactsPhone;

    /**
     * 是否提前开票
     */
    private String preInvoice;

    /**
     * 理论成本合计
     */
    private BigDecimal costFeeTotal;

    /**
     * 销售报价合计
     */
    private BigDecimal saleQuoteTotal;

    /**
     * 毛利额合计
     */
    private BigDecimal grossProfitTotal;

    /**
     * 整单毛利率
     */
    private BigDecimal grossMarginTotal;

    /**
     * 商机号
     */
    private String nicheCode;

    /**
     * 审批状态 0:待审批 1:审批中 2:审批通过 3：审批驳回
     */
    private Integer approvalStatus;

    /**
     * 销售服务报价合计
     */
    private BigDecimal saleServiceQuoteTotal;

    /**
     * 销售服务周期报价成本合计
     */
    private BigDecimal saleServiceCycleTotal;

    /**
     * 订单期望交付日期
     */
    private LocalDate expecteDate;

    /**
     * 报价单明细
     */
    private List<PreSaleQuoteDetail> preSaleQuoteDetailList;

    private Boolean permission = true;

    /**
     * 账期
     */
    private List<PreSaleQuoteInfoResponse.PreSaleQuotePeriod> preSaleQuotePeriods;


    @Data
    public static class PreSaleQuotePeriod {

        /**
         *
         */
        private Integer id;

        /**
         * 账期
         */
        private Integer billPeriod;

        /**
         * 支付金额
         */
        private BigDecimal payAmount;

        /**
         * 账期描述
         */
        private String remark;

        /**
         * 付款比例
         */
        private BigDecimal payPercent;

        /**
         * 计划支付日
         */
        private LocalDate planPayDate;

        /**
         * 税率
         */
        private BigDecimal rate;

        /**
         * 发票情况
         */
        private String invoiceDesc;

        /**
         * 实际支付日期
         */
        private LocalDate realPayDate;
    }


    @Data
    public static class PreSaleQuoteDetail {

        /**
         * 报价单明细id
         */
        private Integer id;

        /**
         * 产品方案ID
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;

        /**
         * 产品方案编码
         */
        private String preSaleCode;

        /**
         * 商机编码
         */
        private String nicheCode;

        /**
         * 产品方案名称
         */
        private String preSaleName;

        /**
         * 订单类型
         */
        private Integer orderType;

        /**
         * 产品方案类型
         */
        private Integer preSaleType;

        /**
         * 物料编码
         */
        private String materialCode;

        private String showVersionNo;
        private String showMaterialNo;

        /**
         * 物料名称
         */
        private String productName;

        /**
         * 需求套数
         */
        private Integer num;

        /**
         * 当前库存
         */
        private String stockNum;

        /**
         * 理论成本小计
         */
        private BigDecimal costFee;

        /**
         * 理论成本合计
         */
        private BigDecimal costFeeSum;

        /**
         * 单套理论交期
         */
        private BigDecimal guideDuration;

        /**
         * 售价报价
         */
        private BigDecimal saleQuote;

        /**
         * 交期报价(天)
         */
        private BigDecimal deliveryQuote;

        /**
         * 毛利额
         */
        private BigDecimal grossProfit;

        /**
         * 毛利率
         */
        private BigDecimal grossMargin;

        /**
         * 国家
         */
        @JsonProperty(value = "countryName")
        private String country;

        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 省
         */
        @JsonProperty("provinceName")
        private String province;

        /**
         * 省编码
         */
        private String provinceCode;

        /**
         * 市
         */
        @JsonProperty("cityName")
        private String city;

        /**
         * 市编码
         */
        private String cityCode;

        /**
         * 区
         */
        @JsonProperty("areaName")
        private String area;

        /**
         * 区编码
         */
        private String areaCode;

        /**
         * 销售服务报价
         */
        private BigDecimal saleServiceQuote;

        /**
         * 销售服务周期报价（人/天）
         */
        private BigDecimal saleServiceCycle;

        /**
         * 单套理论服务费
         */
        private BigDecimal systemServiceFee;

        /**
         * 单套服务理论周期
         */
        private BigDecimal systemServiceCycle;

        /**
         * 技术支持负责人代码
         */
        private String techSupportOwnerCode;

        /**
         * 技术支持负责人名称
         */
        private String techSupportOwnerName;
        /**
         * 是否高亮
         */
        private Boolean showHighLight;
        /**
         * 交期处理 待审批人
         */
        private String deliveryReviewApprover;

    }

    /**
     * 成本审批流程实例ID
     */
    private String costApprovalProcessId;

    /**
     * 审批流中任务ID
     */
    private String camundaTaskId;

    /**
     * 审批角色
     */
    private String approvalRole;

    /**
     * 审批人员
     */
    private String approvalBys;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 统一收货地址
     */
    private String commonDeliveryAddress;

    /**
     * 商务费用
     */
    private BigDecimal businessCost;

    /**
     * 共享销售列表
     */
    private List<PreSaleQuotaShareResponse> shareList;

    /**
     * GP1234，汇总
     */
    private GpDetailResponse gpDetailResponse;

    /**
     * 预测GP 汇总
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating;


    /**
     * GP1234，项目类
     */
    private GpDetailResponse gpDetailResponse4Project;

    /**
     * 预测GP 项目类
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating4Project;



    /**
     * GP1234，贸易类
     */
    private GpDetailResponse gpDetailResponse4Trade;

    /**
     * 预测GP 贸易类
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating4Trade;



    /**
     * 核算交期（周），获取待报价方案列表中，单套测算交期（天）最大值。 以该值/7，并向上取整。如 7.1周 算8周。
     */
    private BigDecimal calculateDeliveryWeek;


    /**
     * 复核后交期（周）
     */
    private BigDecimal finallyDeliveryWeek;


    /**
     * 期望交期（周），从商机 期望交期（周）字段，带入
     */
    private BigDecimal expectedDeliveryWeek;
    /**
     * 显示交期复核
     */
    private boolean showDeliveryReview;

    /**
     * 是否发起过交期复核
     */
    private Integer deliveryReviewFlag;

    /**
     * 是否提前执行：1.否，2.是
     */
    private Integer isAdvanceExecution;

    /**
     * 期望交期（周）
     */
    private Integer expectationDeliveryWeek;

    /**
     * 是否允许放弃：ture：允许，false：不允许
     */
    private Boolean isAllowAbandon = true;

}
