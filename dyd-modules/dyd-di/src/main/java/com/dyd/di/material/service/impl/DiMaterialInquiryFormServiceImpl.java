package com.dyd.di.material.service.impl;

import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.constant.DestinationConstants;
import com.dyd.common.core.enums.RelationTypeEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.material.constants.MaterialConstants;
import com.dyd.di.material.convert.MaterialConvert;
import com.dyd.di.material.domain.dto.*;
import com.dyd.di.material.domain.vo.DiMaterialInquiryFormVo;
import com.dyd.di.material.domain.vo.MaterialInquiryListDataVo;
import com.dyd.di.material.entity.*;
import com.dyd.di.material.iservice.IDiMaterialInquiryDetailService;
import com.dyd.di.material.iservice.IDiMaterialQuotedDetailService;
import com.dyd.di.material.iservice.IDiMaterialQuotedFormService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.service.IDiMaterielService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.pre.conver.PreSaleConverUtil;
import com.dyd.di.pre.domain.request.PreSaleAddRequest;
import com.dyd.di.pre.entity.*;
import com.dyd.di.pre.enums.DingTalkEnum;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.mapper.DiPreSaleManifestMapper;
import com.dyd.di.pre.service.*;
import com.dyd.di.pre.service.impl.DiPreSaleServiceImpl;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.service.IDiProjectRelationService;
import com.dyd.di.rd.entity.DiRdNeed;
import com.dyd.di.rd.entity.DiRdNeedModule;
import com.dyd.di.rd.service.IDiRdNeedModuleService;
import com.dyd.di.rd.service.IDiRdNeedService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysUser;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dyd.di.material.mapper.DiMaterialInquiryFormMapper;
import com.dyd.di.material.service.DiMaterialInquiryFormService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 采购_物料询价单 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
@Slf4j
public class DiMaterialInquiryFormServiceImpl extends ServiceImpl<DiMaterialInquiryFormMapper, DiMaterialInquiryForm> implements DiMaterialInquiryFormService {

    @Autowired
    private DiMaterialInquiryFormMapper diMaterialInquiryFormMapper;

    @Resource
    private SequenceService sequenceService;

    @Resource
    private IDiMaterialInquiryDetailService iDiMaterialInquiryDetailService;

    @Resource
    private IDiMaterialQuotedFormService diMaterialQuotedFormService;

    @Resource
    private IDiMaterialQuotedDetailService diMaterialQuotedDetailService;

    @Resource
    private CommonService commonService;

    @Resource
    private IDiPreSaleService diPreSaleService;

    @Resource
    private IDiPreSaleSupportService diPreSaleSupportService;

    @Resource
    private IDiPreSaleSupportModuleService diPreSaleSupportModuleService;

    @Resource
    private IDiMaterielService diMaterielService;

    @Resource
    private IDiRdNeedService diRdNeedService;

    @Resource
    private IDiRdNeedModuleService diRdNeedModuleService;

    @Resource
    private IDiPreSaleService iDiPreSaleService;

    @Resource
    private IDiPreSaleManifestService iDiPreSaleManifestService;

    @Autowired
    private IDiMaterialQuotedFormService iDiMaterialQuotedFormService;

    @Autowired
    private IDiMaterialQuotedDetailService iDiMaterialQuotedDetailService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private IDiPreSaleMaterielSelectionService selectionService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private IDiMessageListService diMessageListService;

    @Resource
    private IDiProjectRelationService diProjectRelationService;

    @Autowired
    private DiPreSaleServiceImpl diPreSaleServiceImpl;

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private PreSaleConverUtil preSaleConverUtil;

    private final static Long PURCHASE_DEPT_ID = 971507119L;

    @Autowired
    private DiAgencyTaskService agencyTaskService;

    /**
     * 新增询价单与报价单
     *
     * @param dto 新增DTO
     * @return 询价单新增数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DiMaterialInquiryForm addInquiryForm(SaveDiMaterialInquiryFormDto dto) {
        //记录方案版本
        DiPreSale preSale = iDiPreSaleService.getById(dto.getPreSaleId());
        if (null == preSale) {
            throw new ServiceException("产品方案不存在");
        }
        Integer preSaleVersion = 1;
        List<DiPreSale> preSales = iDiPreSaleService.list(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, preSale.getPreSaleCode()));
        //产品方案物料
        List<DiPreSaleManifest> diPreSaleManifests = iDiPreSaleManifestService.list(Wrappers.<DiPreSaleManifest>lambdaQuery()
                .in(DiPreSaleManifest::getDiPreSaleId, preSales.stream().map(DiPreSale::getId).toList()).orderByDesc(DiPreSaleManifest::getId));
        if (CollectionUtil.isNotEmpty(diPreSaleManifests)) {
            if (1 < diPreSaleManifests.size()) {
                DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
                preSaleVersion = diPreSaleManifest.getManifestVersion();
            }
        }
        //将DTO转为实体
        DiMaterialInquiryForm inquiryForm = MaterialConvert.INSTANCE.inquiryAddDtoConvertEntity(dto);
        //生成询价单号
        inquiryForm.setMaterialInquiryNo(sequenceService.getSequenceNo("DYD-XJ"));
        inquiryForm.setPreSaleVersion(preSaleVersion);
        inquiryForm.setQuotedStatus(2);
        //保存商机
        if (null != preSale.getNicheId()) {
            inquiryForm.setNicheId(Long.valueOf(preSale.getNicheId()));
        }
        if (StringUtils.isNotBlank(preSale.getNicheCode())) {
            inquiryForm.setNicheNo(preSale.getNicheCode());
        }
        //生成
        List<DiMaterialQuotedDetail> quotedDetailList = saveInquiryQuotedData(inquiryForm, dto.getDetailDto());
        //新增完成后将创建人加入到项目参与者当中
        commonService.personnelSaveProject(inquiryForm.getProjectNo(), Collections.singletonList(SecurityUtils.getUsername()), MaterialConstants.STR_ZERO);

        selectionService.startInquiry(quotedDetailList.stream().map(DiMaterialQuotedDetail::getSelectionId).toList());
        if (null != dto.getInquiryType() && dto.getInquiryType().equals(2)) {
            //获取部门下所有员工
            SysUser user = new SysUser();
            user.setDeptId(PURCHASE_DEPT_ID);
            List<SysUser> userList = remoteUserService.findUserListByCondition(user);
            if (CollectionUtil.isEmpty(userList)) {
                log.error("DiMaterialInquiryFormServiceImpl---addInquiryForm()---根据部门ID未获取到员工列表，部门ID：{}", PURCHASE_DEPT_ID);
                return inquiryForm;
            }
            //员工工号
            List<String> jobNumList = new ArrayList<>();
            //发送钉钉消息
            String title = StrUtil.format(DingTalkEnum.LECTOTYPE_INQUIRY_ADD_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
            String content = StrUtil.format(DingTalkEnum.LECTOTYPE_INQUIRY_ADD_NOTICE.getMessage(), preSale.getNicheCode(), preSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            sendDingTalkMessage(title, content, userList, jobNumList);
            saveAgencyTask(inquiryForm.getMaterialInquiryNo(), AgencyTaskTypeEnum.PURCHASE_MANAGE_LECTOTYPE,
                    StringUtils.isNotBlank(preSale.getPreSaleName()) ? preSale.getPreSaleName() : preSale.getPreSaleCode(),
                    jobNumList, getProjectNo(preSale.getNicheCode()));
        }
        return inquiryForm;
    }

    private String getProjectNo(String nicheCode) {
        //获取项目编号
        List<DiProjectRelation> relationList = diProjectRelationService.list(new LambdaQueryWrapper<DiProjectRelation>()
                .eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS)
                .eq(DiProjectRelation::getRelationNo, nicheCode));
        if (CollectionUtil.isNotEmpty(relationList)) {
            return relationList.get(0).getProjectNo();
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DiMaterialInquiryForm manualAddInquiryForm(SaveDiMaterialInquiryFormDto dto) {
        //将DTO转为实体
        DiMaterialInquiryForm inquiryForm = MaterialConvert.INSTANCE.inquiryAddDtoConvertEntity(dto);
        //生成询价单号
        inquiryForm.setMaterialInquiryNo(sequenceService.getSequenceNo("DYD-XJ"));
        inquiryForm.setQuotedStatus(1);
        saveInquiryQuotedData(inquiryForm, dto.getDetailDto());
        //获取部门下所有员工
        SysUser user = new SysUser();
        user.setDeptId(PURCHASE_DEPT_ID);
        List<SysUser> userList = remoteUserService.findUserListByCondition(user);
        if (CollectionUtil.isEmpty(userList)) {
            log.error("DiMaterialInquiryFormServiceImpl---manualAddInquiryForm()---根据部门ID未获取到员工列表，部门ID：{}", PURCHASE_DEPT_ID);
            return inquiryForm;
        }
        //员工工号
        List<String> jobNumList = new ArrayList<>();
        //生成待办任务发送钉钉消息
        if ("3".equals(dto.getInquirySource()) && dto.getInquiryType().equals(1)) {
            //发送钉钉消息
            String title = StrUtil.format(DingTalkEnum.MANUAL_MATERIAL_INQUIRY_ADD_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
            String content = StrUtil.format(DingTalkEnum.MANUAL_MATERIAL_INQUIRY_ADD_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName(), inquiryForm.getMaterialInquiryNo());
            sendDingTalkMessage(title, content, userList, jobNumList);
            saveAgencyTask(inquiryForm.getMaterialInquiryNo(), AgencyTaskTypeEnum.PURCHASE_MANAGE_INQUIRY,
                    inquiryForm.getMaterialInquiryNo(), jobNumList, "");
        }
        if ("3".equals(dto.getInquirySource()) && dto.getInquiryType().equals(2)) {
            //发送钉钉消息
            String title = StrUtil.format(DingTalkEnum.MANUAL_LECTOTYPE_INQUIRY_ADD_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
            String content = StrUtil.format(DingTalkEnum.MANUAL_LECTOTYPE_INQUIRY_ADD_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName(), inquiryForm.getMaterialInquiryNo());
            sendDingTalkMessage(title, content, userList, jobNumList);
            saveAgencyTask(inquiryForm.getMaterialInquiryNo(), AgencyTaskTypeEnum.PURCHASE_MANAGE_LECTOTYPE,
                    inquiryForm.getMaterialInquiryNo(), jobNumList, "");
        }
        if ("3".equals(dto.getInquirySource()) && dto.getInquiryType().equals(3)) {
            //发送钉钉消息
            String title = StrUtil.format(DingTalkEnum.MANUAL_MFG_FEE_INQUIRY_ADD_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
            String content = StrUtil.format(DingTalkEnum.MANUAL_MFG_FEE_INQUIRY_ADD_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName(), inquiryForm.getMaterialInquiryNo());
            sendDingTalkMessage(title, content, userList, jobNumList);
            saveAgencyTask(inquiryForm.getMaterialInquiryNo(), AgencyTaskTypeEnum.PURCHASE_MANAGE_MFG_FEE,
                    inquiryForm.getMaterialInquiryNo(), jobNumList, "");
        }
        return inquiryForm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitManualInquiryForm(OperationInquiryFormDTO dto) {
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(dto.getId());
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
        updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
        updateWrapper.set(DiMaterialInquiryForm::getQuotedStatus, 2);
        updateWrapper.update();
    }

    public List<DiMaterialQuotedDetail> saveInquiryQuotedData(DiMaterialInquiryForm inquiryForm, List<SaveDiMaterialInquiryDetailDto> detailDto) {
        diMaterialInquiryFormMapper.insert(inquiryForm);
        //将询价单明细设置询价单ID
        List<DiMaterialInquiryDetail> inquiryDetailList = MaterialConvert.INSTANCE.inquiryDetailAddDtoConvertEntityList(detailDto);
        inquiryDetailList.forEach(inquiryDetail -> inquiryDetail.setMaterialInquiryId(Long.valueOf(inquiryForm.getId())));
        //新增询价单明细
        iDiMaterialInquiryDetailService.saveBatch(inquiryDetailList);
        //将询价单实体转为报价单实体
        DiMaterialQuotedForm quotedForm = MaterialConvert.INSTANCE.inquiryFormConvertQuotedForm(inquiryForm);
        quotedForm.setMaterialInquiryId(Long.valueOf(inquiryForm.getId()));
        //生成报价单号
        quotedForm.setMaterialQuotedNo(sequenceService.getSequenceNo("DYD-BJ"));
        quotedForm.setQuotedSource(inquiryForm.getInquirySource());
        //新增报价单
        diMaterialQuotedFormService.save(quotedForm);
        //将询价单明细实体列表转为报价单明细实体列表
        List<DiMaterialQuotedDetail> quotedDetailList = new ArrayList<>();
        inquiryDetailList.forEach(inquiryDetail -> {
            DiMaterialQuotedDetail quotedDetail = MaterialConvert.INSTANCE.inquiryDetailConvertQuotedDetail(inquiryDetail);
            //完善数据
            quotedDetail.setMaterialQuotedId(Long.valueOf(quotedForm.getId()));
            quotedDetail.setMaterialInquiryDetailId(Long.valueOf(inquiryDetail.getId()));
            quotedDetail.setQuotedExplain(inquiryDetail.getInquiryExplain());
            quotedDetailList.add(quotedDetail);
        });
        //新增报价单明细
        diMaterialQuotedDetailService.saveBatch(quotedDetailList);
        return quotedDetailList;
    }

    private void sendDingTalkMessage(String title, String content, List<SysUser> userList, List<String> jobNumList) {
        //发送钉钉消息
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        userList.forEach(item -> {
            jobNumList.add(item.getUserName());
            diMessageList.setSendingUser(item.getUserName());
            diMessageListService.insertDiMessageList(diMessageList);
        });
    }

    private void saveAgencyTask(String businessKey, AgencyTaskTypeEnum taskTypeEnum, String taskName, List<String> jobNumList, String projectNo) {
        //生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(businessKey);
        agencyTaskInfoDto.setProjectNo(projectNo);
        agencyTaskInfoDto.setTaskTypeEnum(taskTypeEnum);
        agencyTaskInfoDto.setTaskName(taskName);
        agencyTaskInfoDto.setTaskStateDesc("询价中");
        agencyTaskInfoDto.setLiabilityByList(jobNumList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    @Override
    public DiMaterialInquiryFormVo getInquiryForm(GetMaterialInquiryFormDTO dto) {
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectOne(new LambdaQueryWrapper<DiMaterialInquiryForm>().eq(DiMaterialInquiryForm::getMaterialInquiryNo, dto.getMaterialInquiryNo()));
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        DiMaterialInquiryFormVo vo = MaterialConvert.INSTANCE.inquiryFormEntityConvertVo(inquiryForm);
        //获取受理人姓名
        if (StringUtils.isNotBlank(vo.getAcceptanceBy())) {
            Map<String, String> userNameMap = commonService.getUserNameByJob(Collections.singletonList(vo.getAcceptanceBy()), null);
            if (CollectionUtil.isNotEmpty(userNameMap) && userNameMap.containsKey(vo.getAcceptanceBy())) {
                vo.setAcceptanceByName(userNameMap.get(vo.getAcceptanceBy()));
            }
        }
        return vo;
    }

    /**
     * 获取询价单列表
     *
     * @param dto 查询条件DTO
     * @return VO集合
     */
    @Override
    public PageWrapper<List<DiMaterialInquiryFormVo>> findInquiryFormList(FindDiMaterialInquiryFormDto dto) {
        //判断是不是采购部门的,如果是采购部门的默认就能查看所有采购相关数据
        if (!MaterialConstants.PROCURE_DEPT_ID.equals(SecurityUtils.getLoginUser().getSysUser().getDeptId())
                && !MaterialConstants.PROCURE_DEPT_ID.equals(SecurityUtils.getLoginUser().getSysUser().getDept().getParentId())) {
            //数据权限处理
            String jobNumberStrSql = commonService.findLoginJobNumberStrSql();
            List<String> jobNumberList = commonService.findLoginJobNumberList();
            if (StrUtil.isNotBlank(jobNumberStrSql) && CollectionUtil.isNotEmpty(jobNumberList)) {
                dto.setJobNumberList(jobNumberList);
                dto.setJobNumberStrSql(jobNumberStrSql);
            }
        }
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<DiMaterialInquiryFormVo> voList = diMaterialInquiryFormMapper.findInquiryFormList(dto);
        if (CollectionUtil.isNotEmpty(voList)) {
            //获取用户姓名
            List<String> userList = new ArrayList<>(voList.stream().map(DiMaterialInquiryFormVo::getCreateBy).filter(StrUtil::isNotBlank).toList());
            userList.addAll(voList.stream().map(DiMaterialInquiryFormVo::getUpdateBy).filter(StrUtil::isNotBlank).toList());
            userList.addAll(voList.stream().map(DiMaterialInquiryFormVo::getOfferBy).filter(StrUtil::isNotBlank).toList());
            userList.addAll(voList.stream().map(DiMaterialInquiryFormVo::getAcceptanceBy).filter(StrUtil::isNotBlank).toList());
            Map<String, String> nameMap = commonService.getUserNameByJob(userList, null);
            if (CollectionUtil.isNotEmpty(nameMap)) {
                voList.forEach(vo -> {
                    if (StrUtil.isNotBlank(nameMap.get(vo.getCreateBy()))) {
                        vo.setCreateByName(nameMap.get(vo.getCreateBy()));
                    }
                    if (StrUtil.isNotBlank(nameMap.get(vo.getUpdateBy()))) {
                        vo.setUpdateByName(nameMap.get(vo.getUpdateBy()));
                    }
                    if (StrUtil.isNotBlank(nameMap.get(vo.getOfferBy()))) {
                        vo.setOfferByName(nameMap.get(vo.getOfferBy()));
                    }
                    if (StringUtils.isNotBlank(nameMap.get(vo.getAcceptanceBy()))) {
                        vo.setAcceptanceByName(nameMap.get(vo.getAcceptanceBy()));
                    }
                });
            }
        }
        return PageHelp.render(page, voList);
    }

    /**
     * 确认报价
     *
     * @param id 主键id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inquiryQuoted(String id) {
        //根据id查询询价单
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(id);
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        //获取请购单明细
        List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId()));
        if (CollectionUtil.isEmpty(detailList)) {
            throw new ServiceException("询价单明细不存在");
        }
        //校验是否全部报价完成
        detailList.forEach(detail -> {
            if (null == detail.getGuideMaterialCost() || StrUtil.isBlank(detail.getDeliveryTime())
                    || StrUtil.isBlank(detail.getSupplier()) || null == detail.getMaterialQuotedDetailId()) {
                throw new ServiceException("询价单明细未全部报价，不允许提交");
            }
        });
        inquiryForm.setIsQuoted(MaterialConstants.STR_ONE);
        diMaterialInquiryFormMapper.updateById(inquiryForm);
        //数据回写
        selectionService.completeInquiry(detailList);
        //TODO:更新完成后将询价单数据推送到其他地方(具体什么地方串流程时在调)
        // modify - 20240909: 报价完成后推送消息，在物料库会订阅消息，更新物料价格（这个版本不考虑物料版本，维护物料全局的价格信息）
//        rocketMQTemplate.syncSend(DestinationConstants.MATERIAL_INQUIRY_FORM_TOPIC_TAG, detailList);
    }

    @Override
    public void preSaleMaterielReLoad(Long selectionId) {
        try {

            DiPreSaleMaterielSelection diPreSaleMaterielSelection = selectionService.getBaseMapper().selectById(selectionId);
            if (Objects.isNull(diPreSaleMaterielSelection)) {
                return;
            }

            /*//获取请购单明细
            List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                    .eq(DiMaterialInquiryDetail::getSelectionId, selectionId));
            if (CollectionUtil.isEmpty(detailList)) {
                throw new ServiceException("询价单明细不存在");
            }

            //根据id查询询价单
            DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(detailList.get(0).getMaterialInquiryId());
            if (null == inquiryForm) {
                throw new ServiceException("询价单不存在");
            }*/

            List<DiMateriel> diMateriels = diMaterielService.queryMaterielByNos(Arrays.asList(diPreSaleMaterielSelection.getMaterielNo()));
            if (CollectionUtil.isEmpty(diMateriels)) {
                return;
            }

            List<DiPreSaleManifest> diPreSaleManifestList = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, diPreSaleMaterielSelection.getDiPreSaleId()).eq(DiPreSaleManifest::getDelFlag, 0));
            List<PreSaleAddRequest.PreSaleManifest> preSaleManifests = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(diPreSaleManifestList)) {
                preSaleManifests = diPreSaleManifestList.stream().map(diPreSaleManifest -> {
                    PreSaleAddRequest.PreSaleManifest preSaleManifest = preSaleConverUtil.converAddPreManifest(diPreSaleManifest);
                    preSaleManifest.setMaterielId(diPreSaleManifest.getMaterialVersion());
                    return preSaleManifest;
                }).collect(Collectors.toList());
            }
            PreSaleAddRequest.PreSaleManifest preSaleManifest = new PreSaleAddRequest.PreSaleManifest();
            preSaleManifest.setMaterielId(diMateriels.get(0).getId());
            preSaleManifest.setProductName(diPreSaleMaterielSelection.getMaterielName());
            preSaleManifest.setUseNum(diPreSaleMaterielSelection.getConsumeQuantity());
            preSaleManifests.add(preSaleManifest);

            createTradePreSale(diPreSaleMaterielSelection.getDiPreSaleId(), preSaleManifests);
        } catch (Exception e) {
            log.info("回写贸易类清单报错{}", e);
        }
    }


    /**
     * 回写贸易类清单
     *
     * @param preSaleId
     * @param manifests
     */
    private void createTradePreSale(Long preSaleId, List<PreSaleAddRequest.PreSaleManifest> manifests) {
        DiPreSale diPreSale = diPreSaleService.getById(preSaleId);
        if (Objects.isNull(diPreSale)) {
            return;
        }
        if (!diPreSale.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode())) {
            return;
        }
        if (CollectionUtil.isNotEmpty(manifests)) {

            diPreSaleServiceImpl.createTradePreSale(diPreSale, manifests, null, diPreSale.getPackageType());
        }

    }

    /**
     * 新增询价单获取售前方案或研发需求物料清单
     *
     * @param dto 查询条件DTO
     * @return 结果
     */
    @Override
    public List<MaterialInquiryListDataVo> findMaterialInquiryListData(MaterialInquiryListDataDto dto) {
        List<MaterialInquiryListDataVo> voList = new ArrayList<>();
        if (MaterialConstants.STR_ONE.equals(dto.getInquirySource())) {
            //售前方案询价数据构建
            formPreSaleInquiryDetailData(dto.getPreSaleOrRdNeedId(), null, null, voList, MaterialConstants.STR_TWO);
        } else if (MaterialConstants.STR_TWO.equals(dto.getInquirySource())) {
            //研发需求询价数据构建
            formRdNeedInquiryDetailData(dto.getPreSaleOrRdNeedId(), null, null, voList, MaterialConstants.STR_TWO);
        } else {
            throw new ServiceException("询价来源错误");
        }
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmQuoted(InquiryQuotedRequest request) {
        //查询询价单
        DiMaterialInquiryForm inquiryForm = this.lambdaQuery().eq(DiMaterialInquiryForm::getId, request.getInquiryQutedId()).one();
        if (null == inquiryForm) {
            throw new ServiceException("物料询价单不存在");
        }
        //获取询价单明细
        List<DiMaterialInquiryDetail> inquiryDetailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId()).eq(DiMaterialInquiryDetail::getIsDeleted, MaterialConstants.ZERO));
        if (CollectionUtil.isEmpty(inquiryDetailList)) {
            throw new ServiceException("询价单明细不存在");
        }
        //查询报价单
        DiMaterialQuotedForm quotedForm = iDiMaterialQuotedFormService.lambdaQuery().eq(DiMaterialQuotedForm::getMaterialInquiryId, inquiryForm.getId()).one();
        if (quotedForm == null) {
            throw new ServiceException("物料报价单不存在");
        }
        //获取报价单明细
        List<DiMaterialQuotedDetail> quotedDetailList = iDiMaterialQuotedDetailService.list(new LambdaQueryWrapper<DiMaterialQuotedDetail>()
                .eq(DiMaterialQuotedDetail::getMaterialQuotedId, quotedForm.getId()));
        if (CollectionUtil.isEmpty(quotedDetailList)) {
            throw new ServiceException("报价单明细不存在");
        }
        if (inquiryDetailList.size() != quotedDetailList.size()) {
            throw new ServiceException("询价单数据不正确");
        }
        Map<Long, DiMaterialQuotedDetail> quotedDetailMap = quotedDetailList.stream().collect(Collectors.toMap(DiMaterialQuotedDetail::getMaterialInquiryDetailId, Function.identity()));
        inquiryDetailList.forEach(inquiryDetail -> {
            if (!quotedDetailMap.containsKey(Long.valueOf(inquiryDetail.getId()))) {
                throw new ServiceException("未获取到物料：" + inquiryDetail.getMaterielName() + "的报价数据");
            }
            DiMaterialQuotedDetail quotedDetail = quotedDetailMap.get(Long.valueOf(inquiryDetail.getId()));
            if (inquiryForm.getInquiryType().equals(1)) {
                Assert.notNull(quotedDetail.getGuideMaterialCost(), "指导物料费不能为空");
            }
            if (inquiryForm.getInquiryType().equals(3)) {
                Assert.notNull(quotedDetail.getGuideProductionCosts(), "物料制作费不能为空");
            }
            inquiryDetail.setGuideMaterialCost(quotedDetail.getGuideMaterialCost());
            inquiryDetail.setGuideProductionCosts(quotedDetail.getGuideProductionCosts());
            inquiryDetail.setDeliveryTime(quotedDetail.getDeliveryTime());
            inquiryDetail.setValidityDate(quotedDetail.getValidityDate());
            inquiryDetail.setSupplier(quotedDetail.getSupplier());
            iDiMaterialInquiryDetailService.updateById(inquiryDetail);
        });

        //这里代码有问题，如果前端传参有问题，这里会导致数据更新不正确
//        request.getInquiryQuotedInfoList().stream().forEach(info -> {
//            DiMaterialInquiryDetail inquiryDetail = detailList.stream().filter(d -> info.getMaterialId().equals(d.getMaterialId())).findFirst().orElse(null);
//            if (null == inquiryDetail) {
//                throw new ServiceException("询价单明细不存在");
//            }
//            if (inquiryForm.getInquiryType().equals(1)) {
//                Assert.notNull(info.getGuideMaterialCost(), "指导物料费不能为空");
//            }
//            if (inquiryForm.getInquiryType().equals(3)) {
//                Assert.notNull(info.getGuideProductionCosts(), "物料制作费不能为空");
//            }
////            Assert.notNull(info.getCurrencyName(), "币种不能为空");
////            Assert.notNull(info.getPriceCoefficient(), "价格系数不能为空");
////            Assert.notNull(info.getDeliveryTimeCoefficient(), "货期系数不能为空");
////            inquiryDetail.setQuotedPrice(info.getQuotedPrice());
//            inquiryDetail.setGuideMaterialCost(info.getGuideMaterialCost());
//            inquiryDetail.setGuideProductionCosts(info.getGuideProductionCosts());
//            inquiryDetail.setDeliveryTime(info.getDeliveryTime());
//            inquiryDetail.setValidityDate(info.getValidityDate());
//            inquiryDetail.setSupplier(info.getSupplier());
////            inquiryDetail.setCurrencyName(info.getCurrencyName());
////            inquiryDetail.setCurrencyType(info.getCurrencyType());
////            inquiryDetail.setPriceCoefficient(info.getPriceCoefficient());
////            inquiryDetail.setDeliveryTimeCoefficient(info.getDeliveryTimeCoefficient());
//            iDiMaterialInquiryDetailService.updateById(inquiryDetail);
//
//            List<DiMaterialQuotedDetail> quotedDetailList = iDiMaterialQuotedDetailService.list(new LambdaQueryWrapper<DiMaterialQuotedDetail>()
//                    .eq(DiMaterialQuotedDetail::getMaterialQuotedId, materialQuotedForm.getId()));
//            DiMaterialQuotedDetail quotedDetail = quotedDetailList.stream().filter(d -> info.getMaterialId().equals(d.getMaterialId())).findFirst().orElse(null);
//            if (null == quotedDetail) {
//                throw new ServiceException("报价单明细不存在");
//            }
////            quotedDetail.setCurrencyName(info.getCurrencyName());
////            quotedDetail.setCurrencyType(info.getCurrencyType());
////            quotedDetail.setPriceCoefficient(info.getPriceCoefficient());
////            quotedDetail.setDeliveryTimeCoefficient(info.getDeliveryTimeCoefficient());
//            quotedDetail.setSupplier(info.getSupplier());
//            quotedDetail.setQuotedPrice(info.getQuotedPrice());
//            quotedDetail.setGuideMaterialCost(info.getGuideMaterialCost());
//            quotedDetail.setGuideProductionCosts(info.getGuideProductionCosts());
//            quotedDetail.setDeliveryTime(info.getDeliveryTime());
//            quotedDetail.setValidityDate(info.getValidityDate());
//            iDiMaterialQuotedDetailService.updateById(quotedDetail);
//        });

        inquiryForm.setIsQuoted(MaterialConstants.STR_ONE);
        inquiryForm.setExpectCompleteTime(request.getExpectCompleteTime());
        inquiryForm.setQuotedStatus(MaterialConstants.THREE);
        if (StringUtils.isBlank(inquiryForm.getAcceptanceBy())) {
            inquiryForm.setAcceptanceBy(SecurityUtils.getUsername());
            inquiryForm.setIsAcceptance(MaterialConstants.ONE);
        }
        inquiryForm.setOfferBy(SecurityUtils.getUsername());
        diMaterialInquiryFormMapper.updateById(inquiryForm);

        quotedForm.setExpectCompleteTime(request.getExpectCompleteTime());
        diMaterialQuotedFormService.updateById(quotedForm);

        //TODO:更新完成后将询价单数据推送到其他地方(具体什么地方串流程时在调)
        // modify - 20240909: 报价完成后推送消息，在物料库会订阅消息，更新物料价格（这个版本不考虑物料版本，维护物料全局的价格信息）
        List<DiMaterialInquiryDetail> sendDataList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId())
                .eq(DiMaterialInquiryDetail::getIsDeleted, 0));
        rocketMQTemplate.syncSend(DestinationConstants.MATERIAL_INQUIRY_FORM_TOPIC_TAG, sendDataList);
        //将待办任务置为完成
        AgencyTaskInfoDTO agencyTaskInfoDto = getAgencyTaskInfoDTO(inquiryForm);
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_QUOTED_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
        String content = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_QUOTED_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        sendMessage(title, content, inquiryForm.getCreateBy());
        return true;
    }

    private static AgencyTaskInfoDTO getAgencyTaskInfoDTO(DiMaterialInquiryForm inquiryForm) {
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(inquiryForm.getMaterialInquiryNo());
        if (inquiryForm.getInquiryType().equals(1)) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PURCHASE_MANAGE_INQUIRY);
        }
        if (inquiryForm.getInquiryType().equals(2)) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PURCHASE_MANAGE_LECTOTYPE);
        }
        if (inquiryForm.getInquiryType().equals(3)) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PURCHASE_MANAGE_MFG_FEE);
        }
        agencyTaskInfoDto.setType("2");
        return agencyTaskInfoDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modelSelectionQuoted(InquiryQuotedRequest request) {
        DiMaterialInquiryForm inquiryForm = this.lambdaQuery().eq(DiMaterialInquiryForm::getId, request.getInquiryQutedId()).one();
        if (null == inquiryForm) {
            throw new ServiceException("物料选型单不存在");
        }
        DiMaterialQuotedForm quotedForm = iDiMaterialQuotedFormService.lambdaQuery().eq(DiMaterialQuotedForm::getMaterialInquiryId, inquiryForm.getId()).one();
        if (null == quotedForm) {
            throw new ServiceException("物料报价单不存在");
        }
        //获取询价单明细
        List<DiMaterialInquiryDetail> inquiryDetailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId())
                .eq(DiMaterialInquiryDetail::getIsDeleted, MaterialConstants.ZERO));
        if (CollectionUtil.isEmpty(inquiryDetailList)) {
            throw new ServiceException("选型单明细不存在");
        }
        //获取报价单明细
        List<DiMaterialQuotedDetail> quotedDetailList = iDiMaterialQuotedDetailService.list(new LambdaQueryWrapper<DiMaterialQuotedDetail>()
                .eq(DiMaterialQuotedDetail::getMaterialQuotedId, quotedForm.getId()));
        if (CollectionUtil.isEmpty(quotedDetailList)) {
            throw new ServiceException("报价单明细不存在");
        }
        if (inquiryDetailList.size() != quotedDetailList.size()) {
            throw new ServiceException("选型单数据不正确");
        }
        Map<Long, DiMaterialQuotedDetail> quotedDetailMap = quotedDetailList.stream().collect(Collectors.toMap(DiMaterialQuotedDetail::getMaterialInquiryDetailId, Function.identity()));
        inquiryDetailList.forEach(inquiryDetail -> {
            if (!quotedDetailMap.containsKey(Long.valueOf(inquiryDetail.getId()))) {
                throw new ServiceException("未获取到物料：" + inquiryDetail.getMaterielName() + "的报价数据");
            }
            DiMaterialQuotedDetail quotedDetail = quotedDetailMap.get(Long.valueOf(inquiryDetail.getId()));
            Assert.notNull(quotedDetail.getGuideMaterialCost(), "指导物料费不能为空");
            Assert.notNull(quotedDetail.getGuideProductionCosts(), "物料制作费不能为空");
            inquiryDetail.setGuideProductionCosts(quotedDetail.getGuideProductionCosts());
            inquiryDetail.setGuideMaterialCost(quotedDetail.getGuideMaterialCost());
            inquiryDetail.setSupplier(quotedDetail.getSupplier());
            inquiryDetail.setDeliveryTime(quotedDetail.getDeliveryTime());
            inquiryDetail.setValidityDate(quotedDetail.getValidityDate());
            iDiMaterialInquiryDetailService.updateById(inquiryDetail);
            // selectionService.completeInquiry(List.of(inquiryDetail));
        });

//        request.getInquiryQuotedInfoList().stream().forEach(info -> {
//
//            DiMaterialQuotedDetail quotedDetail = iDiMaterialQuotedDetailService.getById(info.getMaterialQuotedDetailId());
//            if (null == quotedDetail) {
//                throw new ServiceException("报价单明细不存在");
//            }
//            Assert.notNull(info.getGuideMaterialCost(), "指导物料费不能为空");
//            Assert.notNull(info.getGuideProductionCosts(), "物料制作费不能为空");
//            quotedDetail.setQuotedPrice(info.getQuotedPrice());
//            quotedDetail.setGuideMaterialCost(info.getGuideMaterialCost());
//            quotedDetail.setGuideProductionCosts(info.getGuideProductionCosts());
//            quotedDetail.setDeliveryTime(info.getDeliveryTime());
//            quotedDetail.setValidityDate(info.getValidityDate());
//            quotedDetail.setSupplier(info.getSupplier());
//            iDiMaterialQuotedDetailService.updateById(quotedDetail);
//
//            DiMaterialInquiryDetail inquiryDetail = iDiMaterialInquiryDetailService.getById(quotedDetail.getMaterialInquiryDetailId());
//            if (null == inquiryDetail) {
//                throw new ServiceException("询价单明细不存在");
//            }
////            inquiryDetail.setQuotedPrice(info.getQuotedPrice());
//            inquiryDetail.setGuideProductionCosts(info.getGuideProductionCosts());
//            inquiryDetail.setGuideMaterialCost(info.getGuideMaterialCost());
//            inquiryDetail.setSupplier(info.getSupplier());
//            inquiryDetail.setDeliveryTime(info.getDeliveryTime());
//            inquiryDetail.setValidityDate(info.getValidityDate());
//            iDiMaterialInquiryDetailService.updateById(inquiryDetail);
//            selectionService.completeInquiry(List.of(inquiryDetail));
//        });

        if (StringUtils.isBlank(inquiryForm.getAcceptanceBy())) {
            inquiryForm.setIsAcceptance(MaterialConstants.ONE);
            inquiryForm.setAcceptanceBy(SecurityUtils.getUsername());
        }
        inquiryForm.setOfferBy(SecurityUtils.getUsername());
        inquiryForm.setIsQuoted(MaterialConstants.STR_ONE);
        inquiryForm.setExpectCompleteTime(request.getExpectCompleteTime());
        inquiryForm.setQuotedStatus(MaterialConstants.THREE);
        diMaterialInquiryFormMapper.updateById(inquiryForm);
        //数据回写

        quotedForm.setExpectCompleteTime(request.getExpectCompleteTime());
        diMaterialQuotedFormService.updateById(quotedForm);

//        if (CollectionUtil.isNotEmpty(request.getInquiryQuotedInfoList())) {
//            List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
//                    .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId()));
//            rocketMQTemplate.syncSend(DestinationConstants.MATERIAL_INQUIRY_FORM_TOPIC_TAG, detailList);
//        }
        rocketMQTemplate.syncSend(DestinationConstants.MATERIAL_INQUIRY_FORM_TOPIC_TAG, inquiryDetailList);
        //将待办任务置为完成
        AgencyTaskInfoDTO agencyTaskInfoDto = getAgencyTaskInfoDTO(inquiryForm);
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        //发送钉钉消息
        String content = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_QUOTED_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        String title = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_QUOTED_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
        sendMessage(title, content, inquiryForm.getCreateBy());
        return true;
    }

    @Override
    public List<DiMaterialInquiryDetail> queryMaterielInquiryByPreSale(Long preSaleId, Long materielId) {
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectOne(new LambdaQueryWrapper<DiMaterialInquiryForm>()
                .eq(DiMaterialInquiryForm::getPreSaleId, preSaleId));
        if (inquiryForm == null) {
            return List.of();
        }
        List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.lambdaQuery()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId())
                .eq(DiMaterialInquiryDetail::getMaterialId, materielId).list();
        return detailList;
    }

    /**
     * 构建售前方案物料询价明细
     *
     * @param preSaleId         售前方案ID
     * @param inquiryForm       询价单
     * @param inquiryDetailList 询价明细
     * @param type              1.新增使用, 2查询使用
     */
    private void formPreSaleInquiryDetailData(Long preSaleId, DiMaterialInquiryForm inquiryForm,
                                              List<DiMaterialInquiryDetail> inquiryDetailList,
                                              List<MaterialInquiryListDataVo> dataVoList, String type) {
        //获取售前方案
        DiPreSale preSale = diPreSaleService.getById(preSaleId);
        if (null == preSale) {
            throw new ServiceException("售前方案不存在");
        }
        //获取售前方案支持
        DiPreSaleSupport preSaleSupport = diPreSaleSupportService.getOne(new LambdaQueryWrapper<DiPreSaleSupport>().eq(DiPreSaleSupport::getDiPreSaleId, preSale.getId()));
        if (null == preSaleSupport) {
            throw new ServiceException("售前方案支持不存在");
        }
        //获取售前方案物料清单
        List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleService.list(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportId, preSaleSupport.getId()));
        if (CollectionUtil.isEmpty(diPreSaleSupportModules)) {
            throw new ServiceException("售前方案物料清单不存在");
        }
        //获取物料信息
        List<String> materialNoList = diPreSaleSupportModules.stream().map(DiPreSaleSupportModule::getMaterialCode).toList();
        if (CollectionUtil.isEmpty(materialNoList)) {
            throw new ServiceException("售前方案未绑定物料信息");
        }
        //获取物料信息
        Map<String, DiMateriel> materielMap = getMaterielMapByCode(materialNoList);
        if (MaterialConstants.STR_ONE.equals(type)) {
            //构建询价单与询价单明细数据
            inquiryForm.setProjectNo(preSale.getProjectCode());
            inquiryForm.setPreSaleId(preSale.getId());
            inquiryForm.setPreSaleCode(preSale.getPreSaleCode());
            inquiryForm.setExpectCompleteTime(Date.from(preSaleSupport.getExpecteFinishDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            diPreSaleSupportModules.forEach(diPreSaleSupportModule -> {
                //获取物料信息
                DiMateriel diMateriel = materielMap.get(diPreSaleSupportModule.getMaterialCode());
                DiMaterialInquiryDetail inquiryDetail = new DiMaterialInquiryDetail();
                inquiryDetail.setMaterialId(diMateriel.getId());
                inquiryDetail.setMaterielNo(diPreSaleSupportModule.getMaterialCode());
                inquiryDetail.setIsNew(MaterialConstants.STR_TWO);
                inquiryDetail.setExpectCompleteTime(Date.from(preSaleSupport.getExpecteFinishDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                inquiryDetail.setConsumeQuantity(diPreSaleSupportModule.getUseNum());
                inquiryDetailList.add(inquiryDetail);
            });
        }
        if (MaterialConstants.STR_TWO.equals(type)) {
            //获取物料库存信息
            Map<Long, DiMaterialStockInfo> materielStockMap = commonService.getMaterialStockMapByIds(materielMap.values().stream().map(DiMateriel::getId).toList());
            diPreSaleSupportModules.forEach(diPreSaleSupportModule -> {
                //获取物料信息
                DiMateriel diMateriel = materielMap.get(diPreSaleSupportModule.getMaterialCode());
                //构建售前询价物料清单数据
                MaterialInquiryListDataVo dataVo = new MaterialInquiryListDataVo();
                dataVo.setMaterielNo(diPreSaleSupportModule.getMaterialCode());
                dataVo.setMaterielName(diMateriel.getMaterielName());
                dataVo.setBrand(diMateriel.getBrand());
                dataVo.setPatternNo(diMateriel.getPatternNo());
                dataVo.setProductStandard(diMateriel.getProductStandard());
                dataVo.setMaterielProperty(diMateriel.getMaterielProperty());
                dataVo.setExpectCompleteTime(preSaleSupport.getExpecteFinishDate());
                dataVo.setConsumeQuantity(diPreSaleSupportModule.getUseNum());
                if (CollectionUtil.isNotEmpty(materielStockMap) && materielStockMap.containsKey(diMateriel.getId())) {
                    dataVo.setMaterielStock(materielStockMap.get(diMateriel.getId()).getMaterialStock());
                }
                dataVoList.add(dataVo);
            });
        }
    }

    /**
     * 构建研发需求物料询价明细
     *
     * @param preSaleId         售前方案ID
     * @param inquiryForm       询价单
     * @param inquiryDetailList 询价明细
     * @param type              1.新增使用, 2查询使用
     */
    private void formRdNeedInquiryDetailData(Long preSaleId, DiMaterialInquiryForm inquiryForm,
                                             List<DiMaterialInquiryDetail> inquiryDetailList,
                                             List<MaterialInquiryListDataVo> dataVoList, String type) {
        //获取研发需求
        DiRdNeed rdNeed = diRdNeedService.getById(preSaleId);
        if (null == rdNeed) {
            throw new ServiceException("研发需求不存在");
        }
        //获取研发方案物料清单
        List<DiRdNeedModule> rdNeedModules = diRdNeedModuleService.list(new LambdaQueryWrapper<DiRdNeedModule>().eq(DiRdNeedModule::getRdNeedId, rdNeed.getId()));
        if (CollectionUtil.isEmpty(rdNeedModules)) {
            throw new ServiceException("研发需求物料清单不存在");
        }
        //获取物料信息
        List<String> materialNoList = rdNeedModules.stream().map(DiRdNeedModule::getMaterialCode).toList();
        if (CollectionUtil.isEmpty(materialNoList)) {
            throw new ServiceException("研发需求未绑定物料信息");
        }
        //获取物料信息
        Map<String, DiMateriel> materielMap = getMaterielMapByCode(materialNoList);
        if (MaterialConstants.STR_ONE.equals(type)) {
            //构建询价单与询价单明细数据
            inquiryForm.setProjectNo(rdNeed.getProjectCode());
            inquiryForm.setOrderNo(rdNeed.getOrderCode());
            inquiryForm.setPreSaleCode(rdNeed.getRdNeedCode());
            rdNeedModules.forEach(rdNeedModule -> {
                //获取物料信息
                DiMateriel diMateriel = materielMap.get(rdNeedModule.getMaterialCode());
                DiMaterialInquiryDetail inquiryDetail = new DiMaterialInquiryDetail();
                inquiryDetail.setMaterialId(diMateriel.getId());
                inquiryDetail.setMaterielNo(rdNeedModule.getMaterialCode());
                inquiryDetail.setIsNew(MaterialConstants.STR_TWO);
                inquiryDetail.setConsumeQuantity(rdNeedModule.getUseNum());
                inquiryDetailList.add(inquiryDetail);
            });
        }
        if (MaterialConstants.STR_TWO.equals(type)) {
            //获取物料库存信息
            Map<Long, DiMaterialStockInfo> materielStockMap = commonService.getMaterialStockMapByIds(materielMap.values().stream().map(DiMateriel::getId).toList());
            rdNeedModules.forEach(rdNeedModule -> {
                //获取物料信息
                DiMateriel diMateriel = materielMap.get(rdNeedModule.getMaterialCode());
                //构建售前询价物料清单数据
                MaterialInquiryListDataVo dataVo = new MaterialInquiryListDataVo();
                dataVo.setBrand(diMateriel.getBrand());
                dataVo.setPatternNo(diMateriel.getPatternNo());
                dataVo.setProductStandard(diMateriel.getProductStandard());
                dataVo.setMaterielProperty(diMateriel.getMaterielProperty());
                dataVo.setConsumeQuantity(rdNeedModule.getUseNum());
                dataVo.setMaterielNo(rdNeedModule.getMaterialCode());
                dataVo.setMaterielName(diMateriel.getMaterielName());
                if (CollectionUtil.isNotEmpty(materielStockMap) && materielStockMap.containsKey(diMateriel.getId())) {
                    dataVo.setMaterielStock(materielStockMap.get(diMateriel.getId()).getMaterialStock());
                }
                dataVoList.add(dataVo);
            });
        }
    }

    /**
     * 获取物料信息
     *
     * @param materialNoList 物料编号集合
     * @return Map<物料编号, 物料信息>
     */
    private Map<String, DiMateriel> getMaterielMapByCode(List<String> materialNoList) {
        List<DiMateriel> materielList = diMaterielService.queryMaterielByNos(materialNoList.stream().distinct().collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(materielList)) {
            throw new ServiceException("物料不存在");
        }
//        if (materialNoList.size() != materielList.size()) {
//            throw new ServiceException("物料信息不正确");
//        }
        //将物料转为Map<物料编号, 物料信息>
        return materielList.stream().collect(Collectors.toMap(DiMateriel::getMaterielNo, Function.identity()));
    }

    @Override
    public void inquiryReject(String id) {
        //根据id查询询价单
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(id);
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
        updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
        updateWrapper.set(DiMaterialInquiryForm::getQuotedStatus, 4);
        if (StringUtils.isBlank(inquiryForm.getAcceptanceBy())) {
            updateWrapper.set(DiMaterialInquiryForm::getAcceptanceBy, SecurityUtils.getUsername());
            updateWrapper.set(DiMaterialInquiryForm::getIsAcceptance, MaterialConstants.ONE);
        }
        updateWrapper.update();

        List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId()));
        selectionService.rejectSelection(detailList.stream().map(DiMaterialInquiryDetail::getSelectionId).toList());
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_REJECT_NOTICE.getTitle(), inquiryForm.getMaterialInquiryNo());
        String content = StrUtil.format(DingTalkEnum.INQUIRY_SHEET_REJECT_NOTICE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        diMessageList.setSendingUser(inquiryForm.getCreateBy());
        diMessageListService.insertDiMessageList(diMessageList);
        //手动新增的询价单驳回不删除待办
        if (!inquiryForm.getInquirySource().equals("3")) {
            //删除待办
            AgencyTaskInfoDTO agencyTaskInfoDto = getAgencyTaskInfoDTO(inquiryForm);
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateInquiryForm(UpdateDiMaterialInquiryFormDto dto) {
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(dto.getId());
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId())
                .eq(DiMaterialInquiryDetail::getIsDeleted, 0));
        if (CollectionUtil.isEmpty(detailList)) {
            throw new ServiceException("改询价单下未获取到明细");
        }
        List<UpdateDiMaterialInquiryDetailDto> updateDtoList = dto.getDetailDto().stream().filter(detailDto -> null != detailDto.getId()).toList();
        List<UpdateDiMaterialInquiryDetailDto> saveDtoList = dto.getDetailDto().stream().filter(detailDto -> null == detailDto.getId()).toList();
        List<DiMaterialInquiryDetail> updateList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(updateDtoList)) {
            Map<Long, UpdateDiMaterialInquiryDetailDto> updateDtoMap = updateDtoList.stream().collect(Collectors.toMap(UpdateDiMaterialInquiryDetailDto::getId, Function.identity()));
            detailList.forEach(detail -> {
                if (updateDtoMap.containsKey(Long.valueOf(detail.getId()))) {
                    UpdateDiMaterialInquiryDetailDto updateDto = updateDtoMap.get(Long.valueOf(detail.getId()));
//                    if (!updateDto.getMaterialId().equals(detail.getMaterialId())) {
//                        DiMaterialInquiryDetail updateDetail = MaterialConvert.INSTANCE.inquiryDetailUpdateDtoConvertEntity(updateDto);
//                        updateList.add(updateDetail);
//                    }
                    DiMaterialInquiryDetail updateDetail = MaterialConvert.INSTANCE.inquiryDetailUpdateDtoConvertEntity(updateDto);
                    updateList.add(updateDetail);
                } else {
                    detail.setIsDeleted(1);
                    updateList.add(detail);
                }
            });
        } else {
            detailList.forEach(detail -> {
                detail.setIsDeleted(1);
                updateList.add(detail);
            });
        }
        LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
        updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
        updateWrapper.set(DiMaterialInquiryForm::getExpectCompleteTime, dto.getExpectCompleteTime());
        updateWrapper.update();
        if (CollectionUtil.isNotEmpty(updateList)) {
            iDiMaterialInquiryDetailService.updateBatchById(updateList);
        }
        if (CollectionUtil.isNotEmpty(saveDtoList)) {
            List<DiMaterialInquiryDetail> saveList = MaterialConvert.INSTANCE.inquiryDetailUpdateDtoConvertEntityList(saveDtoList);
            saveList.forEach(inquiryDetail -> inquiryDetail.setMaterialInquiryId(Long.valueOf(inquiryForm.getId())));
            iDiMaterialInquiryDetailService.saveBatch(saveList);
        }
        AfterCommitExecutor.submit(() -> updateQuoted(inquiryForm),
                (e) -> log.error("报价单生成失败", e));
        return inquiryForm.getId();
    }

    public void updateQuoted(DiMaterialInquiryForm inquiryForm) {
        DiMaterialQuotedForm quotedForm = diMaterialQuotedFormService.getOne(new LambdaQueryWrapper<DiMaterialQuotedForm>()
                .eq(DiMaterialQuotedForm::getMaterialInquiryId, inquiryForm.getId()));
        if (null == quotedForm) {
            log.info("DiMaterialInquiryFormServiceImpl---updateQuoted()---根据物料询价单ID未获取到报价单数据，物料询价单ID：{}", inquiryForm.getId());
            return;
        }
        diMaterialQuotedDetailService.deleteQuotedDetailByQuotedId(Long.valueOf(quotedForm.getId()));
        diMaterialQuotedFormService.deleteQuotedFormById(Long.valueOf(quotedForm.getId()));
        List<DiMaterialInquiryDetail> detailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialInquiryId, inquiryForm.getId())
                .eq(DiMaterialInquiryDetail::getIsDeleted, 0));
        if (CollectionUtil.isEmpty(detailList)) {
            log.info("DiMaterialInquiryFormServiceImpl---updateQuoted()---根据物料询价单ID未获取到询价明细，物料询价单ID：{}", inquiryForm.getId());
            return;
        }
        //将询价单实体转为报价单实体
        DiMaterialQuotedForm saveQuotedForm = MaterialConvert.INSTANCE.inquiryFormConvertQuotedForm(inquiryForm);
        //生成报价单号
        saveQuotedForm.setMaterialQuotedNo(sequenceService.getSequenceNo("DYD-BJ"));
        saveQuotedForm.setQuotedSource(inquiryForm.getInquirySource());
        saveQuotedForm.setMaterialInquiryId(Long.valueOf(inquiryForm.getId()));
        //新增报价单
        diMaterialQuotedFormService.save(saveQuotedForm);
        //将询价单明细实体列表转为报价单明细实体列表
        List<DiMaterialQuotedDetail> quotedDetailList = new ArrayList<>();
        detailList.forEach(inquiryDetail -> {
            DiMaterialQuotedDetail quotedDetail = MaterialConvert.INSTANCE.inquiryDetailConvertQuotedDetail(inquiryDetail);
            //完善数据
            quotedDetail.setQuotedExplain(inquiryDetail.getInquiryExplain());
            quotedDetail.setMaterialQuotedId(Long.valueOf(saveQuotedForm.getId()));
            quotedDetail.setMaterialInquiryDetailId(Long.valueOf(inquiryDetail.getId()));
            quotedDetailList.add(quotedDetail);
        });
        //新增报价单明细
        diMaterialQuotedDetailService.saveBatch(quotedDetailList);
    }

    public void sendMessage(String title, String content, String sendUser) {
        //发送钉钉消息
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        diMessageList.setSendingUser(sendUser);
        diMessageListService.insertDiMessageList(diMessageList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAcceptanceBy(OperationInquiryFormDTO dto) {
        //根据id查询询价单
        DiMaterialInquiryForm inquiryForm = diMaterialInquiryFormMapper.selectById(dto.getId());
        if (null == inquiryForm) {
            throw new ServiceException("询价单不存在");
        }
        if (MaterialConstants.THREE.equals(inquiryForm.getQuotedStatus())) {
            throw new ServiceException("当前状态无法设置受理人");
        }
        LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
        updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
        updateWrapper.set(DiMaterialInquiryForm::getAcceptanceBy, SecurityUtils.getUsername());
        updateWrapper.set(DiMaterialInquiryForm::getIsAcceptance, MaterialConstants.ONE);
        updateWrapper.update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncInquiryFormData() {
        //获取目前所有询价单数据
        List<DiMaterialInquiryForm> inquiryFormList = this.list();
        if (CollectionUtil.isEmpty(inquiryFormList)) {
            log.info("DiMaterialInquiryFormServiceImpl---syncInquiryFormData()---未获取到询价单数据");
            return;
        }
        //获取方案ID
        List<Long> preSaleIdList = inquiryFormList.stream().map(DiMaterialInquiryForm::getPreSaleId).filter(Objects::nonNull).distinct().toList();
        //获取产品方案
        List<DiPreSale> preSaleList = diPreSaleService.listByIds(preSaleIdList);
        if (CollectionUtil.isEmpty(preSaleList)) {
            log.info("DiMaterialInquiryFormServiceImpl---syncInquiryFormData()---未获取到产品方案，产品方案ID：{}", JSONUtil.toJsonStr(preSaleIdList));
            return;
        }
        Map<Long, DiPreSale> preSaleMap = preSaleList.stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));
        inquiryFormList.forEach(inquiryForm -> {
            boolean isUpdate = false;
            LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
            updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
            if (null != inquiryForm.getPreSaleId() && preSaleMap.containsKey(inquiryForm.getPreSaleId())) {
                isUpdate = true;
                DiPreSale preSale = preSaleMap.get(inquiryForm.getPreSaleId());
                if (null != preSale.getNicheId()) {
                    updateWrapper.set(DiMaterialInquiryForm::getNicheId, preSale.getNicheId());
                }
                if (StringUtils.isNotBlank(preSale.getNicheCode())) {
                    updateWrapper.set(DiMaterialInquiryForm::getNicheNo, preSale.getNicheCode());
                }
            }
            if (MaterialConstants.THREE.equals(inquiryForm.getQuotedStatus())) {
                isUpdate = true;
                updateWrapper.set(DiMaterialInquiryForm::getIsAcceptance, MaterialConstants.ONE);
                updateWrapper.set(DiMaterialInquiryForm::getAcceptanceBy, inquiryForm.getUpdateBy());
                updateWrapper.set(DiMaterialInquiryForm::getOfferBy, inquiryForm.getUpdateBy());
            }
            if (isUpdate) {
                updateWrapper.update();
            }
        });
    }

    @Override
    public void giveUpInquiryForm(String preSaleCode) {
        List<DiMaterialInquiryForm> list = this.lambdaQuery().eq(DiMaterialInquiryForm::getPreSaleCode, preSaleCode).in(DiMaterialInquiryForm::getQuotedStatus, 1, 2)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(inquiryForm -> {
                LambdaUpdateChainWrapper<DiMaterialInquiryForm> updateWrapper = new LambdaUpdateChainWrapper<>(diMaterialInquiryFormMapper);
                updateWrapper.eq(DiMaterialInquiryForm::getId, inquiryForm.getId());
                updateWrapper.set(DiMaterialInquiryForm::getQuotedStatus, 5);
                updateWrapper.update();
                //删除待办
                agencyTaskService.clearTask(inquiryForm.getMaterialInquiryNo());
            });
        }
    }

}
