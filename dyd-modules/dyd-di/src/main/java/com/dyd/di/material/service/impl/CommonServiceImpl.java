package com.dyd.di.material.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.constant.DestinationConstants;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.exception.auth.NotPermissionException;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.datascope.consts.DataListEnum;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.material.constants.MaterialConstants;
import com.dyd.di.material.domain.dto.FindMaterialStockDto;
import com.dyd.di.material.domain.vo.DiMaterialStorageVo;
import com.dyd.di.material.entity.DiMaterialStockInfo;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.material.service.DiMaterialStockInfoService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.domain.DiMaterielVersion;

import com.dyd.di.materiel.service.IDiMaterielService;
import com.dyd.di.process.domain.DiProjectModify;
import com.dyd.di.process.domain.DiProjectRelationUser;
import com.dyd.di.process.pojo.dto.ProcessRelationUserMessageDTO;
import com.dyd.di.process.service.DiProjectRelationUserService;
import com.dyd.exchange.RemoteExchangeService;
import com.dyd.exchange.model.ErpItemStoreResponse;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.domain.SysUserRoleData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公共Service实现类
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Resource
    private IDiMaterielService diMaterielService;


    @Resource
    private DiMaterialStockInfoService diMaterialStockInfoService;

    @Resource
    private RemoteDbcService remoteDbcService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private RemoteSysService remoteSysService;

    @Resource
    private DiProjectRelationUserService diProjectRelationUserService;

    @Autowired
    private RemoteExchangeService remoteExchangeService;

    /**
     * 获取物料信息
     *
     * @param idList 主键集合
     * @return map<主键, 物料信息>
     */
    @Override
    public Map<Long, DiMateriel> getMaterielMapByIds(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }
        List<DiMateriel> materielList = diMaterielService.listByIds(idList);
        if (CollectionUtil.isEmpty(materielList)) {
            return null;
        }
        return materielList.stream().collect(Collectors.toMap(DiMateriel::getId, Function.identity()));
    }

//    /**
//     * 根据物料版本ID获取物料版本信息
//     *
//     * @param idList 主键集合
//     * @return map<主键, 物料版本信息>
//     */
//    @Override
//    public Map<Long, DiMaterielVersion> getMaterielVersionMapByIds(List<String> idList) {
//        if (CollectionUtil.isEmpty(idList)) {
//            return null;
//        }
//        List<DiMaterielVersion> materielVersionList = diMaterielVersionService.listByIds(idList);
//        if (CollectionUtil.isEmpty(materielVersionList)) {
//            return null;
//        }
//        return materielVersionList.stream().collect(Collectors.toMap(DiMaterielVersion::getId, Function.identity()));
//    }

    /**
     * 获取物料库存
     *
     * @param idList 物料主键集合
     * @return map<物料主键, 物料信息>
     */
    @Override
    public Map<Long, DiMaterialStockInfo> getMaterialStockMapByIds(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }

        /*List<DiMaterialStockInfo> stockInfoList = diMaterialStockInfoService.list(new LambdaQueryWrapper<DiMaterialStockInfo>().in(DiMaterialStockInfo::getMaterialId, idList));
        if (CollectionUtil.isEmpty(stockInfoList)) {
            return null;
        }*/
        List<DiMateriel> diMateriels = diMaterielService.getBaseMapper().selectList(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, idList));
        List<DiMaterialStockInfo> stockInfoList = new ArrayList<>();
        for (DiMateriel diMateriel : diMateriels) {
            R<List<ErpItemStoreResponse>> itemStoreR = remoteExchangeService.selectItemStoreQty(diMateriel.getMaterielNo());
            if (itemStoreR.isSuccess() && CollectionUtil.isNotEmpty(itemStoreR.getData())) {
                DiMaterialStockInfo diMaterialStockInfo = new DiMaterialStockInfo();
                diMaterialStockInfo.setMaterialId(diMateriel.getId());
                diMaterialStockInfo.setMaterialStock(Objects.nonNull(itemStoreR.getData().get(0).getStoreQty()) ? itemStoreR.getData().get(0).getStoreQty().intValue() : 0);
                stockInfoList.add(diMaterialStockInfo);
            }
        }
        return stockInfoList.stream().collect(Collectors.toMap(DiMaterialStockInfo::getMaterialId, Function.identity()));
    }

    /**
     * 根据工号获取用户姓名
     *
     * @param createByJobList 创建人工号列表
     * @param updateByJobList 修改人工号列表
     * @return 结果Map<工号job, 姓名name></>
     */
    @Override
    public Map<String, String> getUserNameByJob(List<String> createByJobList, List<String> updateByJobList) {
        List<String> jobList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(createByJobList)) {
            jobList.addAll(createByJobList);
        }
        if (CollectionUtil.isNotEmpty(updateByJobList)) {
            jobList.addAll(updateByJobList);
        }
        if (CollectionUtil.isEmpty(jobList)) {
            return null;
        }
        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(jobList.stream().distinct().toList()).build());
        if (!userListResult.isSuccess()) {
            return null;
        }
        return userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
    }

    /**
     * 将人员加入到项目参与者中
     *
     * @param projectNo     项目编号
     * @param personnelList 需要加入到项目中的人员
     */
    @Override
    public void personnelSaveProject(String projectNo, List<String> personnelList, String status) {
        ProcessRelationUserMessageDTO processRelationUserMessageDTO = new ProcessRelationUserMessageDTO();
        processRelationUserMessageDTO.setProjectNo(projectNo);
        processRelationUserMessageDTO.setStatus(status);
        processRelationUserMessageDTO.setRelationUserList(personnelList);
        rocketMQTemplate.syncSend(DestinationConstants.RELATION_USER_TOPIC_TAG, processRelationUserMessageDTO);
    }

    @Override
    public List<String> findLoginJobNumberList() {
        return commonFindLoginJobNumberList();
    }

    @Override
    public String findLoginJobNumberStrSql() {
        List<String> jobNumberList = commonFindLoginJobNumberList();
        if (CollectionUtil.isEmpty(jobNumberList)) {
            return null;
        }
        if (jobNumberList.size() == MaterialConstants.ONE) {
            return "'[" + jobNumberList.get(MaterialConstants.ZERO) + "]'";
        }
        //循环构建工号SQL
        return jobNumberList.stream().map(jobNumber -> StringUtils.isNotBlank(jobNumber) ? "'[" + jobNumber + "]'" : null).collect(Collectors.joining(","));
    }

    private List<String> commonFindLoginJobNumberList() {
        //当前登录人id
        Long userid = SecurityUtils.getLoginUser().getUserid();
        R<SysUserRoleData> sysUserDataSource = remoteSysService.getSysUserDataSource(userid);
        if (sysUserDataSource.isError()) {
            throw new ServiceException("获取登录人信息失败");
        }
        //判断当亲登录者数据可见范围
        if (MaterialConstants.ZERO.equals(sysUserDataSource.getData().getType())) {
            //如果是限制范围的，构建登录人工号已经他下属层级工号
            return sysUserDataSource.getData().getUserDataList().stream().map(SysUserRoleData.roleData::getJobNumber).toList();
        }
        return Lists.newArrayList();
    }


    public void checkDetailPurview(String createBy, String relationNo, String projectNo) {
        //判断是不是本人访问
        if (StringUtils.isNotBlank(createBy) && createBy.equals(SecurityUtils.getUsername())) {
            //本人访问
            return;
        }
        //获取登录者数据权限
        List<String> jobNumberList = findLoginJobNumberList();
        //判断是否为空，为空证明登录者有全部数据权限，不为空进行判断
        if (CollectionUtil.isEmpty(jobNumberList)) {
            //登录者有所有数据权限
            return;
        }
        log.info("获取到的权限工号信息：{}业务编号：{}", JSONUtil.toJsonStr(jobNumberList), relationNo);
        //判断是不是下级员工创建的合同
        if (jobNumberList.contains(createBy)) {
            //下级员工所建合同
            return;
        }
        //判断是不是项目参与者
        DiProjectRelationUser projectRelationUser = diProjectRelationUserService.getOne(new LambdaQueryWrapper<DiProjectRelationUser>().eq(DiProjectRelationUser::getProjectNo, projectNo));
        if (null == projectRelationUser || StringUtils.isBlank(projectRelationUser.getRelationUsers())) {
            throw new NotPermissionException();
        }
        List<String> havePurviewList = jobNumberList.stream().filter(jobNumber -> projectRelationUser.getRelationUsers().contains(jobNumber)).toList();
        if (CollectionUtil.isEmpty(havePurviewList)) {
            throw new NotPermissionException();
        }
        log.info("有权限的用户工号：{}", JSONUtil.toJsonStr(havePurviewList));
    }

    @Override
    public SysUserRoleData findLoginJobNumberList(String dataListEnum, Long userid) {
        R<SysUserRoleData> sysUserDataSource = remoteSysService.getSysUserDataSourceV2(dataListEnum, userid);
        if (sysUserDataSource.isError()) {
            throw new ServiceException("获取登录人信息失败");
        }
        if(sysUserDataSource.getData()==null){
            var data= new SysUserRoleData();
            data.setType(0);
            data.setUserDataList(Lists.newArrayList());
            return data;
        }
        return sysUserDataSource.getData();
    }
}
