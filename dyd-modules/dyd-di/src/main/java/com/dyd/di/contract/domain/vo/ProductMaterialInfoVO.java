package com.dyd.di.contract.domain.vo;

import com.dyd.di.oss.FileVo;
import com.dyd.di.pre.entity.DiPreSaleLabel;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 产品方案物料信息
 */
@Data
public class ProductMaterialInfoVO {

    /**
     * 产品方案ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 商机ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nicheId;

    /**
     * 商机编号
     */
    private String nicheNo;

    /**
     * 产品方案编号
     */
    private String preSaleCode;

    /**
     * 方案名称
     */
    private String preSaleName;

    /**
     * 产品方案类型：1.标准品，2.非标准品，3.贸易类
     */
    private Integer preSaleType;

    /**
     * 方案类型 1:项目 2:备件 3:燃配 4:出口  5:标准品
     */
    private String orderType;

    /**
     * 方案类型中文描述
     */
    private String orderTypeName;

    /**
     * 物料号
     */
    private String materialNo;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 显示的版本号
     */
    private String showVersionNo;

    /**
     * 需求数量
     */
    private Integer demandNum;

    /**
     * 物料库存
     */
    private Integer materialStock;

    /**
     * 理论成本小计(产品方案清单费用合计)
     */
    private BigDecimal feeTotal;

    /**
     * 单套理论成本(亚杰那边怎么算的这里就是怎么算的)
     */
    private BigDecimal costFee;

    /**
     * 理论成本合计(亚杰那边怎么算的这里就是怎么算的)
     */
    private BigDecimal costFeeSum;

    /**
     * 单套理论交期（天）(现在取的是售前方案表中的指导工期字段)
     */
    private String guideDuration;

    /**
     * 售价报价(报价明细)
     */
    private BigDecimal saleQuote;

    /**
     * 交期报价(报价明细)
     */
    private BigDecimal deliveryQuote;

    /**
     * 毛利额(报价明细)
     */
    private BigDecimal grossProfit;

    /**
     * 毛利率(报价明细)
     */
    private BigDecimal grossMargin;

    /**
     * 国家
     */
    @JsonProperty("countryName")
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    @JsonProperty("provinceName")
    private String province;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市
     */
    @JsonProperty("cityName")
    private String city;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区
     */
    @JsonProperty("areaName")
    private String area;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 销售服务报价
     */
    private BigDecimal saleServiceQuote;

    /**
     * 销售服务周期报价（人/天）
     */
    private BigDecimal saleServiceCycle;

    /**
     * 单套理论服务费
     */
    private BigDecimal systemServiceFee;

    /**
     * 单套服务理论周期
     */
    private BigDecimal systemServiceCycle;

    /**
     * 方案交付日期(项目期望交付日期)
     */
    private String expecteDate;

    /**
     * 包装方式费用(方案表)
     */
    private BigDecimal packageType;

    /**
     * 现场安装杂费(方案表)
     */
    private BigDecimal installationFee;

    /**
     * 标准成本(现在取的是物料价格表中的合计指导字段)
     */
    private BigDecimal guideSumCosts;

    /**
     * 合计成本
     */
    private BigDecimal costSumCosts;

    /**
     * 图纸文件Map,key:文件类型,value:文件key
     */
    private Map<String, List<String>> drawingFileKeyMap;

    /**
     * 文件信息Map
     */
    private Map<String, List<FileVo>> fileInfoMap;

    /**
     * 产品参数配置信息
     */
    private List<DiPreSaleLabel> labelList;

    /**
     * 图片数据Map,key:图片类型,value:图片数据(BASE64)[附件模板使用]---项目类
     */
    private Map<String, List<String>> pictureDataMap;

    /**
     * 参数配置Map,key:参数类型,value:参数配置信息(这里组建成了动态表单数据)[附件模板使用]---项目类
     */
    private Map<String, List<List<AnnexTemplateParamJoinVO>>> paramInfoDataMap;

    /**
     * 贸易清单---贸易类
     */
    private List<TradeListInfoVO> tradeList;

    /**
     * 技术支持负责人代码
     */
    private String techSupportOwnerCode;

    /**
     * 技术支持负责人名称
     */
    private String techSupportOwnerName;

}
