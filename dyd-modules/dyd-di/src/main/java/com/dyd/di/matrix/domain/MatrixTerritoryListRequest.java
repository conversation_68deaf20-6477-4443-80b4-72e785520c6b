package com.dyd.di.matrix.domain;

import lombok.Data;

/**
 * 业务版图列表请求参数
 */
@Data
public class MatrixTerritoryListRequest {

    /**
     * 分类：1:系统、2:组件、3:配件、4:零件、5:服务、6:工程
     */
    private Integer cateId;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 版图领域id
     */
    private Long territoryDomain;

    /**
     * 版图行业id
     */
    private Long territoryIndustry;

    /**
     * 版图应用id
     */
    private Long territoryApplication;

    /**
     * 版图对象id
     */
    private Long territoryObject;

    /**
     * 版图工艺id
     */
    private Long territoryProcess;

    /**
     * 配件
     */
    private Long territoryPart;

    /**
     * 配件品类
     */
    private Long territoryPartCategory;

    /**
     * 组件品类
     */
    private Long territoryComponent;

    /**
     * 线索或者线索的ID
     */
    private String refId;

    /**
     * 类型: clue为线索；niche为商机
     */
    private String type;

}
