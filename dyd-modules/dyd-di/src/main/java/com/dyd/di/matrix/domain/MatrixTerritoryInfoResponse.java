package com.dyd.di.matrix.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.core.annotation.AutoConvertDict;
import com.dyd.common.core.annotation.Dict;
import com.dyd.di.matrix.vo.MatrixLabelVo;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 业务版图保存/编辑
 */
@Data
public class MatrixTerritoryInfoResponse {

    private Long id;

    /**
     * 版图分类
     */
    private Integer cateId;

    /**
     * 版图分类名
     */
    private String cateIdName;

    /**
     * 负责人
     */
    @Dict(type = Dict.DictType.EMPLOYEE)
    private String owner;
    private String ownerName;

    /**
     * 所属BU
     */
    @Dict(type = Dict.DictType.DEPARTMENT)
    private String dept;
    private String deptName;

    /**
     * 版图领域
     */
    private Long territoryDomain;

    /**
     * 版图领域名
     */
    private String territoryDomainName;

    /**
     * 等级
     */
    private Integer territoryLevel;

    /**
     * 等级名
     */
    private String territoryLevelName;

    /**
     * 版图行业
     */
    private Long territoryIndustry;

    /**
     * 版图行业名
     */
    private String territoryIndustryName;

    /**
     * 版图应用
     */
    private Long territoryApplication;

    /**
     * 版图应用名
     */
    private String territoryApplicationName;

    /**
     * 版图对象
     */
    private Long territoryObject;

    /**
     * 版图对象名
     */
    private String territoryObjectName;

    /**
     * 版图工艺
     */
    private Long territoryProcess;

    /**
     * 组件品类
     */
    private Long territoryComponent;

    /**
     * 组件品类名
     */
    private String territoryComponentName;

    /**
     * 版图工艺名
     */
    private String territoryProcessName;

    /**
     * 配件
     */
    private Long territoryPart;

    /**
     * 配件名
     */
    private String territoryPartName;

    /**
     * 配件品类
     */
    private Long territoryPartCategory;

    /**
     * 配件品类名
     */
    private String territoryPartCategoryName;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 二级领域
     */
    private String domainTwoLevel;

    /**
     * 行业描述
     */
    private String industryDesc;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 图镇关联
     */
    private List<DiMatrixLabel> diMatrixLabels;

    /**
     * 技术范围
     */
    private List<DiMatrixLabel> diMatrixLabelTs;

    /**
     * 竞品分析
     */
    private MatrixTerritoryCompetitorAnalysis matrixTerritoryCompetitorAnalysis;

    /**
     * 销售策略
     */
    private MatrixTerritoryMarketingStrategy matrixTerritoryMarketingStrategy;


    /**
     * 销售策略
     */
    @Data
    public static class MatrixTerritoryMarketingStrategy{


        /**
         * id
         */
        private Long id;

        /**
         * 销售渠道
         */
        private Integer distributionChannel;

        /**
         * 执行周期
         */
        private LocalDate executionCycle;

        /**
         * 负责人
         */
        private String owner;

        /**
         * 销售策略
         */
        private String salesStrategy;

        /**
         * 价格策略
         */
        private String priceStrategy;

        /**
         * 资源匹配策略
         */
        private String resourceMatchingStrategy;

        /**
         * 产品策略
         */
        private String productStrategy;
    }


    /**
     * 竞品分析
     */
    @Data
    public static class MatrixTerritoryCompetitorAnalysis{
        /**
         * id
         */
        private Long id;

        /**
         * 市场规模
         */
        private Integer marketSize;

        /**
         * 行业趋势
         */
        private Integer industryTrends;

        /**
         * GP2利润空间
         */
        private Integer gp2ProfitMargin;

        /**
         * 客户分析
         */
        private String customerAnalysis;
    }
}
