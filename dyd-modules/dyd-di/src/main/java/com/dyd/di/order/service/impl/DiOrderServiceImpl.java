package com.dyd.di.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.constant.DestinationConstants;
import com.dyd.common.core.constant.HttpStatus;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.RelationTypeEnum;
import com.dyd.common.core.enums.SequenceEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.agency.enums.ApprovalNoticeEnum;
import com.dyd.di.api.model.*;
import com.dyd.di.contract.domain.request.FindContractRequest;
import com.dyd.di.contract.domain.response.ContractListPageResponse;
import com.dyd.di.contract.entity.DiContract;
import com.dyd.di.contract.entity.DiContractBillPeriod;
import com.dyd.di.contract.mapper.DiContractMapper;
import com.dyd.di.contract.service.DiContractBillPeriodService;
import com.dyd.di.contract.service.DiContractService;
import com.dyd.di.eventbus.utils.EventBusUtils;
import com.dyd.di.home.domain.DdUser;
import com.dyd.di.home.mapper.DdUserMapper;
import com.dyd.di.marketing.domain.DiMarketingContacts;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.domain.DiMarketingNicheDemand;
import com.dyd.di.marketing.enums.NicheDingTalkEnum;
import com.dyd.di.marketing.service.DiMarketingNicheDemandService;
import com.dyd.di.marketing.service.IDiMarketingContactsService;
import com.dyd.di.marketing.service.IDiMarketingCustomerService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.mapper.DiMaterielMapper;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.order.convert.OrderConvert;
import com.dyd.di.order.domain.*;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.enums.OrderScheduleStatusEnum;
import com.dyd.di.order.enums.OrderStageEnum;
import com.dyd.di.order.enums.OrderStatusEnum;
import com.dyd.di.order.event.*;

import com.dyd.di.order.mapper.*;
import com.dyd.di.order.pojo.*;
import com.dyd.di.order.pojo.dto.*;
import com.dyd.di.order.service.*;
import com.dyd.di.pre.domain.response.OrderListDataResponse;
import com.dyd.di.pre.domain.response.PreSaleQuoteProductCostInfoVO;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.entity.DiPreSaleQuoteDetail;
import com.dyd.di.pre.enums.DingTalkEnum;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.mapper.DiPreSaleManifestMapper;
import com.dyd.di.pre.mapper.DiPreSaleMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteDetailMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.pre.service.IDiPreSaleMaterielSelectionService;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.dyd.di.pre.service.IPreSaleQuoteService;
import com.dyd.di.pre.service.impl.PreSaleQuoteService;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.pojo.dto.ProcessRelationMessageDTO;
import com.dyd.di.process.pojo.dto.RelationTypeDTO;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.process.service.IDiProjectRelationService;
import com.dyd.di.rd.domain.request.RdNeedAddRequest;
import com.dyd.di.rd.service.IDiRdNeedService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.di.syscategory.entity.SysCategory;
import com.dyd.di.syscategory.service.ISysCategoryService;
import com.dyd.di.u9.config.DingtalkU9Config;
import com.dyd.di.u9.domain.DiSynErpLog;
import com.dyd.di.u9.mapper.DiSynErpLogMapper;
import com.dyd.dingtalk.DingtalkClient;
import com.dyd.exchange.RemoteExchangeService;
import com.dyd.exchange.model.UpdateRequireDateRequest;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysDept;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dyd.di.pre.enums.AdvanceExecutionStatusEnum.YES;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
@Slf4j
@RefreshScope
public class DiOrderServiceImpl extends ServiceImpl<DiOrderMapper, DiOrder> implements IDiOrderService {
    @Autowired
    private DiOrderMapper diOrderMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private IDiPreSaleService diPreSaleService;

    @Autowired
    private DiContractService diContractService;
    @Autowired
    private IDiProjectRelationService projectRelationService;

    @Autowired
    private IDiMarketingNicheService marketingNicheService;

    @Autowired
    private IDiMarketingCustomerService marketingCustomerService;

    @Autowired
    private IDiRdNeedService diRdNeedService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private DiContractBillPeriodService diContractBillPeriodService;

    @Autowired
    private IDiOrderBillPeriodService diOrderBillPeriodService;

    @Autowired
    private IDiMarketingContactsService diMarketingContactsService;

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;
    @Autowired
    private IPreSaleQuoteService quotesService;


    @Autowired
    private IDiOrderInstallService diOrderInstallService;

    @Autowired
    private DiOrderElectricDesignService diOrderElectricDesignService;

    @Autowired
    private DiOrderMachineDesignService diOrderMachineDesignService;

    @Autowired
    private DiOrderProduceService diOrderProduceService;

    @Autowired
    private DiOrderMaterielPlanService diOrderMaterielPlanService;

    @Autowired
    private DiOrderProduceBomService diOrderProduceBomService;

    @Autowired
    private DiOrderBillPeriodMapper diOrderBillPeriodMapper;

    @Autowired
    private IDiProcessProjectService diProcessProjectService;
    @Autowired
    private IDiMessageListService diMessageListService;
    @Autowired
    private CommonService commonService;

    @Resource
    private DiOrderQualityService diOrderQualityService;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private IDiPreSaleMaterielSelectionService diPreSaleMaterielSelectionService;

    @Autowired
    private DiOrderDeliveryChangeService diOrderDeliveryChangeService;

    @Autowired
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Resource
    private DingtalkClient dingtalkClient;

    @Resource
    private DingtalkU9Config dingtalkU9Config;

    public static final Integer NOT_NEED_FEE = 1;

    public static final int OPERATE_TYPE_START = 1;

    public static final int OPERATE_TYPE_END = 2;

    @Autowired
    private IDiOrderService iDiOrderService;

    @Autowired
    private ISysCategoryService sysCategoryService;

    @Autowired
    private DiSynErpLogMapper diSynErpLogMapper;

    @Autowired
    private RemoteExchangeService remoteExchangeService;

    @Autowired
    private DiPreSaleMapper diPreSaleMapper;

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private DiMaterielMapper diMaterielMapper;

    @Autowired
    private DiContractMapper diContractMapper;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private com.dyd.di.contract.iservice.IDiContractService contractService;

    @Value("${defaultElectricDesignUserId:dyd479}")
    private String defaultElectricDesignUserId;

    @Value("${defaultPurchaseUserId:dyd086}")
    private String defaultPurchaseUserId;

    @Value("${defaultProduceUserId:LR001}")
    private String defaultProduceUserId;

    @Value("${defaultProduceQualityUserId:dyd142}")
    private String defaultProduceQualityUserId;


    @Value("${order.schedule.default_user:dyd019}")
    private String defaultUser;

    @Value("${order.schedule.trade_default_user:dyd145}")
    private String tradeDefaultUser;

    @Autowired
    private RemoteDictDataService dictDataService;


    @Autowired
    private DiMarketingNicheDemandService demandService;

    @Autowired
    private DiOrderSupportCheckService diOrderSupportCheckService;

    @Autowired
    private DiAgencyTaskService taskService;
    @Autowired
    private DiAgencyApprovalService approvalService;
    @Autowired
    private DiOrderWarningService warningService;

    @Value("${order.create-dingtalk-group:cidgqr4/ENLi3nFqwV0ZhR08w==}")
    private String orderCreateGroupId;


    @Autowired
    private IDiOrderExtService orderExtService;

    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public DiOrder selectDiOrderById(String id) {
        return diOrderMapper.selectDiOrderById(id, null);
    }

    /**
     * 查询订单列表
     *
     * @param diOrder 订单
     * @return 订单
     */
    @Override
    public List<DiOrder> selectDiOrderList(DiOrder diOrder) {
        return diOrderMapper.selectDiOrderList(diOrder);
    }

    /**
     * 新增订单
     *
     * @param diOrder 订单
     * @return 结果
     */
    @Override
    public int insertDiOrder(DiOrder diOrder) {
        diOrder.setCreateTime(DateUtils.getNowDate());
        diOrder.setOrderNo(sequenceService.getSequenceNo(SequenceEnum.DYD_DD.getCode()));
        diOrder.setOrderStatus(OrderStatusEnum.INIT.getCode());
        return diOrderMapper.insertDiOrder(diOrder);
    }

    /**
     * 修改订单
     *
     * @param diOrder 订单
     * @return 结果
     */
    @Override
    public int updateDiOrder(DiOrder diOrder) {
        diOrder.setUpdateTime(DateUtils.getNowDate());
        return diOrderMapper.updateDiOrder(diOrder);
    }

    /**
     * 更新是否提前执行
     *
     * @param request
     */
    @Override
    public void updateOrderAdvanceExecution(DiOrderUpdateRequest request) {

        DiOrder diOrderQuery = diOrderMapper.selectById(request.getId());
        if (Objects.isNull(diOrderQuery)) {
            throw new RuntimeException("无效id");
        }

        DiOrder diOrder = new DiOrder();
        diOrder.setId(request.getId());
        diOrder.setIsAdvanceExecution(request.getIsAdvanceExecution());
        diOrder.setUpdateTime(new Date());
        diOrder.setUpdateBy(SecurityUtils.getUsername());
        diOrderMapper.updateById(diOrder);


        DiContract diContract = diContractMapper.selectOne(Wrappers.<DiContract>lambdaQuery().eq(DiContract::getContractNo, diOrderQuery.getContractNo()));

        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.SUPPLY_PRE_SALE_QUOTE_DING_NOTICE.getTitle(), diContract.getProjectNo());
        String content = StrUtil.format(DingTalkEnum.SUPPLY_PRE_SALE_QUOTE_DING_NOTICE.getMessage(), diContract.getProjectNo());
        List<String> userList = new ArrayList<>();
        userList.add(diOrderQuery.getOwner());
        //获取当前员工直属领导
        List<DdUser> createUserList = ddUserMapper.selectList(new LambdaQueryWrapper<DdUser>().eq(DdUser::getJobNumber, diOrderQuery.getOwner()));
        if (CollectionUtil.isNotEmpty(createUserList)) {
            if (createUserList.size() > 1) {
                log.error("ApprovalResultListener---sendSupplyPreSaleQuote()---根据员工工号在钉钉用户表中获取到多条信息，员工工号：{}", diOrderQuery.getOwner());
            }
            List<DdUser> manageUserList = ddUserMapper.selectList(new LambdaQueryWrapper<DdUser>().eq(DdUser::getUserId, createUserList.get(0).getManagerUserid()));
            if (CollectionUtil.isNotEmpty(manageUserList)) {
                if (manageUserList.size() > 1) {
                    log.error("ApprovalResultListener---sendSupplyPreSaleQuote()---根据领导USERID在钉钉用户表中获取到多条信息，领导USERID：{}", createUserList.get(0).getManagerUserid());
                }
                userList.add(manageUserList.get(0).getJobNumber());
            } else {
                log.error("ApprovalResultListener---sendSupplyPreSaleQuote()---根据领导USERID在未在钉钉员工表中获取到数据，领导USERID：{}", createUserList.get(0).getManagerUserid());
            }
        } else {
            log.error("ApprovalResultListener---sendSupplyPreSaleQuote()---根据员工工号在未在钉钉员工表中获取到数据，员工工号：{}", diOrderQuery.getOwner());
        }
        orderCommonService.sendDingTalkMessage("订单", title, content, userList, null);
        //往建单群中发送消息
        dingtalkClient.sendGroupText("项目ID：" + diContract.getProjectNo(), content, "cidgqr4/ENLi3nFqwV0ZhR08w==", dingtalkU9Config.getAppKey(), dingtalkU9Config.getAppSecret());


        OrderAdvanceExecutionEvent event = new OrderAdvanceExecutionEvent();
        event.setOrderId(diOrder.getId());
        EventBusUtils.publishEvent(event);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteDiOrderByIds(String[] ids) {
        return diOrderMapper.deleteDiOrderByIds(ids);
    }

    /**
     * classification
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteDiOrderById(String id) {
        return diOrderMapper.deleteDiOrderById(id);
    }

    @Override
    public TableDataInfo<OrderListDTO> orderList(OrderListVo orderList) {
//        //数据权限处理
        List<String> jobNumberList = commonService.findLoginJobNumberList();
        if (CollectionUtils.isNotEmpty(jobNumberList)) {
            orderList.setJobNumberList(jobNumberList);
        }

        //判断是否查询了技术难度和技术负责人,如果查询了,判断是否查询出了对应的询价单数据
        List<String> preSaleQuoteCodeList = null;
        if (CollectionUtil.isNotEmpty(orderList.getIndustryCategoriesList()) || CollectionUtil.isNotEmpty(orderList.getTechSupportOwnerCodeList())) {
            List<OrderListDataResponse> preSaleQuoteDataList = diPreSaleQuoteDetailMapper.findQuoteNoByCondition(orderList.getIndustryCategoriesList(), orderList.getTechSupportOwnerCodeList(), null);
            if (CollectionUtil.isEmpty(preSaleQuoteDataList)) {
                return new TableDataInfo(Lists.newArrayList(), 0);
            }
            preSaleQuoteCodeList = preSaleQuoteDataList.stream().map(OrderListDataResponse::getPreSaleQuoteCode).toList();
        }

        PageHelper.startPage(orderList.getPageNum(), orderList.getPageSize());
        List<DiOrder> diOrderList = diOrderMapper.findOrderListPage(orderList, preSaleQuoteCodeList);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setTotal(new PageInfo(diOrderList).getTotal());
        if (CollectionUtils.isEmpty(diOrderList)) {
            return new TableDataInfo(Lists.newArrayList(), 0);
        }
        List<OrderListDTO> orderListDTOList = OrderConvert.INSTANCE.entityToOrderListDTO(diOrderList);
        Map<String, String> userNameMap = Maps.newHashMap();
        List<String> ownerList = orderListDTOList.stream().map(OrderListDTO::getOwner).filter(StringUtils::isNotBlank).toList();
        if (CollectionUtils.isNotEmpty(ownerList)) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(ownerList).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            }
        }
        List<String> preSaleQuoteNoList = orderListDTOList.stream().map(OrderListDTO::getPreSaleQuoteNo).filter(StringUtils::isNotBlank).toList();
        Map<String, OrderListDataResponse> preSaleQuoteDataMap;
        if (CollectionUtil.isNotEmpty(preSaleQuoteNoList)) {
            List<OrderListDataResponse> preSaleQuoteList = diPreSaleQuoteDetailMapper.findQuoteNoByCondition(null, null, preSaleQuoteNoList);
            if (CollectionUtil.isNotEmpty(preSaleQuoteList)) {
                preSaleQuoteDataMap = preSaleQuoteList.stream().collect(Collectors.toMap(OrderListDataResponse::getPreSaleQuoteCode, Function.identity()));
            } else {
                preSaleQuoteDataMap = null;
            }
        } else {
            preSaleQuoteDataMap = null;
        }

        List<String> orderNoList = orderListDTOList.stream().map(OrderListDTO::getOrderNo).toList();
        List<OrderTaskMaxEndTimeVO> orderTaskMaxEndTimeList = diOrderMapper.findOrderTaskMaxEndTime(orderNoList);
        Map<String, OrderTaskMaxEndTimeVO> orderTaskMaxEndTimeMap = orderTaskMaxEndTimeList.stream().collect(Collectors.toMap(OrderTaskMaxEndTimeVO::getOrderNo, Function.identity()));
        for (OrderListDTO orderListDTO : orderListDTOList) {
            orderListDTO.setOwnerName(userNameMap.get(orderListDTO.getOwner()));
            //设置部门名称
            if (StringUtils.isNotBlank(orderListDTO.getDeptName())) {
                R<SysDept> deptResult = remoteUserService.getInfoByDeptId(Long.valueOf(orderListDTO.getDeptName()));
                if (null != deptResult.getData()) orderListDTO.setDeptName(deptResult.getData().getDeptName());
            }
            if (CollectionUtil.isNotEmpty(preSaleQuoteDataMap) && preSaleQuoteDataMap.containsKey(orderListDTO.getPreSaleQuoteNo())) {
                OrderListDataResponse preSaleQuoteData = preSaleQuoteDataMap.get(orderListDTO.getPreSaleQuoteNo());
                setIndustryCategories(preSaleQuoteData, orderListDTO);
                setTechSupportOwner(preSaleQuoteData, orderListDTO);
            }
            if (CollectionUtil.isNotEmpty(orderTaskMaxEndTimeMap) && orderTaskMaxEndTimeMap.containsKey(orderListDTO.getOrderNo())) {
                setOrderTaskMaxEndTime(orderTaskMaxEndTimeMap.get(orderListDTO.getOrderNo()), orderListDTO);
            }
        }
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(orderListDTOList);
        rspData.setMsg("查询成功");
        return rspData;
    }

    private void setIndustryCategories(OrderListDataResponse preSaleQuoteData, OrderListDTO orderListDTO) {
        if (StringUtils.isBlank(preSaleQuoteData.getIndustryCategoriesList())) {
            return;
        }
        orderListDTO.setIndustryCategoriesList(preSaleQuoteData.getIndustryCategoriesList());
        List<String> industryCategoriesList = Arrays.stream(preSaleQuoteData.getIndustryCategoriesList().split(",")).map(String::trim).toList();
        if (CollectionUtil.isEmpty(industryCategoriesList)) {
            return;
        }
        List<SysCategory> categoryList = sysCategoryService.list(new LambdaQueryWrapper<SysCategory>().in(SysCategory::getCode, industryCategoriesList));
        if (CollectionUtil.isEmpty(categoryList)) {
            return;
        }
        orderListDTO.setIndustryCategoriesNameList(categoryList.stream().map(SysCategory::getName).collect(Collectors.joining(",")));
    }

    private void setTechSupportOwner(OrderListDataResponse preSaleQuoteData, OrderListDTO orderListDTO) {
        if (StringUtils.isBlank(preSaleQuoteData.getTechSupportOwnerCodeList())) {
            return;
        }
        orderListDTO.setTechSupportOwnerCodeList(preSaleQuoteData.getTechSupportOwnerCodeList());
        List<String> techSupportOwnerCodeList = Arrays.stream(preSaleQuoteData.getTechSupportOwnerCodeList().split(",")).map(String::trim).toList();
        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(techSupportOwnerCodeList).build());
        if (!userListResult.isSuccess() || CollectionUtil.isEmpty(userListResult.getData())) {
            return;
        }
        orderListDTO.setTechSupportOwnerNameList(userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(",")));
    }

    private void setOrderTaskMaxEndTime(OrderTaskMaxEndTimeVO orderTaskMaxEndTime, OrderListDTO orderListDTO) {
        if (Objects.isNull(orderTaskMaxEndTime)) {
            return;
        }
        if (null != orderTaskMaxEndTime.getMachineDesignExpectEndTime()) {
            orderListDTO.setMachineDesignExpectEndTime(orderTaskMaxEndTime.getMachineDesignExpectEndTime());
        }
        if (null != orderTaskMaxEndTime.getElectricDesignExpectEndTime()) {
            orderListDTO.setElectricDesignExpectEndTime(orderTaskMaxEndTime.getElectricDesignExpectEndTime());
        }
        if (null != orderTaskMaxEndTime.getMaterielPlanExpectEndTime()) {
            orderListDTO.setMaterielPlanExpectEndTime(orderTaskMaxEndTime.getMaterielPlanExpectEndTime());
        }
        if (null != orderTaskMaxEndTime.getProduceExpectEndTime()) {
            orderListDTO.setProduceExpectEndTime(orderTaskMaxEndTime.getProduceExpectEndTime());
        }
        if (null != orderTaskMaxEndTime.getQualityExpectEndTime()) {
            orderListDTO.setQualityExpectEndTime(orderTaskMaxEndTime.getQualityExpectEndTime());
        }
    }

    @Deprecated
    @Override
    public OrderInfoDTO orderInfo(OrderInfoVo orderInfoVo) {
        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery().eq(StringUtils.isNotBlank(orderInfoVo.getOrderNo()), DiOrder::getOrderNo, orderInfoVo.getOrderNo()).eq(Objects.nonNull(orderInfoVo.getId()), DiOrder::getId, orderInfoVo.getId()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        OrderInfoDTO orderInfoDTO = OrderConvert.INSTANCE.entityToOrderDTO(diOrder);
//        if (StringUtils.isNotBlank(diOrder.getContractId())) {
//            orderInfoDTO.setContractDetail(diContractService.selectDiContractById(null, diOrder.getContractId()));
//        }
//        if (StringUtils.isNotBlank(diOrder.getPreSalePlanId())) {
//            orderInfoDTO.setPreSaleDetail(diPreSaleService.selectDiPreSaleById(null, diOrder.getPreSalePlanId(), 1));
//        }
        return orderInfoDTO;
    }

    /**
     * 新增订单
     * 订单由合同上传完，通过合同审批后自动生成，生成的订单需要关联项目ID
     *
     * @param orderAddDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrder(OrderAddDTO orderAddDTO) {
        DiOrder diOrder = OrderConvert.INSTANCE.addDTOTOEntity(orderAddDTO);
        if ("1".equals(orderAddDTO.getIsAdvanceExecution())) {
            diOrder.setOrderStatus(OrderStatusEnum.INIT.getCode());
        } else {
            diOrder.setOrderStatus(OrderStatusEnum.WAIT_SETTING_PM.getCode());
        }

        diOrder.setOrderNo(sequenceService.getSequenceNo(SequenceEnum.DYD_DD.getCode()));
        //查合同的账期
        DiContract diContract = diContractService.getContractByIdOrNo(null, orderAddDTO.getContractNo());
        if (Objects.isNull(diContract)) {
            throw new ServiceException("合同不存在");
        }
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectById(diContract.getPreSaleQuoteId());
        if (Objects.isNull(preSaleQuote)) {
            throw new ServiceException("销售报价单不存在");
        }
        diOrder.setCustomerName(diContract.getFirstPartyName());
//        diOrder.setContractDeliveryDate(Date.from(diContract.getOrderDeliveryDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
        diOrder.setContractDeliveryCycleWeek(diContract.getContractDeliveryCycleWeek());
        List<DiContractBillPeriod> billPeriodList = diContractBillPeriodService.list(Wrappers.lambdaQuery(DiContractBillPeriod.class).eq(DiContractBillPeriod::getDiContractId, diContract.getId()));

        List<DiOrderBillPeriod> orderBillPeriodList = OrderConvert.INSTANCE.billPeriodListToOrderPeriodList(billPeriodList);
        if (CollectionUtils.isNotEmpty(orderBillPeriodList)) {
            orderBillPeriodList.forEach(b -> {
                b.setId(null);
                b.setOrderNo(diOrder.getOrderNo());
            });
            diOrderBillPeriodService.saveBatch(orderBillPeriodList);
        }
        //判断销售报价单是否为提前执行
        if (1 == preSaleQuote.getIsAdvanceExecution()) {
            //修改商机
            marketingNicheService.updateNicheStatus(orderAddDTO.getNicheNo(), "3");
        }
        //查关联关系
        Map<String, List<DiProjectRelation>> relationMap = projectRelationService.getRelationListByTypes(RelationTypeDTO.builder().projectNo(orderAddDTO.getProjectNo()).relationTypeList(Lists.newArrayList(RelationTypeEnum.BUSINESS.getRelationType(), RelationTypeEnum.CUSTOMER.getRelationType(), RelationTypeEnum.PRE_SALES_SOLUTION.getRelationType())).build()).stream().collect(Collectors.groupingBy(DiProjectRelation::getRelationType));
        relationMap.getOrDefault(RelationTypeEnum.PRE_SALES_SOLUTION.getRelationType(), Lists.newArrayList()).stream().findFirst().ifPresent(diProjectRelation -> {
            diOrder.setPreSalePlanId(diProjectRelation.getRelationNo());
        });
        Optional.ofNullable(marketingNicheService.selectDiMarketingNicheByNo(orderAddDTO.getNicheNo())).ifPresent(diMarketingNiche -> {
                   /* diOrder.setContactPerson(diMarketingNiche.getContactsName());
                    diOrder.setContactPhone(diMarketingNiche.getContactsPhone());*/
            diOrder.setDeptId(diMarketingNiche.getNicheOwnerDept());
            diOrder.setOwner(diMarketingNiche.getNicheOwner());
            //发送钉钉消息
            String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_ORDER_FORM.getMessage(), diOrder.getOrderNo(), "系统");
            marketingNicheService.updateNicheStatusSendDingTalk(diMarketingNiche, content);
        });
//        relationMap.getOrDefault(RelationTypeEnum.BUSINESS.getRelationType(), Lists.newArrayList()).stream().findFirst().flatMap(diProjectRelation -> Optional.ofNullable(marketingNicheService.selectDiMarketingNicheByNo(diProjectRelation.getRelationNo()))).ifPresent(diMarketingNiche -> {
//                   /* diOrder.setContactPerson(diMarketingNiche.getContactsName());
//                    diOrder.setContactPhone(diMarketingNiche.getContactsPhone());*/
//            diOrder.setDeptId(diMarketingNiche.getNicheOwnerDept());
//            diOrder.setOwner(diMarketingNiche.getNicheOwner());
//            //发送钉钉消息
//            String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_ORDER_FORM.getMessage(), diOrder.getOrderNo(), "系统");
//            marketingNicheService.updateNicheStatusSendDingTalk(diMarketingNiche, content);
//        });
        relationMap.getOrDefault(RelationTypeEnum.CUSTOMER.getRelationType(), Lists.newArrayList()).stream().findFirst().flatMap(diProjectRelation -> Optional.ofNullable(marketingCustomerService.selectDiMarketingCustomerById(diProjectRelation.getRelationNo()))).ifPresent(diMarketingCustomer -> {
            diOrder.setIndustry(diMarketingCustomer.getIndustryClassification());
        });
//        diOrder.setOrderStatus(OrderStatusEnum.INIT.getCode());
        try {
            R<String> docNoR = remoteExchangeService.selectSmSoOne(null, orderAddDTO.getContractNo());
            if (docNoR.isSuccess()) {
                diOrder.setU9OrderNo(docNoR.getData());
            }
        } catch (Exception e) {
            log.info("u9订单号获取失败{}", e.toString());
        }
        diOrderMapper.insert(diOrder);
        if (StringUtils.isNotBlank(orderAddDTO.getProjectNo())) {
            rocketMQTemplate.syncSend(DestinationConstants.RELATION_TOPIC_TAG, MessageBuilder.withPayload(JSONUtil.toJsonStr(ProcessRelationMessageDTO.builder().projectNo(orderAddDTO.getProjectNo()).relationNo(diOrder.getOrderNo()).relationType(RelationTypeEnum.ORDER.getRelationType()).build())).build());
        }
        //发送消息
        AfterCommitExecutor.submit(() -> {
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setBusinessKey(orderAddDTO.getNicheNo());
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE);
            agencyTaskInfoDto.setType("2");
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        }, e -> log.error("发送消息失败", e));
//        AfterCommitExecutor.submit(() -> {
//            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
//            agencyTaskInfoDto.setBusinessKey(orderAddDTO.getNicheNo());
//            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
//            agencyTaskInfoDto.setType("2");
//            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
//            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
//        }, e -> log.error("发送消息失败", e));
        //生成售中方案
        //拿售前方案id
        AfterCommitExecutor.submit(() -> {
            relationMap.getOrDefault(RelationTypeEnum.PRE_SALES_SOLUTION.getRelationType(), Lists.newArrayList()).stream().findFirst()
                    .flatMap(diProjectRelation -> Optional.ofNullable(diPreSaleService.queryDiPreSaleByCode(diProjectRelation.getRelationNo())))
                    .ifPresent(diPreSale -> {
                        RdNeedAddRequest needAddRequest = new RdNeedAddRequest();
                        needAddRequest.setPreSaleId(diPreSale.getId().intValue());
                        needAddRequest.setProjectCode(orderAddDTO.getProjectNo());
                        needAddRequest.setOrderCode(diOrder.getOrderNo());
                        diRdNeedService.insertDiRdNeed(needAddRequest);
                    });
        }, e -> log.error("生成售中方案失败", e));

        return diOrder.getId();
    }


    @Override
    public TableDataInfo getOrderListByProjectNoList(OrderListByProjectNoVo projectNoVo) {
        Map<String, List<DiProjectRelation>> relationMap = projectRelationService.getRelationListByTypes(RelationTypeDTO.builder().projectNoList(projectNoVo.getProjectNoList()).relationTypeList(Lists.newArrayList(RelationTypeEnum.ORDER.getRelationType())).build()).stream().collect(Collectors.groupingBy(DiProjectRelation::getRelationType));

        List<String> orderNoList = relationMap.getOrDefault(RelationTypeEnum.ORDER.getRelationType(), Lists.newArrayList()).stream().map(DiProjectRelation::getRelationNo).collect(Collectors.toList());

        OrderListVo orderListVo = new OrderListVo();
        orderListVo.setOrderIdList(orderNoList);
        return orderList(orderListVo);
    }

    @Override
    public OrderDetailDTO orderDetail(OrderDetailVO orderDetailVO) {
        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.lambdaQuery(DiOrder.class).eq(StringUtils.isNotBlank(orderDetailVO.getOrderNo()), DiOrder::getOrderNo, orderDetailVO.getOrderNo()).eq(Objects.nonNull(orderDetailVO.getId()), DiOrder::getId, orderDetailVO.getId()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        //TODO 这里暂时去掉权限控制，所有人能看，未来在订单里面修改负责人时需要同步推送消息，更改project_relation_user表,现在临时先放开
        //commonService.checkDetailPurview(diOrder.getCreateBy(), diOrder.getOrderNo(), diOrder.getProjectNo());
        OrderDetailDTO orderDetailDTO = OrderConvert.INSTANCE.orderEntityToDetailDTO(diOrder);
        orderDetailDTO.setIsOrderAdvanceExecution(diOrder.getIsAdvanceExecution());
        List<String> jobNumberList = Lists.newArrayList(diOrder.getEnforceUserId(),
                diOrder.getProjectManagerUserId(),
                diOrder.getOwner(),
                diOrder.getElectricDesignUserId(),
                diOrder.getMachineDesignUserId(),
                diOrder.getPurchaseUserId(),
                diOrder.getSystemsEngineer()
        ).stream().filter(StringUtils::isNotBlank).toList();
        if (CollectionUtils.isNotEmpty(jobNumberList)) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(jobNumberList).build());
            if (userListResult.isSuccess()) {
                Map<String, String> userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                orderDetailDTO.setOwnerName(userNameMap.get(diOrder.getOwner()));
                orderDetailDTO.setEnforceUserIdName(userNameMap.get(diOrder.getEnforceUserId()));
                orderDetailDTO.setProjectManagerUserIdName(userNameMap.get(diOrder.getProjectManagerUserId()));
                orderDetailDTO.setElectricDesignUserIdName(userNameMap.get(diOrder.getElectricDesignUserId()));
                orderDetailDTO.setMachineDesignUserIdName(userNameMap.get(diOrder.getMachineDesignUserId()));
//                orderDetailDTO.setProduceUserIdName(userNameMap.get(diOrder.getProduceUserId()));
                orderDetailDTO.setPurchaseUserIdName(userNameMap.get(diOrder.getPurchaseUserId()));
//                orderDetailDTO.setProduceQualityUserName(userNameMap.get(diOrder.getProduceQualityUserId()));
                orderDetailDTO.setSystemsEngineerName(userNameMap.get(diOrder.getSystemsEngineer()));
            }
        }
        if (StringUtils.isNotBlank(diOrder.getProduceUserId())) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Arrays.stream(diOrder.getProduceUserId().split(",")).toList()).build());
            if (userListResult.isSuccess() && CollectionUtil.isNotEmpty(userListResult.getData())) {
                orderDetailDTO.setProduceUserIdName(userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(",")));
            }
        }
        if (StringUtils.isNotBlank(diOrder.getProduceQualityUserId())) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Arrays.stream(diOrder.getProduceQualityUserId().split(",")).toList()).build());
            if (userListResult.isSuccess() && CollectionUtil.isNotEmpty(userListResult.getData())) {
                orderDetailDTO.setProduceQualityUserName(userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(",")));
            }
        }
        Optional.ofNullable(marketingNicheService.selectDiMarketingNicheByNo(diOrder.getNicheNo())).ifPresent(niche -> {
            orderDetailDTO.setDeptId(niche.getNicheOwnerDept());
            orderDetailDTO.setOwner(niche.getNicheOwner());
            orderDetailDTO.setImportance(niche.getImportance());
            orderDetailDTO.setDetermineCause(niche.getDetermineCause());
            List<String> owners = Arrays.asList(niche.getNicheOwner());
            //获取共享销售中文名
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(owners).build());
            if (userListResult.isSuccess() && userListResult.getData().size() > 0) {
                orderDetailDTO.setOwnerName(userListResult.getData().get(0).getName());
            }
            R<SysDept> deptResult = remoteUserService.getInfoByDeptId(Long.valueOf(orderDetailDTO.getDeptId()));
            if (null != deptResult.getData()) {
                orderDetailDTO.setDeptName(deptResult.getData().getDeptName());
            }
        });
        //联系人
        DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
        diMarketingContacts.setBusinessId(diOrder.getNicheNo());
        orderDetailDTO.setContactsList(diMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts));
        //产品方案
        PreSaleQuoteProductCostInfoVO productCostInfoVO = preSaleQuoteService.getQuoteProductCostInfoByNo(diOrder.getPreSaleQuoteNo(), NOT_NEED_FEE);
        List<OrderProductPlanDetailDTO> orderProductDetailList = OrderConvert.INSTANCE.productInfoListToPlanDetailList(productCostInfoVO.getDetailedList());
        for (OrderProductPlanDetailDTO orderProductPlanDetailDTO : orderProductDetailList) {
            if (1 == orderProductPlanDetailDTO.getPreSaleType()) {
                orderProductPlanDetailDTO.setExpecteDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, diOrder.getCreateTime()));
            }
        }
        List<Long> preSaleIdList = orderProductDetailList.stream().map(OrderProductPlanDetailDTO::getPreSaleId).toList();
        if (CollectionUtils.isEmpty(preSaleIdList)) {
            orderDetailDTO.setOrderProductPlanDetailDTOList(orderProductDetailList);
            return orderDetailDTO;
        }


        Map<String, DiPreSale> diPreSaleMap = diPreSaleService.queryDiPreSaleByIds(preSaleIdList).stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));

        List<DiOrderInstall> orderInstallList = diOrderInstallService.list(Wrappers.lambdaQuery(DiOrderInstall.class).in(DiOrderInstall::getPreSaleId, preSaleIdList).eq(DiOrderInstall::getDelFlag, 0));
        List<DiOrderElectricDesign> orderElectricDesignList = diOrderElectricDesignService.list(Wrappers.lambdaQuery(DiOrderElectricDesign.class).in(DiOrderElectricDesign::getPreSaleId, preSaleIdList).eq(DiOrderElectricDesign::getDelFlag, 0));


        List<DiOrderMachineDesign> orderMachineDesignList = diOrderMachineDesignService.list(Wrappers.lambdaQuery(DiOrderMachineDesign.class).in(DiOrderMachineDesign::getPreSaleId, preSaleIdList).eq(DiOrderMachineDesign::getDelFlag, 0));

        List<DiOrderProduce> diOrderProduceList = diOrderProduceService.list(Wrappers.lambdaQuery(DiOrderProduce.class).in(DiOrderProduce::getPreSaleId, preSaleIdList).eq(DiOrderProduce::getDelFlag, 0));

        List<DiOrderMaterielPlan> diOrderMaterielPlanList = diOrderMaterielPlanService.list(Wrappers.lambdaQuery(DiOrderMaterielPlan.class).in(DiOrderMaterielPlan::getPreSaleId, preSaleIdList).eq(DiOrderMaterielPlan::getDelFlag, 0));

        List<DiOrderQuality> diOrderQualityList = diOrderQualityService.list(Wrappers.lambdaQuery(DiOrderQuality.class).in(DiOrderQuality::getPreSaleId, preSaleIdList).eq(DiOrderQuality::getDelFlag, 0));


        Map<String, String> userNameMap = Maps.newHashMap();
        List<String> enforceUserIdList = orderInstallList.stream().map(DiOrderInstall::getEnforceUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        enforceUserIdList.addAll(orderElectricDesignList.stream().map(DiOrderElectricDesign::getElectricDesignUserId).filter(StringUtils::isNotBlank).toList());
        enforceUserIdList.addAll(orderMachineDesignList.stream().map(DiOrderMachineDesign::getMachineDesignUserId).filter(StringUtils::isNotBlank).toList());
        diOrderProduceList.stream().map(DiOrderProduce::getProduceUserId).filter(StringUtils::isNotBlank).toList().forEach(produceUserId ->
                enforceUserIdList.addAll(List.of(produceUserId.split(","))));
        diOrderQualityList.stream().map(DiOrderQuality::getQualityUserId).filter(StringUtils::isNotBlank).toList().forEach(qualityUserId ->
                enforceUserIdList.addAll(List.of(qualityUserId.split(","))));
        enforceUserIdList.addAll(diOrderMaterielPlanList.stream().map(DiOrderMaterielPlan::getPurchaseUserId).filter(StringUtils::isNotBlank).toList());
        if (CollectionUtils.isNotEmpty(enforceUserIdList)) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(enforceUserIdList).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            }
        }
        //继续封装
        Map<String, List<DiOrderInstall>> orderInstallMap = orderInstallList.stream().collect(Collectors.groupingBy(DiOrderInstall::getPreSaleCode));
        Map<String, List<DiOrderElectricDesign>> orderElectricDesignMap = orderElectricDesignList.stream().collect(Collectors.groupingBy(DiOrderElectricDesign::getPreSaleCode));
        Map<String, List<DiOrderMachineDesign>> orderMachineDesignMap = orderMachineDesignList.stream().collect(Collectors.groupingBy(DiOrderMachineDesign::getPreSaleCode));
        Map<String, List<DiOrderProduce>> orderProduceMap = diOrderProduceList.stream().collect(Collectors.groupingBy(DiOrderProduce::getPreSaleCode));
        Map<String, List<DiOrderMaterielPlan>> orderMaterielPlanMap = diOrderMaterielPlanList.stream().collect(Collectors.groupingBy(DiOrderMaterielPlan::getPreSaleCode));
        Map<String, List<DiOrderQuality>> orderQualityMap = diOrderQualityList.stream().collect(Collectors.groupingBy(DiOrderQuality::getPreSaleCode));

        boolean orderPreSaleCheckFlag = false;
        for (OrderProductPlanDetailDTO orderProductPlanDetailDTO : orderProductDetailList) {

            DiPreSale diPreSale = diPreSaleMap.get(orderProductPlanDetailDTO.getPreSaleCode());
            if (!orderPreSaleCheckFlag) {
                orderPreSaleCheckFlag = diPreSale.isTechnicalReviewFlag();
//                        diPreSale.getOrderPreSaleStatus() == OrderPreSaleStatusEnum.TWO.getCode();
//                diPreSaleMaterielSelectionService.hasSelection(diPreSale.getId());
            }

            orderProductPlanDetailDTO.setOrderPreSaleStatus(Objects.nonNull(diPreSale.getOrderPreSaleStatus()) ? String.valueOf(diPreSale.getOrderPreSaleStatus()) : "");

            Map<String, String> finalUserNameMap = userNameMap;

            Optional.ofNullable(orderElectricDesignMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderElectricDesigns -> diOrderElectricDesigns.stream().findFirst()).ifPresent(diOrderElectricDesign -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setOrderPlanType("electricDesign");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("电气设计");
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderElectricDesign.getElectricDesignUserId());
                orderPlanDetailInfoDTO.setEnforceUserIdName(finalUserNameMap.get(diOrderElectricDesign.getElectricDesignUserId()));
                if (Objects.nonNull(diOrderElectricDesign.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setPreSaleDay(diOrderElectricDesign.getPreSaleDay());
                    orderPlanDetailInfoDTO.setInstallDay(diOrderElectricDesign.getPreSaleDay().longValue());
                }
                orderPlanDetailInfoDTO.setRealInstallStartTime(diOrderElectricDesign.getDesignStartTime());
                orderPlanDetailInfoDTO.setRealInstallEndTime(diOrderElectricDesign.getDesignEndTime());
                orderPlanDetailInfoDTO.setInstallProcess(diOrderElectricDesign.getDesignProcess());
                if (Objects.nonNull(orderPlanDetailInfoDTO.getInstallDay()) && Objects.nonNull(orderPlanDetailInfoDTO.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setIsWarn(orderPlanDetailInfoDTO.getInstallDay().intValue() > orderPlanDetailInfoDTO.getPreSaleDay() ? 1 : 0);
                }
                orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });

            Optional.ofNullable(orderMachineDesignMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderMachineDesigns -> diOrderMachineDesigns.stream().findFirst()).ifPresent(diOrderMachineDesign -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setOrderPlanType("machineDesign");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("机械设计");
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderMachineDesign.getMachineDesignUserId());
                orderPlanDetailInfoDTO.setEnforceUserIdName(finalUserNameMap.get(diOrderMachineDesign.getMachineDesignUserId()));
                if (Objects.nonNull(diOrderMachineDesign.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setPreSaleDay(diOrderMachineDesign.getPreSaleDay());
                    orderPlanDetailInfoDTO.setInstallDay(diOrderMachineDesign.getPreSaleDay().longValue());
                }
                orderPlanDetailInfoDTO.setRealInstallStartTime(diOrderMachineDesign.getDesignStartTime());
                orderPlanDetailInfoDTO.setRealInstallEndTime(diOrderMachineDesign.getDesignEndTime());
                orderPlanDetailInfoDTO.setInstallProcess(diOrderMachineDesign.getDesignProcess());
                if (Objects.nonNull(orderPlanDetailInfoDTO.getInstallDay()) && Objects.nonNull(orderPlanDetailInfoDTO.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setIsWarn(orderPlanDetailInfoDTO.getInstallDay().intValue() > orderPlanDetailInfoDTO.getPreSaleDay() ? 1 : 0);
                }
                orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });


            Optional.ofNullable(orderProduceMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderProduces -> diOrderProduces.stream().findFirst()).ifPresent(diOrderProduce -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setOrderPlanType("produce");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("生产任务");
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderProduce.getProduceUserId());
                orderPlanDetailInfoDTO.setEnforceUserIdName(getNameByJob(finalUserNameMap, List.of(diOrderProduce.getProduceUserId().split(","))));
                if (Objects.nonNull(diOrderProduce.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setPreSaleDay(diOrderProduce.getPreSaleDay());
                    orderPlanDetailInfoDTO.setInstallDay(diOrderProduce.getPreSaleDay().longValue());
                }
                orderPlanDetailInfoDTO.setRealInstallStartTime(diOrderProduce.getRealProduceStartTime());
                orderPlanDetailInfoDTO.setRealInstallEndTime(diOrderProduce.getRealProduceEndTime());
                orderPlanDetailInfoDTO.setInstallProcess(diOrderProduce.getDesignProcess());
                if (Objects.nonNull(orderPlanDetailInfoDTO.getInstallDay()) && Objects.nonNull(orderPlanDetailInfoDTO.getPreSaleDay())) {
                    orderPlanDetailInfoDTO.setIsWarn(orderPlanDetailInfoDTO.getInstallDay().intValue() > orderPlanDetailInfoDTO.getPreSaleDay() ? 1 : 0);
                }
                orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });

            Optional.ofNullable(orderQualityMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderQualities -> diOrderQualities.stream().findFirst()).ifPresent(diOrderQuality -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setOrderPlanType("quality");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("生产质检");
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderQuality.getQualityUserId());
                orderPlanDetailInfoDTO.setEnforceUserIdName(getNameByJob(finalUserNameMap, List.of(diOrderQuality.getQualityUserId().split(","))));
                orderPlanDetailInfoDTO.setRealInstallStartTime(diOrderQuality.getRealCheckStartTime());
                orderPlanDetailInfoDTO.setRealInstallEndTime(diOrderQuality.getRealCheckEndTime());
                orderPlanDetailInfoDTO.setInstallProcess(diOrderQuality.getQualityProcess());
                orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });


            Optional.ofNullable(orderMaterielPlanMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderMaterielPlans -> diOrderMaterielPlans.stream().findFirst()).ifPresent(diOrderMaterielPlan -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setOrderPlanType("materiel");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("采购供货");
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderMaterielPlan.getPurchaseUserId());
                orderPlanDetailInfoDTO.setEnforceUserIdName(finalUserNameMap.get(diOrderMaterielPlan.getPurchaseUserId()));
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderMaterielPlan.getLatestCompleteTime());
                orderPlanDetailInfoDTO.setInstallProcess(Objects.nonNull(diOrderMaterielPlan.getScheduleStatus()) ?
                        (diOrderMaterielPlan.getScheduleStatus() == 2 ? new BigDecimal(100) : new BigDecimal(0)) : new BigDecimal(0));
                diOrderProduceBomService.list(Wrappers.lambdaQuery(DiOrderProduceBom.class).eq(DiOrderProduceBom::getPreSaleId, diOrderMaterielPlan.getPreSaleId()).eq(DiOrderProduceBom::getDelFlag, 0)).stream().map(DiOrderProduceBom::getLatestCompleteTime).max(Comparator.naturalOrder()).ifPresent(orderPlanDetailInfoDTO::setRealInstallEndTime);
//                orderPlanDetailInfoDTO.setInstallProcess(BigDecimal.ZERO);
                orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });

            Optional.ofNullable(orderInstallMap.get(orderProductPlanDetailDTO.getPreSaleCode())).flatMap(diOrderInstalls -> diOrderInstalls.stream().findFirst()).ifPresent(diOrderInstall -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = OrderConvert.INSTANCE.installEntityToPlanDTO(diOrderInstall);
                if (Objects.nonNull(orderPlanDetailInfoDTO)) {
                    orderPlanDetailInfoDTO.setEnforceUserIdName(finalUserNameMap.get(orderPlanDetailInfoDTO.getEnforceUserId()));
                    if (Objects.nonNull(orderPlanDetailInfoDTO.getInstallDay()) && Objects.nonNull(orderPlanDetailInfoDTO.getPreSaleDay())) {
                        orderPlanDetailInfoDTO.setMistakeDay(orderPlanDetailInfoDTO.getInstallDay().intValue() - orderPlanDetailInfoDTO.getPreSaleDay());
                        orderPlanDetailInfoDTO.setIsWarn(orderPlanDetailInfoDTO.getInstallDay().intValue() > orderPlanDetailInfoDTO.getPreSaleDay() ? 1 : 0);
                    }
                    orderProductPlanDetailDTO.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
                }
            });

        }

        orderDetailDTO.setOrderPreSaleCheckFlag(orderPreSaleCheckFlag);
        orderDetailDTO.setOrderProductPlanDetailDTOList(orderProductDetailList);

        //获取变更记录
        if (orderDetailDTO.getIsChangeDeliveryDate().equals(1)) {
            List<DiOrderDeliveryChange> changeList = diOrderDeliveryChangeService.list(new LambdaQueryWrapper<DiOrderDeliveryChange>()
                    .eq(DiOrderDeliveryChange::getOrderId, diOrder.getId())
                    .eq(DiOrderDeliveryChange::getIsDeleted, 0));
            if (CollectionUtil.isNotEmpty(changeList)) {
                if (changeList.size() == 1) {
                    orderDetailDTO.setDeliveryChangeId(changeList.get(0).getId());
                    orderDetailDTO.setChangeDeliveryDate(changeList.get(0).getChangeDeliveryDate());
                } else {
                    log.error("DiOrderServiceImpl---orderDetail()---变更记录不正确，订单ID：{}", diOrder.getId());
                }
            } else {
                log.error("DiOrderServiceImpl---orderDetail()---订单下无变更记录，订单ID：{}", diOrder.getId());
            }
        }
        DiContract contract = diContractService.getContractByIdOrNo(null, diOrder.getContractNo());

        orderDetailDTO.setIsContractAdvanceExecution(contract.getIsAdvanceExecution());
        // 设置了项目经理 开始可以排期
        // 排期退出条件，已排期，且 订单可以流转
        if (Objects.equals(OrderStageEnum.ONE.getCode(), orderDetailDTO.getOrderStage())
                && StringUtils.isNotBlank(diOrder.getProjectManagerUserId())
            //&& (diOrderBillPeriodService.hasPayment(diOrder.getOrderNo()) || "2".equals(orderDetailDTO.getIsOrderAdvanceExecution()))
        ) {
            orderDetailDTO.setShowScheduleBtn(true);
            //退出
            if (this.canRun(diOrder)) {
                orderDetailDTO.setShowScheduleBtn(false);
            }
        }
        //是否显示交期变更
        // 1.已排期 且 订单可以流转 且 订单不是终态
        if (OrderScheduleStatusEnum.COMPLETED.getCode().equals(diOrder.getScheduleStatus())
                && this.canRun(diOrder)
                && !(OrderStageEnum.EIGHT.getCode().equals(orderDetailDTO.getOrderStage()) || OrderStageEnum.GIVE_UP.getCode().equals(orderDetailDTO.getOrderStage()))
        ) {
            orderDetailDTO.setShowScheduleChangeBtn(true);
        }
        return orderDetailDTO;
    }


    private String getNameByJob(Map<String, String> userNameMap, List<String> userJobList) {
        if (CollectionUtil.isEmpty(userNameMap)) {
            return String.join(",", userJobList);
        }
        StringBuilder userName = new StringBuilder();
        for (int i = 0; i < userJobList.size(); i++) {
            if (i == userJobList.size() - 1) {
                userName.append(userNameMap.containsKey(userJobList.get(i)) ? userNameMap.get(userJobList.get(i)) : userJobList.get(i));
            } else {
                userName.append(userNameMap.containsKey(userJobList.get(i)) ? userNameMap.get(userJobList.get(i)) : userJobList.get(i)).append("，");
            }
        }
        return userName.toString();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long orderUpdateVO(OrderUpdateVO orderUpdateVO) {

        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.lambdaQuery(DiOrder.class).eq(StringUtils.isNotBlank(orderUpdateVO.getOrderNo()), DiOrder::getOrderNo, orderUpdateVO.getOrderNo()).eq(Objects.nonNull(orderUpdateVO.getId()), DiOrder::getId, orderUpdateVO.getId()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        //  1. 机械的，根据之前给的分配规则
        //  2. 电气的，全部分配给张启遇
        //  3. 采购的，根据之前给的分配规则
        //  4. 生产的，全部分配给 罗祖进
        //  5. 质量的，全部分配给 张华
        if (StringUtils.isNotBlank(orderUpdateVO.getMachineDesignUserId())) {
            orderUpdateVO.setMachineDesignUserId(getSystemEngineer(diOrder));
        }
        if (StringUtils.isNotBlank(orderUpdateVO.getElectricDesignUserId())) {
            orderUpdateVO.setElectricDesignUserId(defaultElectricDesignUserId);
        }
        if (StringUtils.isNotBlank(orderUpdateVO.getPurchaseUserId())) {
            orderUpdateVO.setPurchaseUserId(defaultPurchaseUserId);
        }
        if (StringUtils.isNotBlank(orderUpdateVO.getProduceUserList())) {
            orderUpdateVO.setProduceUserList(defaultProduceUserId);
        }
        if (StringUtils.isNotBlank(orderUpdateVO.getProduceQualityUserList())) {
            orderUpdateVO.setProduceQualityUserList(defaultProduceQualityUserId);
        }

        //获取合同信息
        List<DiContract> contractList = diContractService.list(new LambdaQueryWrapper<DiContract>().eq(DiContract::getContractNo, diOrder.getContractNo()));
        if (CollectionUtil.isEmpty(contractList)) {
            throw new ServiceException("合同不存在");
        }
        if (contractList.size() > 1) {
            throw new ServiceException("合同不正确");
        }

        OrderManagerUpdateEvent orderManagerUpdateEvent = null;
        // if (StringUtils.isBlank(diOrder.getProjectManagerUserId()) && StringUtils.isNotBlank(orderUpdateVO.getProjectManagerUserId())) {
        orderManagerUpdateEvent = new OrderManagerUpdateEvent();
        orderManagerUpdateEvent.setOrderId(diOrder.getId());
        orderManagerUpdateEvent.setNewManager(orderUpdateVO.getProjectManagerUserId());
        orderManagerUpdateEvent.setOldManager(diOrder.getProjectManagerUserId());
        // }

        //更新
        DiOrder updateOrder = OrderConvert.INSTANCE.updateVOToOrderEntity(orderUpdateVO);
//        if(StringUtils.isNotEmpty(orderUpdateVO.getProjectManagerUserId())){
//            updateOrder.setOrderStatus(OrderStatusEnum.WAIT_PREPAYMENT.getCode());
//            //实际支付金额不为空数量
//            Long count = diOrderBillPeriodMapper.selectCount(Wrappers.<DiOrderBillPeriod>lambdaQuery().eq(DiOrderBillPeriod::getOrderNo, orderUpdateVO.getOrderNo()).isNotNull(DiOrderBillPeriod::getRealPayDate));
//            if( count > 0){
//                updateOrder.setOrderStatus(OrderStatusEnum.UNDER_IMPLEMENTATION.getCode());
//            }
//
//        }
//        updateOrder.setId(diOrder.getId());
        if (StringUtils.isNotBlank(orderUpdateVO.getProduceUserList())) {
            updateOrder.setProduceUserId(orderUpdateVO.getProduceUserList());
        }
        if (StringUtils.isNotBlank(orderUpdateVO.getProduceQualityUserList())) {
            updateOrder.setProduceQualityUserId(orderUpdateVO.getProduceQualityUserList());
        }

        if (null == diOrder.getProjectStartDate() || diOrder.getProjectStartDate().compareTo(orderUpdateVO.getProjectStartDate()) != 0) {
            if (null != diOrder.getContractDeliveryCycleWeek()) {
                Instant instant = orderUpdateVO.getProjectStartDate().toInstant();
                LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                if (diOrder.getContractDeliveryCycleWeek().compareTo(new BigDecimal("1")) <= 0) {
                    LocalDateTime date = localDateTime.plusDays(6);
                    updateOrder.setOrderDeliveryDate(Date.from(date.toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                } else {
                    //项目开始日期不相同，重新计算项目交付日期
                    int day = diOrder.getContractDeliveryCycleWeek().multiply(BigDecimal.valueOf(7)).intValue() - 1;
                    LocalDateTime date = localDateTime.plusDays(day);
                    updateOrder.setOrderDeliveryDate(Date.from(date.toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                }
            } else {
                updateOrder.setOrderDeliveryDate(diOrder.getProjectStartDate());
            }
        }
        updateOrder.setId(diOrder.getId());
        diOrderMapper.updateById(updateOrder);


        //更新项目
        DiProcessProject diProcessProject = new DiProcessProject();
        diProcessProject.setProjectNo(diOrder.getProjectNo());
//        if (StringUtils.isNotBlank(orderUpdateVO.getEnforceUserId())) {
//            diProcessProject.setEnforceUserId(orderUpdateVO.getEnforceUserId());
//        }
        if (StringUtils.isNotBlank(orderUpdateVO.getProjectManagerUserId())) {
            diProcessProject.setProjectManagerUserId(orderUpdateVO.getProjectManagerUserId());
        }
        diProcessProjectService.updateProjectByProjectNo(diProcessProject);

        //产品方案
//        PreSaleQuoteProductCostInfoVO productCostInfoVO = preSaleQuoteService.getQuoteProductCostInfoByNo(diOrder.getPreSaleQuoteNo(), NOT_NEED_FEE);
        //查询订单可见范围的人
        List<String> visibleUserList = orderCommonService.getLiabilityUserList(diOrder.getNicheNo());
        //项目经理处理
        operateProjectManagerUser(diOrder, orderUpdateVO, visibleUserList);
        //机械设计负责人处理
//        operateMachineDesignUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);
        //电气设计负责人处理
//        operateElectricDesignUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);
        //采购负责人处理
//        operatePurchaseUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);
        //生产计划负责人处理
//        operateProduceUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);
        //安装调试负责人处理
//        operateEnforceUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);
        //质量排期负责人处理
//        operateProduceQualityUser(diOrder, orderUpdateVO, productCostInfoVO, visibleUserList);

        //2024-12-11版本上线,根据产品要求，判断合同是否提前执行，如果是提前执行，没有待付款状态
        if (!YES.textEquals(contractList.get(0).getIsAdvanceExecution()) && !YES.textEquals(diOrder.getIsAdvanceExecution())) {
            // 修改订单时，判断订单的项目经理以及财务信息里的实际支付日期是否全部填完，如果是则更新订单状态为履约中
            if (StringUtils.isNotEmpty(updateOrder.getProjectManagerUserId()) && diOrderBillPeriodService.count(Wrappers.lambdaQuery(DiOrderBillPeriod.class).eq(DiOrderBillPeriod::getOrderNo, updateOrder.getOrderNo()).isNotNull(DiOrderBillPeriod::getRealPayDate)) > 0) {
                //更新订单状态 已完成
                LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
                updateWrapper.eq(DiOrder::getOrderNo, updateOrder.getOrderNo());
                updateWrapper.set(DiOrder::getOrderStatus, OrderStatusEnum.UNDER_IMPLEMENTATION.getCode());
                updateWrapper.update();
//                //发送钉钉消息
//                sendDingDingMessage(updateOrder, OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS);
            } else if (StringUtils.isNotEmpty(updateOrder.getProjectManagerUserId())) {
                //更新订单状态 待预付款
                LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
                updateWrapper.eq(DiOrder::getOrderNo, updateOrder.getOrderNo());
                updateWrapper.set(DiOrder::getOrderStatus, OrderStatusEnum.WAIT_PREPAYMENT.getCode());
                updateWrapper.update();
            }
        } else {
            if (StringUtils.isNotEmpty(updateOrder.getProjectManagerUserId())) {
                //更新订单状态 履约中
                LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
                updateWrapper.eq(DiOrder::getOrderNo, updateOrder.getOrderNo());
                updateWrapper.set(DiOrder::getOrderStatus, OrderStatusEnum.UNDER_IMPLEMENTATION.getCode());
                updateWrapper.update();
//                //发送钉钉消息
//                sendDingDingMessage(updateOrder, OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS);
            }
        }
        //履约中
        rocketMQTemplate.syncSend(DestinationConstants.ORDER_TOPIC_TAG, updateOrder);
        this.updateSystemEngineer(diOrder.getId(), this.getSystemEngineer(diOrder), diOrder.getSystemsEngineer());
        //初始化排期数据
        SaveSchedulingInformationRequest req = new SaveSchedulingInformationRequest();
        req.setOrderId(diOrder.getId());
        this.createDesignTask(req);

        //设置项目经理,判断是否需要 发送排期通知
        if (orderManagerUpdateEvent != null) {
            EventBusUtils.publishEvent(orderManagerUpdateEvent);
        }


        if (StringUtils.isNotEmpty(diOrder.getU9OrderNo()) && Objects.nonNull(updateOrder.getOrderDeliveryDate())) {
            //更新u9交期
            try {
                UpdateRequireDateRequest updateRequireDateRequest = new UpdateRequireDateRequest();
                updateRequireDateRequest.setRequireDate(DateUtils.format(updateOrder.getOrderDeliveryDate(), DateUtils.YYYY_MM_DD));
                updateRequireDateRequest.setDocNo(diOrder.getU9OrderNo());
                remoteExchangeService.updateRequireDate(updateRequireDateRequest);
            } catch (Exception e) {
                log.info("更新U9订单交期失败");
            }
        }
        return diOrder.getId();
    }

    private void createDesignTask(SaveSchedulingInformationRequest request) {
        DiOrder order = diOrderMapper.selectById(request.getOrderId());
        List<Long> preSaleIds = preSaleQuoteService.queryPreSaleIdListByQuoteNo(order.getPreSaleQuoteNo());
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleIds);

        Map<String, DiOrderElectricDesign> electricDesignMap = diOrderElectricDesignService.queryByOrderNo(order.getOrderNo());
        Map<String, DiOrderMachineDesign> machineDesignMap = diOrderMachineDesignService.queryByOrderNo(order.getOrderNo());

        for (DiPreSale diPreSale : diPreSales) {
            PreSaleSchedulingDTO req = request.getList().stream().filter(item -> item.getPreSaleId().equals(diPreSale.getId())).findFirst().orElse(null);
            if (req == null) {
                req = new PreSaleSchedulingDTO();
                req.setPreSaleId(diPreSale.getId());
            }
            //DiPreSale diPreSale = diPreSales.stream().filter(item -> item.getId().equals(req.getPreSaleId())).findFirst().get();
            DiPreSaleManifest manifest = diPreSaleService.queryFirstManifest(req.getPreSaleId());
            PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(diPreSale.getPreSaleType());
            //电气设计
            if (StringUtils.isNotEmpty(order.getElectricDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD) {
                DiOrderElectricDesign diOrderElectricDesign = electricDesignMap.get(diPreSale.getPreSaleCode());

                if (diOrderElectricDesign == null) {
                    OrderElectricAddVO add = new OrderElectricAddVO();
                    add.setOrderNo(order.getOrderNo());
                    add.setOrderNo(order.getOrderNo());
                    add.setPreSaleId(diPreSale.getId());
                    add.setPreSaleCode(diPreSale.getPreSaleCode());
                    add.setElectricDesignUserId("dyd479");
                    add.setElectricDesignUserName("张启遇");
                    add.setExpectStartTime(req.getElectricStart());
                    add.setExpectEndTime(req.getElectricEnd());
                    add.setPreSaleDay(manifest.getElectricalDesignPeriod());
                    diOrderElectricDesignService.add(add);
                } else {
                    OrderElectricUpdateVO update = new OrderElectricUpdateVO();
                    update.setId(diOrderElectricDesign.getId());
                    update.setOrderNo(order.getOrderNo());
                    update.setOrderNo(order.getOrderNo());
                    update.setPreSaleId(diPreSale.getId());
                    update.setExpectStartTime(req.getElectricStart());
                    update.setExpectEndTime(req.getElectricEnd());
                    update.setIgnoreDingDing(true);
                    diOrderElectricDesignService.updateElectric(update);
                }
            }
            //机械
            if (StringUtils.isNotEmpty(order.getMachineDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD) {
                DiOrderMachineDesign machineDesign = machineDesignMap.get(diPreSale.getPreSaleCode());
                DiMarketingNiche marketingNiche = marketingNicheService.queryByNicheId(diPreSale.getNicheId());
                if (machineDesign == null) {
                    OrderMachineAddVO add = new OrderMachineAddVO();
                    add.setOrderNo(order.getOrderNo());
                    add.setPreSaleId(diPreSale.getId());
                    add.setPreSaleCode(diPreSale.getPreSaleCode());
                    if (!marketingNiche.getNeedTechSupport()) {
                        add.setMachineDesignUserId("dyd019");
                    } else {
                        add.setMachineDesignUserId(this.getSystemEngineer(order));
                    }
                    add.setExpectStartTime(req.getMachineStart());
                    add.setExpectEndTime(req.getMachineEnd());
                    add.setPreSaleDay(manifest.getMechanicalDesignDuration());
                    if (req.getBomExpectEndTime() == null) {
                        add.setBomExpectEndTime(DateUtils.addDays(order.getCreateTime(), 7));
                    } else {
                        add.setBomExpectEndTime(req.getBomExpectEndTime());
                    }
                    diOrderMachineDesignService.add(add);
                } else {
                    OrderMachineUpdateVO update = new OrderMachineUpdateVO();
                    update.setId(machineDesign.getId());
                    update.setPreSaleId(diPreSale.getId());
                    update.setExpectStartTime(req.getMachineStart());
                    update.setExpectEndTime(req.getMachineEnd());
                    update.setBomExpectEndTime(req.getBomExpectEndTime());
                    update.setIgnoreDingDing(true);
                    diOrderMachineDesignService.updateMachine(update);
                }
            }
        }
    }

    /**
     * 操作项目经理处理
     *
     * @param diOrder         订单信息
     * @param orderUpdateVO   订单修改信息
     * @param visibleUserList 可见订单用户集合
     */
    private void operateProjectManagerUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, List<String> visibleUserList) {
        //判断是否新增了项目经理
        boolean isSaveProjectManagerUser = false;
        //判断是否修改了项目经理
        boolean isUpdateProjectManagerUser = false;
        if (StringUtils.isBlank(diOrder.getProjectManagerUserId())) {
            if (StringUtils.isNotBlank(orderUpdateVO.getProjectManagerUserId())) {
                isSaveProjectManagerUser = true;
            }
        } else {
            if (StringUtils.isNotBlank(orderUpdateVO.getProjectManagerUserId()) && !diOrder.getProjectManagerUserId().equals(orderUpdateVO.getProjectManagerUserId())) {
                isUpdateProjectManagerUser = true;
            }
        }
        if (isSaveProjectManagerUser || isUpdateProjectManagerUser) {
            //给最新的项目经理发送钉钉消息
            visibleUserList.add(orderUpdateVO.getProjectManagerUserId());
            String title = String.format(OrderDingTalkEnum.ORDER_PROJECT_MANAGER_USER_UPDATE.getTitle(), diOrder.getOrderNo());
            String content = String.format(OrderDingTalkEnum.ORDER_PROJECT_MANAGER_USER_UPDATE.getMessage(), orderUpdateVO.getProjectManagerUserIdName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, null);
        } else {
            if (StringUtils.isNotBlank(diOrder.getProjectManagerUserId())) {
                visibleUserList.add(diOrder.getProjectManagerUserId());
            }
        }
    }

//    /**
//     * 操作机械设计负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operateMachineDesignUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        //判断是否新增了机械设计负责人
//        boolean isSaveMachineDesignUser = false;
//        //判断是否修改了机械设计负责人
//        boolean isUpdateMachineDesignUser = false;
//        if (StringUtils.isBlank(diOrder.getMachineDesignUserId())) {
//            if (StringUtils.isNotBlank(orderUpdateVO.getMachineDesignUserId())) {
//                isSaveMachineDesignUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getMachineDesignUserId()), "0");
//            }
//        } else {
//            if (StringUtils.isNotBlank(orderUpdateVO.getMachineDesignUserId()) && !diOrder.getMachineDesignUserId().equals(orderUpdateVO.getMachineDesignUserId())) {
//                isUpdateMachineDesignUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrder.getMachineDesignUserId()), "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getMachineDesignUserId()), "0");
//            }
//        }
//        if (isSaveMachineDesignUser) {
//            List<String> saveUserList = Collections.singletonList(orderUpdateVO.getMachineDesignUserId());
//            productCostInfoVO.getDetailedList().forEach(diOrderMachineDesign -> {
//                if (!PreSaleTypeEnum.nonStandard(diOrderMachineDesign.getPreSaleType())) {
//                    log.info("不是非标/大非标 不发送代办 ：{}", diOrderMachineDesign.getPreSaleCode());
//                    return;
//                }
//                //增加机械设计待办任务
//                saveAgencyTask(diOrderMachineDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.MACHINE_DESIGN,
//                        StringUtils.isNotBlank(diOrderMachineDesign.getPreSaleName()) ? diOrderMachineDesign.getPreSaleName() : diOrderMachineDesign.getPreSaleCode(),
//                        "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//        }
//        if (isUpdateMachineDesignUser) {
//            //修改了机械设计负责人,判断订单机械设计负责人在不在排期中，如果不在排期中，将待办任务删除
//            List<DiOrderMachineDesign> machineDesignList = diOrderMachineDesignService.list(new LambdaQueryWrapper<DiOrderMachineDesign>().eq(DiOrderMachineDesign::getOrderNo, diOrder.getOrderNo()).eq(DiOrderMachineDesign::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(machineDesignList)) {
//                //记录已经生成过待办任务的产品方案-机械设计的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                machineDesignList.forEach(machineDesign -> {
//                    codeList.add(machineDesign.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (machineDesign.getScheduleStatus().equals(2)) {
//                        return;
//                    }
//                    if (!machineDesign.getMachineDesignUserId().equals(orderUpdateVO.getMachineDesignUserId())) {
//                        List<String> saveUserList = Arrays.asList(machineDesign.getMachineDesignUserId(), orderUpdateVO.getMachineDesignUserId());
//                        //增加机械设计待办任务
//                        saveAgencyTask(machineDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.MACHINE_DESIGN,
//                                preSaleMap.containsKey(machineDesign.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(machineDesign.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(machineDesign.getPreSaleCode()).get(0).getPreSaleName() : machineDesign.getPreSaleCode(),
//                                getTaskStateDesc(machineDesign.getScheduleStatus()), saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    }
//                    if (!machineDesign.getMachineDesignUserId().equals(diOrder.getMachineDesignUserId())) {
//                        deleteAgencyTask(Collections.singletonList(machineDesign.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.MACHINE_DESIGN, Lists.newArrayList(diOrder.getMachineDesignUserId()));
//                    }
//                });
//                //过滤出未生成机械设计的产品方案，并生成待办任务
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(product -> !codeList.contains(product.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    List<String> deleteCode = filterList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                    //删除待办
//                    deleteAgencyTask(deleteCode, diOrder.getOrderNo(), AgencyTaskTypeEnum.MACHINE_DESIGN, Lists.newArrayList(diOrder.getMachineDesignUserId()));
//                    List<String> saveUserList = Collections.singletonList(orderUpdateVO.getMachineDesignUserId());
//                    filterList.forEach(diOrderMachineDesign -> {
//                        if (!PreSaleTypeEnum.nonStandard(diOrderMachineDesign.getPreSaleType())) {
//                            log.info("不是非标/大非标 不发送机械代办3 ：{}", diOrderMachineDesign.getPreSaleCode());
//                            return;
//                        }
//                        //增加机械设计待办任务
//                        saveAgencyTask(diOrderMachineDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.MACHINE_DESIGN,
//                                StringUtils.isNotBlank(diOrderMachineDesign.getPreSaleName()) ? diOrderMachineDesign.getPreSaleName() : diOrderMachineDesign.getPreSaleCode(),
//                                "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    });
//                }
//            } else {
//                List<String> codeList = productCostInfoVO.getDetailedList().stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                deleteAgencyTask(codeList, diOrder.getOrderNo(), AgencyTaskTypeEnum.MACHINE_DESIGN, Lists.newArrayList(diOrder.getMachineDesignUserId()));
//                List<String> saveUserList = Collections.singletonList(orderUpdateVO.getMachineDesignUserId());
//                productCostInfoVO.getDetailedList().forEach(diOrderMachineDesign -> {
//                    if (!PreSaleTypeEnum.nonStandard(diOrderMachineDesign.getPreSaleType())) {
//                        log.info("不是非标/大非标 不发送代办1 ：{}", diOrderMachineDesign.getPreSaleCode());
//                        return;
//                    }
//                    //增加机械设计待办任务
//                    saveAgencyTask(diOrderMachineDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.MACHINE_DESIGN,
//                            StringUtils.isNotBlank(diOrderMachineDesign.getPreSaleName()) ? diOrderMachineDesign.getPreSaleName() : diOrderMachineDesign.getPreSaleCode(),
//                            "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                });
//            }
//        }
//        if (isSaveMachineDesignUser || isUpdateMachineDesignUser) {
//            //发送钉钉消息
//            String title = String.format(OrderDingTalkEnum.ORDER_MACHINE_DESIGN_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//            String content = String.format(OrderDingTalkEnum.ORDER_MACHINE_DESIGN_USER_UPDATE.getMessage(), StringUtils.isNotBlank(orderUpdateVO.getMachineDesignUserIdName()) ? orderUpdateVO.getMachineDesignUserIdName() : orderUpdateVO.getMachineDesignUserId(), SecurityUtils.getLoginUser().getSysUser().getNickName());
//            orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, orderUpdateVO.getMachineDesignUserId());
//        }
//    }

//    /**
//     * 操作电气设计负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operateElectricDesignUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        //判断是否新增了电气设计负责人
//        boolean isSaveElectricDesignUser = false;
//        //判断是否修改了电气设计负责人
//        boolean isUpdateElectricDesignUser = false;
//        if (StringUtils.isBlank(diOrder.getElectricDesignUserId())) {
//            if (StringUtils.isNotBlank(orderUpdateVO.getElectricDesignUserId())) {
//                isSaveElectricDesignUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getElectricDesignUserId()), "0");
//            }
//        } else {
//            if (!diOrder.getElectricDesignUserId().equals(orderUpdateVO.getElectricDesignUserId())) {
//                isUpdateElectricDesignUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrder.getElectricDesignUserId()), "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getElectricDesignUserId()), "0");
//            }
//        }
//        if (isSaveElectricDesignUser) {
////            //新增待办任务
//            List<String> saveUserList = Collections.singletonList(orderUpdateVO.getElectricDesignUserId());
//            productCostInfoVO.getDetailedList().forEach(diOrderElectricDesign -> {
//                if (!PreSaleTypeEnum.nonStandard(diOrderElectricDesign.getPreSaleType())) {
//                    log.info("不是非标/大非标 不发送电气代办1 ：{}", diOrderElectricDesign.getPreSaleCode());
//                    return;
//                }
//                //增加机械设计待办任务
//                saveAgencyTask(diOrderElectricDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN,
//                        StringUtils.isNotBlank(diOrderElectricDesign.getPreSaleName()) ? diOrderElectricDesign.getPreSaleName() : diOrderElectricDesign.getPreSaleCode(),
//                        "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//        }
//        if (isUpdateElectricDesignUser) {
//            //修改了电气设计负责人,判断订单电气设计负责人在不在排期中，如果不在排期中，将待办任务删除
//            List<DiOrderElectricDesign> electricDesignList = diOrderElectricDesignService.list(new LambdaQueryWrapper<DiOrderElectricDesign>().eq(DiOrderElectricDesign::getOrderNo, diOrder.getOrderNo()).eq(DiOrderElectricDesign::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(electricDesignList)) {
//                //记录已经生成过待办任务的产品方案-电气设计的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                //改为每个任务都对比一下
//                electricDesignList.forEach(electricDesign -> {
//                    codeList.add(electricDesign.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (electricDesign.getScheduleStatus().equals(2)) {
//                        return;
//                    }
//                    //判断修改后的负责人是否与排期中的负责人一致,不一致发送新增待办
//                    if (!electricDesign.getElectricDesignUserId().equals(orderUpdateVO.getElectricDesignUserId())) {
//                        //发送待办
//                        List<String> saveUserList = Arrays.asList(electricDesign.getElectricDesignUserId(), orderUpdateVO.getElectricDesignUserId());
//                        //增加电气设计待办任务
//                        saveAgencyTask(electricDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN,
//                                preSaleMap.containsKey(electricDesign.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(electricDesign.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(electricDesign.getPreSaleCode()).get(0).getPreSaleName() : electricDesign.getPreSaleCode(),
//                                getTaskStateDesc(electricDesign.getScheduleStatus()), saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    }
//                    //判断修改前的负责人是否与排期中的负责人一致,不一致发送删除待办
//                    if (!electricDesign.getElectricDesignUserId().equals(diOrder.getElectricDesignUserId())) {
//                        //删除待办
//                        deleteAgencyTask(Collections.singletonList(electricDesign.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN, Lists.newArrayList(diOrder.getElectricDesignUserId()));
//                    }
//                });
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(product -> !codeList.contains(product.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    List<String> deleteCode = filterList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                    //删除待办
//                    deleteAgencyTask(deleteCode, diOrder.getOrderNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN, Lists.newArrayList(diOrder.getElectricDesignUserId()));
//                    List<String> saveUserList = Collections.singletonList(orderUpdateVO.getElectricDesignUserId());
//                    filterList.forEach(diOrderElectricDesign -> {
//                        //增加电气设计待办任务
//                        if (!PreSaleTypeEnum.nonStandard(diOrderElectricDesign.getPreSaleType())) {
//                            log.info("不是非标/大非标 不发送电气代办3 ：{}", diOrderElectricDesign.getPreSaleCode());
//                            return;
//                        }
//                        saveAgencyTask(diOrderElectricDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN,
//                                StringUtils.isNotBlank(diOrderElectricDesign.getPreSaleName()) ? diOrderElectricDesign.getPreSaleName() : diOrderElectricDesign.getPreSaleCode(),
//                                "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    });
//                }
//            } else {
//                List<String> codeList = productCostInfoVO.getDetailedList().stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                deleteAgencyTask(codeList, diOrder.getOrderNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN, Lists.newArrayList(diOrder.getElectricDesignUserId()));
////                //根据新的电气设计负责人生成新的待办
////                saveElectricDesignAgencyTask(productCostInfoVO.getDetailedList(), diOrder.getOrderNo(), Collections.singletonList(orderUpdateVO.getElectricDesignUserId()), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                List<String> saveUserList = Collections.singletonList(orderUpdateVO.getElectricDesignUserId());
//                productCostInfoVO.getDetailedList().forEach(diOrderElectricDesign -> {
//                    //增加机械设计待办任务
//                    if (!PreSaleTypeEnum.nonStandard(diOrderElectricDesign.getPreSaleType())) {
//                        log.info("不是非标/大非标 不发送电气代办2 ：{}", diOrderElectricDesign.getPreSaleCode());
//                        return;
//                    }
//                    saveAgencyTask(diOrderElectricDesign.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ELECTRIC_DESIGN,
//                            StringUtils.isNotBlank(diOrderElectricDesign.getPreSaleName()) ? diOrderElectricDesign.getPreSaleName() : diOrderElectricDesign.getPreSaleCode(),
//                            "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                });
//            }
//        }
//        if (isSaveElectricDesignUser || isUpdateElectricDesignUser) {
//            //发送钉钉消息
//            String title = String.format(OrderDingTalkEnum.ORDER_ELECTRIC_DESIGN_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//            String content = String.format(OrderDingTalkEnum.ORDER_ELECTRIC_DESIGN_USER_UPDATE.getMessage(), StringUtils.isNotBlank(orderUpdateVO.getElectricDesignUserIdName()) ? orderUpdateVO.getElectricDesignUserIdName() : orderUpdateVO.getElectricDesignUserId(), SecurityUtils.getLoginUser().getSysUser().getNickName());
//            orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, orderUpdateVO.getElectricDesignUserId());
//        }
//    }

//    /**
//     * 操作采购负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operatePurchaseUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        boolean isSavePurchaseUser = false;
//        boolean isUpdatePurchaseUser = false;
//        if (StringUtils.isBlank(diOrder.getPurchaseUserId())) {
//            if (StringUtils.isNotBlank(orderUpdateVO.getPurchaseUserId())) {
//                isSavePurchaseUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getPurchaseUserId()), "0");
//            }
//        } else {
//            if (!diOrder.getPurchaseUserId().equals(orderUpdateVO.getPurchaseUserId())) {
//                isUpdatePurchaseUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrder.getPurchaseUserId()), "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getPurchaseUserId()), "0");
//            }
//        }
//        if (isSavePurchaseUser) {
//            List<String> saveUserList = Collections.singletonList(orderUpdateVO.getPurchaseUserId());
//            productCostInfoVO.getDetailedList().forEach(diOrderPurchase -> {
//                //增加采购待办任务
//                saveAgencyTask(diOrderPurchase.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ORDER_PURCHASE,
//                        StringUtils.isNotBlank(diOrderPurchase.getPreSaleName()) ? diOrderPurchase.getPreSaleName() : diOrderPurchase.getPreSaleCode(),
//                        "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//        }
//        if (isUpdatePurchaseUser) {
//            List<DiOrderMaterielPlan> materielPlanList = diOrderMaterielPlanService.list(new LambdaQueryWrapper<DiOrderMaterielPlan>().eq(DiOrderMaterielPlan::getOrderNo, diOrder.getOrderNo()).eq(DiOrderMaterielPlan::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(materielPlanList)) {
//                //记录已经生成过待办任务的产品方案-电气设计的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                //改为每个任务都对比一下
//                materielPlanList.forEach(materielPlan -> {
//                    codeList.add(materielPlan.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (materielPlan.getScheduleStatus().equals(2)) {
//                        return;
//                    }
//                    //判断修改后的负责人是否与排期中的负责人一致,不一致发送新增待办
//                    if (!materielPlan.getPurchaseUserId().equals(orderUpdateVO.getPurchaseUserId())) {
//                        //发送待办
//                        List<String> saveUserList = Arrays.asList(materielPlan.getPurchaseUserId(), orderUpdateVO.getPurchaseUserId());
//                        //增加物料计划待办任务
//                        saveAgencyTask(materielPlan.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ORDER_PURCHASE,
//                                preSaleMap.containsKey(materielPlan.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(materielPlan.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(materielPlan.getPreSaleCode()).get(0).getPreSaleName() : materielPlan.getPreSaleCode(),
//                                getTaskStateDesc(materielPlan.getScheduleStatus()), saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    }
//                    //判断修改前的负责人是否与排期中的负责人一致,不一致发送删除待办
//                    if (!materielPlan.getPurchaseUserId().equals(diOrder.getPurchaseUserId())) {
//                        //删除待办
//                        deleteAgencyTask(Collections.singletonList(materielPlan.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.ORDER_PURCHASE, Lists.newArrayList(diOrder.getPurchaseUserId()));
//                    }
//                });
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(product -> !codeList.contains(product.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    List<String> deleteCode = filterList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                    //删除待办
//                    deleteAgencyTask(deleteCode, diOrder.getOrderNo(), AgencyTaskTypeEnum.ORDER_PURCHASE, Lists.newArrayList(diOrder.getPurchaseUserId()));
//                    List<String> saveUserList = Collections.singletonList(orderUpdateVO.getPurchaseUserId());
//                    filterList.forEach(diOrderPurchase -> {
//                        //增加电气设计待办任务
//                        saveAgencyTask(diOrderPurchase.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ORDER_PURCHASE,
//                                StringUtils.isNotBlank(diOrderPurchase.getPreSaleName()) ? diOrderPurchase.getPreSaleName() : diOrderPurchase.getPreSaleCode(),
//                                "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    });
//                }
//            } else {
//                List<String> codeList = productCostInfoVO.getDetailedList().stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                deleteAgencyTask(codeList, diOrder.getOrderNo(), AgencyTaskTypeEnum.ORDER_PURCHASE, Lists.newArrayList(diOrder.getPurchaseUserId()));
//                List<String> saveUserList = Collections.singletonList(orderUpdateVO.getPurchaseUserId());
//                productCostInfoVO.getDetailedList().forEach(diOrderPurchase -> {
//                    //增加采购待办任务
//                    saveAgencyTask(diOrderPurchase.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ORDER_PURCHASE,
//                            StringUtils.isNotBlank(diOrderPurchase.getPreSaleName()) ? diOrderPurchase.getPreSaleName() : diOrderPurchase.getPreSaleCode(),
//                            "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                });
//            }
//        }
//        if (isSavePurchaseUser || isUpdatePurchaseUser) {
//            //发送钉钉消息
//            String title = String.format(OrderDingTalkEnum.ORDER_PURCHASE_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//            String content = String.format(OrderDingTalkEnum.ORDER_PURCHASE_USER_UPDATE.getMessage(), StringUtils.isNotBlank(orderUpdateVO.getPurchaseUserIdName()) ? orderUpdateVO.getPurchaseUserIdName() : orderUpdateVO.getPurchaseUserId(), SecurityUtils.getLoginUser().getSysUser().getNickName());
//            orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, orderUpdateVO.getPurchaseUserId());
//        }
//    }

//    /**
//     * 操作生产负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operateProduceUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        //判断是否新增了生产负责人
//        boolean isSaveProduceUser = false;
//        //判断是否修改了生产负责人
//        boolean isUpdateProduceUser = false;
//        if (StringUtils.isBlank(diOrder.getProduceUserId())) {
//            if (CollectionUtil.isNotEmpty(orderUpdateVO.getProduceUserList())) {
//                isSaveProduceUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), orderUpdateVO.getProduceUserList().stream().distinct().toList(), "0");
//            }
//        } else {
//            List<String> produceUsers = List.of(diOrder.getProduceUserId().split(","));
//            if (!ListUtils.areEqualIgnoringOrder(orderUpdateVO.getProduceUserList().stream().distinct().toList(), produceUsers)) {
//                isUpdateProduceUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), produceUsers, "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), orderUpdateVO.getProduceUserList().stream().distinct().toList(), "0");
//            }
//        }
//        if (isSaveProduceUser) {
//            List<String> produceUserIdList = orderUpdateVO.getProduceUserList().stream().distinct().toList();
//            Map<String, String> nameMap = commonService.getUserNameByJob(produceUserIdList, null);
//            //新增待办任务
//            productCostInfoVO.getDetailedList().forEach(diOrderProduce -> {
//                if (PreSaleTypeEnum.TRADE == PreSaleTypeEnum.of(diOrderProduce.getPreSaleType())) {
//                    log.info("贸易类 没有 生产计划待办任务：{}", diOrderProduce.getPreSaleCode());
//                    return;
//                }
//                //增加生产计划待办任务
//                saveAgencyTask(diOrderProduce.getPreSaleCode(), diOrder.getOrderNo(), diOrder.getProjectNo(), diOrder.getProjectNo(), AgencyTaskTypeEnum.PRODUCE_MANAGE,
//                        StringUtils.isNotBlank(diOrderProduce.getPreSaleName()) ? diOrderProduce.getPreSaleName() : diOrderProduce.getPreSaleCode(),
//                        "未开始", produceUserIdList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//            orderUpdateVO.getProduceUserList().forEach(produceUser -> {
//                //发送钉钉消息
//                String title = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                String content = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getMessage(),
//                        diOrder.getProjectNo(), diOrder.getOrderNo(),
//                        CollectionUtil.isNotEmpty(nameMap) && nameMap.containsKey(produceUser) ? nameMap.get(produceUser) : produceUser,
//                        SecurityUtils.getLoginUser().getSysUser().getNickName());
//                orderCommonService.sendDingTalkMessage("订单", title, content, null, produceUser);
//            });
//        }
//        if (isUpdateProduceUser) {
//            //新订单生产负责人
//            List<String> newProduceUserIdList = new ArrayList<>(orderUpdateVO.getProduceUserList().stream().distinct().toList());
//            Map<String, String> userMap = commonService.getUserNameByJob(newProduceUserIdList, null);
//            //老订单生产负责人
//            List<String> oldProduceUserIdList = new ArrayList<>(List.of(diOrder.getProduceUserId().split(",")));
//            //修改了生产负责人,判断订单生产负责人在不在排期中，如果不在排期中，将待办任务删除
//            List<DiOrderProduce> produceList = diOrderProduceService.list(new LambdaQueryWrapper<DiOrderProduce>().eq(DiOrderProduce::getOrderNo, diOrder.getOrderNo()).eq(DiOrderProduce::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(produceList)) {
//                //记录已经生成过待办任务的产品方案-生产计划的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                produceList.forEach(produce -> {
//                    codeList.add(produce.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (produce.getScheduleStatus().equals(2)) {
//                        return;
//                    }
////                    List<String> taskUserList = new ArrayList<>();
//                    newProduceUserIdList.addAll(List.of(produce.getProduceUserId().split(",")));
//                    oldProduceUserIdList.addAll(List.of(produce.getProduceUserId().split(",")));
//                    List<String> saveUserList = newProduceUserIdList.stream().filter(userId -> !oldProduceUserIdList.contains(userId)).toList();
//                    List<String> deleteUserList = oldProduceUserIdList.stream().filter(userId -> !newProduceUserIdList.contains(userId)).toList();
//                    if (CollectionUtil.isNotEmpty(saveUserList)) {

    /// /                        taskUserList.addAll(saveUserList);
//                        if (PreSaleTypeEnum.TRADE == PreSaleTypeEnum.of(preSaleMap.get(produce.getPreSaleCode()).get(0).getPreSaleType())) {
//                            log.info("贸易类 没有 生产计划待办任务2：{}", produce.getPreSaleCode());
//                            return;
//                        }
//                        //增加生产计划待办任务
//                        saveAgencyTask(produce.getPreSaleCode(), diOrder.getOrderNo(), diOrder.getProjectNo(), diOrder.getProjectNo(), AgencyTaskTypeEnum.PRODUCE_MANAGE,
//                                preSaleMap.containsKey(produce.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(produce.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(produce.getPreSaleCode()).get(0).getPreSaleName() : produce.getPreSaleCode(),
//                                getTaskStateDesc(produce.getScheduleStatus()), newProduceUserIdList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                        saveUserList.forEach(userId -> {
//                            //发送钉钉消息
//                            String title = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                            String content = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getMessage(),
//                                    diOrder.getProjectNo(), diOrder.getOrderNo(),
//                                    CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(userId) ? userMap.get(userId) : userId,
//                                    SecurityUtils.getLoginUser().getSysUser().getNickName());
//                            orderCommonService.sendDingTalkMessage("订单", title, content, null, userId);
//                        });
//                    }
//                    if (CollectionUtil.isNotEmpty(deleteUserList)) {
//                        //删除待办任务
//                        deleteAgencyTask(Collections.singletonList(produce.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.PRODUCE_MANAGE, deleteUserList.stream().distinct().toList());
//                    }
//                });
//                //过滤出未生成生产计划的产品方案，并生成待办任务
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(produce -> !codeList.contains(produce.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    //无排期方案负责人变更生成待办发送钉钉通知
//                    produceNotSchedulingUpdate(filterList, oldProduceUserIdList, newProduceUserIdList, diOrder.getOrderNo(), diOrder.getProjectNo(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, userMap);
//                }
//            } else {
//                //无排期方案负责人变更生成待办发送钉钉通知
//                produceNotSchedulingUpdate(productCostInfoVO.getDetailedList(), oldProduceUserIdList, newProduceUserIdList, diOrder.getOrderNo(), diOrder.getProjectNo(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, userMap);
//            }
//        }
//        if (isSaveProduceUser || isUpdateProduceUser) {
//            if (CollectionUtil.isNotEmpty(visibleUserList)) {
//                List<String> list = orderUpdateVO.getProduceUserList().stream().distinct().toList();
//                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(list).build());
//                String userName;
//                if (userListResult.isSuccess() && CollectionUtil.isNotEmpty(userListResult.getData())) {
//                    userName = userListResult.getData().stream().map(DdUserDTO::getName).distinct().collect(Collectors.joining("，"));
//                } else {
//                    userName = String.join(",", list);
//                }
//                //发送钉钉消息
//                String title = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                String content = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getMessage(),
//                        diOrder.getProjectNo(), diOrder.getOrderNo(), userName, SecurityUtils.getLoginUser().getSysUser().getNickName());
//                orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, null);
//            }
//        }
//    }
    private void produceNotSchedulingUpdate(List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> orderProduceList,
                                            List<String> oldProduceUserIdList, List<String> newProduceUserIdList,
                                            String orderNo, String projectNo, Date expectDeliverDate,
                                            Map<String, String> userMap) {
        List<String> codeList = orderProduceList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
        List<String> deleteUserList = oldProduceUserIdList.stream().filter(userId -> !newProduceUserIdList.contains(userId)).toList();
        if (CollectionUtil.isNotEmpty(deleteUserList)) {
            //删除待办任务
            deleteAgencyTask(codeList, orderNo, AgencyTaskTypeEnum.PRODUCE_MANAGE, deleteUserList.stream().distinct().toList());
        }
        List<String> sendUserList = newProduceUserIdList.stream().filter(userId -> !oldProduceUserIdList.contains(userId)).toList();
        if (CollectionUtil.isNotEmpty(sendUserList)) {
            orderProduceList.forEach(diOrderProduce -> {
                if (PreSaleTypeEnum.TRADE == PreSaleTypeEnum.of(diOrderProduce.getPreSaleType())) {
                    log.info("贸易类 没有 生产计划待办任务4：{}", diOrderProduce.getPreSaleCode());
                    return;
                }
                //增加生产计划待办任务
                saveAgencyTask(diOrderProduce.getPreSaleCode(), orderNo, projectNo, projectNo, AgencyTaskTypeEnum.PRODUCE_MANAGE,
                        StringUtils.isNotBlank(diOrderProduce.getPreSaleName()) ? diOrderProduce.getPreSaleName() : diOrderProduce.getPreSaleCode(),
                        "未开始", newProduceUserIdList, expectDeliverDate);
            });
            sendUserList.forEach(userId -> {
                //发送钉钉消息
                String title = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getTitle(), orderNo);
                String content = String.format(OrderDingTalkEnum.ORDER_PRODUCE_USER_UPDATE.getMessage(),
                        projectNo, orderNo, CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(userId) ? userMap.get(userId) : userId,
                        SecurityUtils.getLoginUser().getSysUser().getNickName());
                orderCommonService.sendDingTalkMessage("订单", title, content, null, userId);
            });
        }
    }

//    /**
//     * 操作安装调试负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operateEnforceUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        //判断是否新增了安装调试负责人
//        boolean isSaveEnforceUser = false;
//        //判断是否修改了安装调试负责人
//        boolean isUpdateEnforceUser = false;
//        if (StringUtils.isBlank(diOrder.getEnforceUserId())) {
//            if (StringUtils.isNotBlank(orderUpdateVO.getEnforceUserId())) {
//                isSaveEnforceUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getEnforceUserId()), "0");
//            }
//        } else {
//            if (!diOrder.getEnforceUserId().equals(orderUpdateVO.getEnforceUserId())) {
//                isUpdateEnforceUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrder.getEnforceUserId()), "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderUpdateVO.getEnforceUserId()), "0");
//            }
//        }
//        if (isSaveEnforceUser) {
//            List<String> saveUserList = Collections.singletonList(orderUpdateVO.getEnforceUserId());
//            productCostInfoVO.getDetailedList().forEach(diOrderInstall -> {
//                //增加安装调试待办任务
//                saveAgencyTask(diOrderInstall.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.INSTALL_DEBUG,
//                        StringUtils.isNotBlank(diOrderInstall.getPreSaleName()) ? diOrderInstall.getPreSaleName() : diOrderInstall.getPreSaleCode(),
//                        "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//        }
//        if (isUpdateEnforceUser) {
//            //修改了安装调试负责人,判断订单安装调试负责人在不在排期中，如果不在排期中，将待办任务删除
//            List<DiOrderInstall> installList = diOrderInstallService.list(new LambdaQueryWrapper<DiOrderInstall>().eq(DiOrderInstall::getOrderNo, diOrder.getOrderNo()).eq(DiOrderInstall::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(installList)) {
//                //记录已经生成过待办任务的产品方案-安装调试的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                installList.forEach(install -> {
//                    codeList.add(install.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (install.getScheduleStatus().equals(2)) {
//                        return;
//                    }
//                    if (!install.getEnforceUserId().equals(orderUpdateVO.getEnforceUserId())) {
//                        List<String> saveUserList = Arrays.asList(install.getEnforceUserId(), orderUpdateVO.getEnforceUserId());
//                        //增加安装调试待办任务
//                        saveAgencyTask(install.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.INSTALL_DEBUG,
//                                preSaleMap.containsKey(install.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(install.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(install.getPreSaleCode()).get(0).getPreSaleName() : install.getPreSaleCode(),
//                                getTaskStateDesc(install.getScheduleStatus()), saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    }
//                    if (!install.getEnforceUserId().equals(diOrder.getEnforceUserId())) {
//                        deleteAgencyTask(Collections.singletonList(install.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.INSTALL_DEBUG, Lists.newArrayList(diOrder.getEnforceUserId()));
//                    }
//                });
//                //过滤出未生成安装调试的产品方案，并生成待办任务
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(product -> !codeList.contains(product.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    List<String> deleteCode = filterList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                    deleteAgencyTask(deleteCode, diOrder.getOrderNo(), AgencyTaskTypeEnum.INSTALL_DEBUG, Lists.newArrayList(diOrder.getEnforceUserId()));
//                    List<String> saveList = Collections.singletonList(orderUpdateVO.getEnforceUserId());
//                    filterList.forEach(diOrderInstall -> {
//                        //增加安装调试待办任务
//                        saveAgencyTask(diOrderInstall.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.INSTALL_DEBUG,
//                                StringUtils.isNotBlank(diOrderInstall.getPreSaleName()) ? diOrderInstall.getPreSaleName() : diOrderInstall.getPreSaleCode(),
//                                "未开始", saveList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                    });
//                }
//            } else {
//                List<String> codeList = productCostInfoVO.getDetailedList().stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
//                deleteAgencyTask(codeList, diOrder.getOrderNo(), AgencyTaskTypeEnum.INSTALL_DEBUG, Lists.newArrayList(diOrder.getEnforceUserId()));
////                //根据新的安装调试负责人生成新的待办
////                saveEnforceAgencyTask(productCostInfoVO.getDetailedList(), diOrder.getOrderNo(), orderUpdateVO.getEnforceUserId(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                List<String> saveUserList = Collections.singletonList(orderUpdateVO.getEnforceUserId());
//                productCostInfoVO.getDetailedList().forEach(diOrderInstall -> {
//                    //增加安装调试待办任务
//                    saveAgencyTask(diOrderInstall.getPreSaleCode(), diOrder.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.INSTALL_DEBUG,
//                            StringUtils.isNotBlank(diOrderInstall.getPreSaleName()) ? diOrderInstall.getPreSaleName() : diOrderInstall.getPreSaleCode(),
//                            "未开始", saveUserList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                });
//            }
//        }
//        if (isSaveEnforceUser || isUpdateEnforceUser) {
//            //发送钉钉消息
//            String title = String.format(OrderDingTalkEnum.ORDER_ENFORCE_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//            String content = String.format(OrderDingTalkEnum.ORDER_ENFORCE_USER_UPDATE.getMessage(), StringUtils.isNotBlank(orderUpdateVO.getEnforceUserIdName()) ? orderUpdateVO.getEnforceUserIdName() : orderUpdateVO.getEnforceUserId(), SecurityUtils.getLoginUser().getSysUser().getNickName());
//            orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, orderUpdateVO.getEnforceUserId());
//        }
//    }

//    /**
//     * 操作生产质量负责人处理
//     *
//     * @param diOrder           订单信息
//     * @param orderUpdateVO     订单修改信息
//     * @param productCostInfoVO 产品方案信息
//     * @param visibleUserList   可见订单用户集合
//     */
//    private void operateProduceQualityUser(DiOrder diOrder, OrderUpdateVO orderUpdateVO, PreSaleQuoteProductCostInfoVO productCostInfoVO, List<String> visibleUserList) {
//        //判断是否新增了生产质量负责人
//        boolean isSaveProduceQualityUser = false;
//        //判断是否修改了生产质量负责人
//        boolean isUpdateProduceQualityUser = false;
//        if (StringUtils.isBlank(diOrder.getProduceQualityUserId())) {
//            if (CollectionUtil.isNotEmpty(orderUpdateVO.getProduceQualityUserList())) {
//                isSaveProduceQualityUser = true;
//                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), orderUpdateVO.getProduceQualityUserList().stream().distinct().toList(), "0");
//            }
//        } else {
//            List<String> produceQualityUserIds = List.of(diOrder.getProduceQualityUserId().split(","));
//            if (!ListUtils.areEqualIgnoringOrder(orderUpdateVO.getProduceQualityUserList().stream().distinct().toList(), produceQualityUserIds)) {
//                isUpdateProduceQualityUser = true;
//                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), produceQualityUserIds, "2");
//                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
//                commonService.personnelSaveProject(diOrder.getProjectNo(), orderUpdateVO.getProduceQualityUserList().stream().distinct().toList(), "0");
//            }
//        }
//        if (isSaveProduceQualityUser) {
//            List<String> produceQualityUserIdList = orderUpdateVO.getProduceQualityUserList().stream().distinct().toList();
//            Map<String, String> nameMap = commonService.getUserNameByJob(produceQualityUserIdList, null);
//            productCostInfoVO.getDetailedList().forEach(diOrderQuality -> {
//                if (PreSaleTypeEnum.TRADE == PreSaleTypeEnum.of(diOrderQuality.getPreSaleType())) {
//                    log.info("贸易类 没有 质量任务 ：{}", diOrderQuality.getPreSaleCode());
//                    return;
//                }
//                //增加生产质量待办任务
//                saveAgencyTask(diOrderQuality.getPreSaleCode(), diOrder.getOrderNo(), diOrder.getProjectNo(), diOrder.getProjectNo(), AgencyTaskTypeEnum.PRODUCE_QUALITY,
//                        StringUtils.isNotBlank(diOrderQuality.getPreSaleName()) ? diOrderQuality.getPreSaleName() : diOrderQuality.getPreSaleCode(),
//                        "未开始", produceQualityUserIdList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//            });
//            //发送钉钉消息
//            orderUpdateVO.getProduceQualityUserList().forEach(produceQualityUser -> {
//                String title = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                String content = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getMessage(),
//                        diOrder.getProjectNo(), diOrder.getOrderNo(),
//                        CollectionUtil.isNotEmpty(nameMap) && nameMap.containsKey(produceQualityUser) ? nameMap.get(produceQualityUser) : produceQualityUser,
//                        SecurityUtils.getLoginUser().getSysUser().getNickName());
//                orderCommonService.sendDingTalkMessage("订单", title, content, null, produceQualityUser);
//            });
//        }
//        if (isUpdateProduceQualityUser) {
//            //老订单生产负责人
//            List<String> oldQualityUserIdList = new ArrayList<>(List.of(diOrder.getProduceQualityUserId().split(",")));
//            //新订单生产负责人
//            List<String> newQualityUserIdList = new ArrayList<>(orderUpdateVO.getProduceQualityUserList().stream().distinct().toList());
//            Map<String, String> userMap = commonService.getUserNameByJob(newQualityUserIdList, null);
//            //修改了生产质量负责人,判断订单生产质量负责人在不在排期中，如果不在排期中，将待办任务删除
//            List<DiOrderQuality> qualityList = diOrderQualityService.list(new LambdaQueryWrapper<DiOrderQuality>().eq(DiOrderQuality::getOrderNo, diOrder.getOrderNo()).eq(DiOrderQuality::getDelFlag, 0));
//            if (CollectionUtil.isNotEmpty(qualityList)) {
//                //记录已经生成过待办任务的产品方案-生产质量的方案CODE
//                List<String> codeList = new ArrayList<>();
//                Map<String, List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed>> preSaleMap = productCostInfoVO.getDetailedList().stream().collect(Collectors.groupingBy(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode));
//                qualityList.forEach(quality -> {
//                    codeList.add(quality.getPreSaleCode());
//                    //方案已完成不发送代办
//                    if (quality.getScheduleStatus().equals(2)) {
//                        return;
//                    }
////                    List<String> taskUserList = new ArrayList<>();
//                    oldQualityUserIdList.addAll(List.of(quality.getQualityUserId().split(",")));
//                    newQualityUserIdList.addAll(List.of(quality.getQualityUserId().split(",")));
//                    List<String> saveUserList = newQualityUserIdList.stream().filter(userId -> !oldQualityUserIdList.contains(userId)).toList();
//                    List<String> deleteUserList = oldQualityUserIdList.stream().filter(userId -> !newQualityUserIdList.contains(userId)).toList();
//                    if (CollectionUtil.isNotEmpty(saveUserList)) {

    /// /                        taskUserList.addAll(saveUserList);
//                        //增加生产质量待办任务
//                        saveAgencyTask(quality.getPreSaleCode(), diOrder.getOrderNo(), diOrder.getProjectNo(), diOrder.getProjectNo(), AgencyTaskTypeEnum.PRODUCE_QUALITY,
//                                preSaleMap.containsKey(quality.getPreSaleCode()) && StringUtils.isNotBlank(preSaleMap.get(quality.getPreSaleCode()).get(0).getPreSaleName()) ? preSaleMap.get(quality.getPreSaleCode()).get(0).getPreSaleName() : quality.getPreSaleCode(),
//                                getTaskStateDesc(quality.getScheduleStatus()), newQualityUserIdList, null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
//                        saveUserList.forEach(userId -> {
//                            //发送钉钉消息
//                            String title = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                            String content = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getMessage(),
//                                    diOrder.getProjectNo(), diOrder.getOrderNo(),
//                                    CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(userId) ? userMap.get(userId) : userId,
//                                    SecurityUtils.getLoginUser().getSysUser().getNickName());
//                            orderCommonService.sendDingTalkMessage("订单", title, content, null, userId);
//                        });
//                    }
//                    if (CollectionUtil.isNotEmpty(deleteUserList)) {
//                        deleteAgencyTask(Collections.singletonList(quality.getPreSaleCode()), diOrder.getOrderNo(), AgencyTaskTypeEnum.PRODUCE_QUALITY, deleteUserList.stream().distinct().toList());
//                    }
//                });
//                //过滤出未生成生产质量的产品方案，并生成待办任务
//                List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> filterList = productCostInfoVO.getDetailedList().stream().filter(product -> !codeList.contains(product.getPreSaleCode())).toList();
//                if (CollectionUtil.isNotEmpty(filterList)) {
//                    //无排期变更负责人生成待办和发送钉钉
//                    qualityNotSchedulingUpdate(filterList, oldQualityUserIdList, newQualityUserIdList, diOrder.getOrderNo(), diOrder.getProjectNo(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, userMap);
//                }
//            } else {
//                //无排期变更负责人生成待办和发送钉钉
//                qualityNotSchedulingUpdate(productCostInfoVO.getDetailedList(), oldQualityUserIdList, newQualityUserIdList, diOrder.getOrderNo(), diOrder.getProjectNo(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, userMap);
//            }
//        }
//        if (isSaveProduceQualityUser || isUpdateProduceQualityUser) {
//            if (CollectionUtil.isNotEmpty(visibleUserList)) {
//                List<String> list = orderUpdateVO.getProduceQualityUserList().stream().distinct().toList();
//                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(list).build());
//                String userName;
//                if (userListResult.isSuccess() && CollectionUtil.isNotEmpty(userListResult.getData())) {
//                    userName = userListResult.getData().stream().map(DdUserDTO::getName).distinct().collect(Collectors.joining("，"));
//                } else {
//                    userName = String.join(",", list);
//                }
//                //发送钉钉消息
//                String title = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getTitle(), diOrder.getOrderNo());
//                String content = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getMessage(),
//                        diOrder.getProjectNo(), diOrder.getOrderNo(), userName, SecurityUtils.getLoginUser().getSysUser().getNickName());
//                orderCommonService.sendDingTalkMessage("订单", title, content, visibleUserList, null);
//            }
//        }
//    }
    private void qualityNotSchedulingUpdate(List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> orderProductList,
                                            List<String> oldQualityUserIdList, List<String> newQualityUserIdList,
                                            String orderNo, String projectNo, Date expectDeliverDate,
                                            Map<String, String> userMap) {
        List<String> codeList = orderProductList.stream().map(PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed::getPreSaleCode).distinct().toList();
        List<String> deleteUserList = oldQualityUserIdList.stream().filter(userId -> !newQualityUserIdList.contains(userId)).toList();
        if (CollectionUtil.isNotEmpty(deleteUserList)) {
            deleteAgencyTask(codeList, orderNo, AgencyTaskTypeEnum.PRODUCE_QUALITY, deleteUserList.stream().distinct().toList());
        }
        List<String> sendUserList = newQualityUserIdList.stream().filter(userId -> !oldQualityUserIdList.contains(userId)).toList();
        if (CollectionUtil.isNotEmpty(sendUserList)) {
            orderProductList.forEach(diOrderQuality -> {
                if (PreSaleTypeEnum.TRADE == PreSaleTypeEnum.of(diOrderQuality.getPreSaleType())) {
                    log.info("贸易类 没有 质量任务2 ：{}", diOrderQuality.getPreSaleCode());
                    return;
                }
                //增加生产质量待办任务
                saveAgencyTask(diOrderQuality.getPreSaleCode(), orderNo, projectNo, projectNo, AgencyTaskTypeEnum.PRODUCE_QUALITY, StringUtils.isNotBlank(diOrderQuality.getPreSaleName()) ? diOrderQuality.getPreSaleName() : diOrderQuality.getPreSaleCode(),
                        "未开始", newQualityUserIdList, expectDeliverDate);
            });
            sendUserList.forEach(userId -> {
                //发送钉钉消息
                String title = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getTitle(), orderNo);
                String content = String.format(OrderDingTalkEnum.ORDER_QUALITY_USER_UPDATE.getMessage(),
                        projectNo, orderNo, CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(userId) ? userMap.get(userId) : userId,
                        SecurityUtils.getLoginUser().getSysUser().getNickName());
                orderCommonService.sendDingTalkMessage("订单", title, content, null, userId);
            });
        }
    }

    /**
     * 新增待办任务
     *
     * @param businessKey     关联KEY
     * @param jumpKey         跳转KEY
     * @param taskTypeEnum    任务枚举
     * @param taskName        任务名称
     * @param taskStateDesc   状态中文描述
     * @param liabilityByList 责任人
     */
    private void saveAgencyTask(String businessKey, String jumpKey, String taskId, String projectNo, AgencyTaskTypeEnum taskTypeEnum, String taskName, String taskStateDesc, List<String> liabilityByList, Date expectDeliverDate) {
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(businessKey);
        agencyTaskInfoDto.setJumpKey(jumpKey);
        agencyTaskInfoDto.setTaskId(taskId);
        agencyTaskInfoDto.setProjectNo(projectNo);
        agencyTaskInfoDto.setTaskTypeEnum(taskTypeEnum);
        agencyTaskInfoDto.setTaskName(taskName);
        agencyTaskInfoDto.setTaskStateDesc(taskStateDesc);
        agencyTaskInfoDto.setLiabilityByList(liabilityByList);
        agencyTaskInfoDto.setExpectDeliverDate(expectDeliverDate);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 删除待办任务
     *
     * @param businessKeyList 关联key集合
     * @param jumpKey         跳转key
     * @param taskTypeEnum    任务枚举
     * @param userIdList      责任人
     */
    private void deleteAgencyTask(List<String> businessKeyList, String jumpKey, AgencyTaskTypeEnum taskTypeEnum, List<String> userIdList) {
        for (String businessKey : businessKeyList) {
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setBusinessKey(businessKey);
            agencyTaskInfoDto.setJumpKey(jumpKey);
            agencyTaskInfoDto.setTaskTypeEnum(taskTypeEnum);
            agencyTaskInfoDto.setType("2");
            agencyTaskInfoDto.setLiabilityByList(userIdList);
            String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        }
    }

    /**
     * 获取状态中文描述
     *
     * @param scheduleStatus 状态
     * @return 描述
     */
    private String getTaskStateDesc(Integer scheduleStatus) {
        return switch (scheduleStatus) {
            case 0 -> "未开始";
            case 1 -> "进行中";
            case 2 -> "已完成";
            default -> "未知状态";
        };
    }


    @Override
    public PageWrapper<List<ContractListPageResponse>> contractList(OrderDetailVO orderDetailVO) {
        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.lambdaQuery(DiOrder.class).eq(StringUtils.isNotBlank(orderDetailVO.getOrderNo()), DiOrder::getOrderNo, orderDetailVO.getOrderNo()).eq(Objects.nonNull(orderDetailVO.getId()), DiOrder::getId, orderDetailVO.getId()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        FindContractRequest request = new FindContractRequest();
        request.setContractNo(diOrder.getContractNo());
        request.setPageNum(1);
        request.setPageSize(1);
        return diContractService.findContractListPage(request);
    }


    @Override
    public List<OrderBillPeriodDTO> billPeriodList(OrderDetailVO orderDetailVO) {
        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.lambdaQuery(DiOrder.class).eq(StringUtils.isNotBlank(orderDetailVO.getOrderNo()), DiOrder::getOrderNo, orderDetailVO.getOrderNo()).eq(Objects.nonNull(orderDetailVO.getId()), DiOrder::getId, orderDetailVO.getId()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }

        List<DiOrderBillPeriod> orderBillPeriodList = diOrderBillPeriodService.list(Wrappers.lambdaQuery(DiOrderBillPeriod.class).eq(DiOrderBillPeriod::getOrderNo, diOrder.getOrderNo()));

        return OrderConvert.INSTANCE.orderBillPeriodTODTOList(orderBillPeriodList);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateBillPeriod(OrderBillPeriodUpdateVO orderBillPeriodUpdateVO) {
        //查询是否要变更状态
        DiOrder diOrder = diOrderMapper.selectOne(Wrappers.lambdaQuery(DiOrder.class).eq(StringUtils.isNotBlank(orderBillPeriodUpdateVO.getOrderNo()), DiOrder::getOrderNo, orderBillPeriodUpdateVO.getOrderNo()));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        //获取合同信息
        List<DiContract> contractList = diContractService.list(new LambdaQueryWrapper<DiContract>().eq(DiContract::getContractNo, diOrder.getContractNo()));
        if (CollectionUtil.isEmpty(contractList)) {
            throw new ServiceException("合同不存在");
        }
        if (contractList.size() > 1) {
            throw new ServiceException("合同不正确");
        }

        //更新订单状态 已完成
        LambdaUpdateChainWrapper<DiOrderBillPeriod> updateChainWrapper = new LambdaUpdateChainWrapper<>(diOrderBillPeriodMapper);
        updateChainWrapper.eq(DiOrderBillPeriod::getId, orderBillPeriodUpdateVO.getId());
        if (Objects.isNull(orderBillPeriodUpdateVO.getRealPayDate())) {
            updateChainWrapper.set(DiOrderBillPeriod::getRealPayDate, null);
        } else {
            updateChainWrapper.set(DiOrderBillPeriod::getRealPayDate, orderBillPeriodUpdateVO.getRealPayDate());
        }
        updateChainWrapper.update();

        //2024-12-11版本上线,根据产品要求，判断合同是否提前执行，如果是提前执行，没有待付款状态
        if ("1".equals(contractList.get(0).getIsAdvanceExecution())) {
            //待履约的订单状态才能够继续
            if (diOrder.getOrderStatus() == OrderStatusEnum.INIT.getCode() || diOrder.getOrderStatus() == OrderStatusEnum.WAIT_PREPAYMENT.getCode()) {
                boolean validBillPeriod = diOrderBillPeriodService.count(Wrappers.lambdaQuery(DiOrderBillPeriod.class).eq(DiOrderBillPeriod::getOrderNo, diOrder.getOrderNo()).isNotNull(DiOrderBillPeriod::getRealPayDate)) > 0;
                if (StringUtils.isNotEmpty(diOrder.getProjectManagerUserId()) && validBillPeriod) {
                    //更新订单状态 待履约
                    LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
                    updateWrapper.eq(DiOrder::getOrderNo, diOrder.getOrderNo());
                    updateWrapper.set(DiOrder::getOrderStatus, OrderStatusEnum.UNDER_IMPLEMENTATION.getCode());
                    updateWrapper.update();
//                    try {
//                        sendDingDingMessage(diOrder, OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS);
//                    } catch (Exception e) {
//                        log.error("发送钉钉消息失败{}", e);
//                    }
                } else if (validBillPeriod) {
                    //更新订单状态 待设定项目经理
                    LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
                    updateWrapper.eq(DiOrder::getOrderNo, diOrder.getOrderNo());
                    updateWrapper.set(DiOrder::getOrderStatus, OrderStatusEnum.WAIT_SETTING_PM.getCode());
                    updateWrapper.update();
                }
            }
        }

        rocketMQTemplate.syncSend(DestinationConstants.ORDER_TOPIC_TAG, diOrder);

        if (orderBillPeriodUpdateVO.getRealPayDate() != null) {
            OrderActualPaymentEvent orderPaymentedEvent = new OrderActualPaymentEvent();
            orderPaymentedEvent.setOrderId(diOrder.getId());
            EventBusUtils.publishEvent(orderPaymentedEvent);
        }

        return orderBillPeriodUpdateVO.getId();
    }

    public static final String DING_TALK_BUSINESS_MODULE = "订单ID：";

    /**
     * 订单消息
     *
     * @param diOrder
     * @param orderDingTalkEnum
     */
    public void sendDingDingMessage(DiOrder diOrder, OrderDingTalkEnum orderDingTalkEnum) {
        List<String> userIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(diOrder.getProjectManagerUserId())) {
            userIdList.add(diOrder.getProjectManagerUserId());
        }
        DiMarketingNiche diMarketingNiche = marketingNicheService.selectDiMarketingNicheByNo(diOrder.getNicheNo());
        Optional.ofNullable(diMarketingNiche).ifPresent(niche -> {
            userIdList.add(niche.getNicheOwner());
            if (CollectionUtils.isNotEmpty(niche.getDiMarketingContactsList())) {
                niche.getDiMarketingContactsList().stream().map(DiMarketingContacts::getContactsOwner).filter(StringUtils::isNotBlank).forEach(userIdList::add);
            }
        });
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        HashSet<String> userIdSet = Sets.newHashSet(userIdList);
        List<DiMessageList> diMessageLists = Lists.newArrayList();
        for (String userId : userIdSet) {
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule(DING_TALK_BUSINESS_MODULE);
            diMessageList.setSendingTime(new Date());
            diMessageList.setSendingUser(userId);
//            if (OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS.equals(orderDingTalkEnum)) {
//                diMessageList.setTitle(String.format(OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS.getTitle(), diOrder.getOrderNo()));
//                diMessageList.setRemarks(diMessageList.getTitle());
//                diMessageList.setContent(String.format(OrderDingTalkEnum.ORDER_CHANGE_IN_PROGRESS.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName()));
//                diMessageLists.add(diMessageList);
//            }
            if (OrderDingTalkEnum.ORDER_CHANGE_COMPLETE.equals(orderDingTalkEnum)) {
                diMessageList.setTitle(String.format(OrderDingTalkEnum.ORDER_CHANGE_COMPLETE.getTitle(), diOrder.getOrderNo()));
                diMessageList.setRemarks(diMessageList.getTitle());
                diMessageList.setContent(String.format(OrderDingTalkEnum.ORDER_CHANGE_COMPLETE.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName()));
                diMessageLists.add(diMessageList);
            }
        }
        if (CollectionUtils.isNotEmpty(diMessageLists)) {
            for (DiMessageList diMessageList : diMessageLists) {
                diMessageListService.insertDiMessageList(diMessageList);
            }
        }

    }

    /**
     * 更新实际支付日期
     *
     * @param orderBillPeriodRequest
     * @return
     */
    @Override
    public R<BillPeriodResponse> updateOrderPayRealDate(OrderBillPeriodRequest orderBillPeriodRequest) {

        List<DiOrder> diOrders = diOrderMapper.selectList(Wrappers.<DiOrder>lambdaQuery().eq(DiOrder::getContractNo, orderBillPeriodRequest.getContractNo()));
        if (CollectionUtils.isEmpty(diOrders)) {
            return R.fail("无该合同的订单");
        }

        //账期
        List<DiOrderBillPeriod> diOrderBillPeriods = diOrderBillPeriodMapper.selectList(Wrappers.<DiOrderBillPeriod>lambdaQuery().eq(DiOrderBillPeriod::getOrderNo, diOrders.get(0).getOrderNo()));
        if (CollectionUtils.isEmpty(diOrderBillPeriods)) {
            return R.fail("订单无账期");
        }
        //key为账期分类
        Map<String, OrderBillPeriodRequest.OrderBillPeriod> orderBillPeriodMap = orderBillPeriodRequest.getOrderBillPeriods().stream().collect(Collectors.toMap(OrderBillPeriodRequest.OrderBillPeriod::getBillPeriodRemark, Function.identity()));

        AtomicBoolean actualPayment = new AtomicBoolean(false);
        //项目类
        StringBuilder soBuilder = new StringBuilder();

        StringBuilder otherBuilder = new StringBuilder();
        diOrderBillPeriods.stream().forEach(diOrderBillPeriod -> {
            OrderBillPeriodRequest.OrderBillPeriod orderBillPeriod = orderBillPeriodMap.get(diOrderBillPeriod.getBillPeriodRemark());
            if (Objects.nonNull(orderBillPeriod)) {

                if (Objects.nonNull(orderBillPeriod.getRealPayDate())) {
                    if (diOrders.get(0).getOrderStatus() == OrderStatusEnum.UNDER_IMPLEMENTATION.getCode()) {
                        DiOrderBillPeriod diOrderBillPeriodT = new DiOrderBillPeriod();
                        diOrderBillPeriodT.setId(diOrderBillPeriod.getId());
                        diOrderBillPeriodT.setRealPayDate(DateUtils.toDate(orderBillPeriod.getRealPayDate()));
                        if (Objects.isNull(diOrderBillPeriod.getRealPayDate())) {
                            diOrderBillPeriodMapper.updateById(diOrderBillPeriodT);
                            actualPayment.set(true);
                        }
                    } else if (diOrders.get(0).getOrderStatus() != OrderStatusEnum.FINISHED.getCode()) {
                        OrderBillPeriodUpdateVO orderBillPeriodUpdateVO = new OrderBillPeriodUpdateVO();
                        orderBillPeriodUpdateVO.setOrderNo(diOrderBillPeriod.getOrderNo());
                        orderBillPeriodUpdateVO.setId(diOrderBillPeriod.getId());
                        orderBillPeriodUpdateVO.setRealPayDate(DateUtils.toDate(orderBillPeriod.getRealPayDate()));
                        if (Objects.isNull(diOrderBillPeriod.getRealPayDate())) {
                            iDiOrderService.updateBillPeriod(orderBillPeriodUpdateVO);
                            actualPayment.set(true);
                        }
                    }
                }/*else{
                    LambdaUpdateWrapper<DiOrderBillPeriod> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    lambdaUpdateWrapper.eq(DiOrderBillPeriod::getId,diOrderBillPeriod.getId());
                    lambdaUpdateWrapper.set(DiOrderBillPeriod::getRealPayDate,null);

                    diOrderBillPeriodMapper.update(lambdaUpdateWrapper);
                }*/
            } else {

                DiSynErpLog diSynErpLog = new DiSynErpLog();
                diSynErpLog.setRequestParam("订单账期:" + diOrders.get(0).getOrderNo() + ",账期描述:" + diOrderBillPeriod.getBillPeriodRemark() + "不匹配");
                diSynErpLog.setStatus(1);
                diSynErpLog.setCreateTime(LocalDateTime.now());
                diSynErpLogMapper.insert(diSynErpLog);

                //有自然关闭的订单
                List<OrderBillPeriodRequest.OrderBillPeriod> orderBillPeriods = orderBillPeriodRequest.getOrderBillPeriods().stream().filter(orderBillPeriodT -> 1 == orderBillPeriodT.getStatus()).collect(Collectors.toList());
                //实际支付是空
                if (Objects.isNull(diOrderBillPeriod.getRealPayDate()) && CollectionUtil.isEmpty(orderBillPeriods)) {
                    if (orderBillPeriodRequest.getOrderNo().contains("SO")) {
                        soBuilder.append("||" + diSynErpLog.getRequestParam() + "||");
                    } else {
                        otherBuilder.append("||" + diSynErpLog.getRequestParam() + "||");
                    }
                }
            }
        });

        BillPeriodResponse billPeriodResponse = new BillPeriodResponse();
        if (com.dyd.common.core.utils.StringUtils.isNotEmpty(soBuilder.toString())) {

            billPeriodResponse.setSoString(soBuilder.toString());
        }

        if (com.dyd.common.core.utils.StringUtils.isNotEmpty(otherBuilder.toString())) {
            billPeriodResponse.setOtherString(otherBuilder.toString());
        }

        if (actualPayment.get()) {
            OrderActualPaymentEvent orderPaymentedEvent = new OrderActualPaymentEvent();
            orderPaymentedEvent.setOrderId(diOrders.get(0).getId());
            EventBusUtils.publishEvent(orderPaymentedEvent);
        }

        return R.ok(billPeriodResponse);
    }

    @Override
    public PageWrapper<List<DiOrderListPageResponse>> selectOrderList(Integer pageNum, Integer pageSize) {

        Page page = PageHelper.startPage(pageNum, pageSize);

        List<DiOrder> diOrders = diOrderMapper.selectList(null);

        List<DiOrderListPageResponse> diOrderListPageResponses = diOrders.stream().map(diOrder -> {
            DiOrderListPageResponse diOrderListPageResponse = new DiOrderListPageResponse();
            diOrderListPageResponse.setContractNo(diOrder.getContractNo());
            return diOrderListPageResponse;
        }).collect(Collectors.toList());
        return PageHelp.render(page, diOrderListPageResponses);
    }

    /**
     * 时间范围查询订单
     *
     * @param request
     * @return
     */
    @Override
    public List<DiOrderListResponse> selectOrderListByTime(DiOrderListRequest request) {
        List<DiOrder> diOrders = diOrderMapper.selectList(Wrappers.<DiOrder>lambdaQuery().eq(DiOrder::getDelFlag, 0).ne(DiOrder::getOrderStatus, 3).between(DiOrder::getCreateTime, request.getStartTime(), request.getEndTime()));
        if (CollectionUtil.isEmpty(diOrders)) {
            return null;
        }

        List<DiPreSaleQuote> diPreSaleQuotes = diPreSaleQuoteMapper.selectList(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getQuoteStatus, 1).eq(DiPreSaleQuote::getApprovalStatus, 2).in(DiPreSaleQuote::getPreSaleQuoteCode, diOrders.stream().map(DiOrder::getPreSaleQuoteNo).collect(Collectors.toList())));
        if (CollectionUtil.isEmpty(diPreSaleQuotes)) {
            return null;
        }

        List<Long> preSaleIds = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().in(DiPreSaleQuoteDetail::getPreSaleQuoteId, diPreSaleQuotes.stream().map(DiPreSaleQuote::getId).collect(Collectors.toList()))).stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList());
        Map<String, List<DiPreSale>> preSaleMap = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().in(DiPreSale::getId, preSaleIds)).stream().collect(Collectors.groupingBy(DiPreSale::getNicheCode));

        List<DiPreSaleManifest> diPreSaleManifestList = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, preSaleIds).eq(DiPreSaleManifest::getDelFlag, 0));

        Map<Long, List<DiPreSaleManifest>> preSalemanifestMap = diPreSaleManifestList.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));
        Map<Long, DiMateriel> materielMap = diMaterielMapper.selectList(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifestList.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList()))).stream().collect(Collectors.toMap(DiMateriel::getId, Function.identity()));
        //合同
        Map<String, DiContract> diContractMap = diContractMapper.selectList(Wrappers.<DiContract>lambdaQuery().in(DiContract::getContractNo, diOrders.stream().map(DiOrder::getContractNo).collect(Collectors.toList()))).stream().collect(Collectors.toMap(DiContract::getContractNo, Function.identity()));
        if (CollectionUtil.isNotEmpty(diOrders)) {
            return diOrders.stream().map(diOrder -> {
                DiOrderListResponse diOrderListPageResponse = new DiOrderListResponse();
                diOrderListPageResponse.setContractNo(diOrder.getContractNo());
                diOrderListPageResponse.setOrderNo(diOrder.getOrderNo());
                diOrderListPageResponse.setNicheNo(diOrder.getNicheNo());
                diOrderListPageResponse.setU9OrderNo(diOrder.getU9OrderNo());
                diOrderListPageResponse.setDeptId(Long.parseLong(diOrder.getDeptId()));
                diOrderListPageResponse.setProjectNo(diContractMap.get(diOrder.getContractNo()).getProjectNo());

                List<DiOrderListResponse.Item> items = new ArrayList<>();
                List<DiPreSale> diPreSalesT = preSaleMap.get(diOrder.getNicheNo());
                if (CollectionUtil.isNotEmpty(diPreSalesT)) {
                    for (DiPreSale diPreSale : diPreSalesT) {
                        List<DiPreSaleManifest> diPreSaleManifestListT = preSalemanifestMap.get(diPreSale.getId());
                        if (CollectionUtil.isNotEmpty(diPreSaleManifestListT)) {
                            for (DiPreSaleManifest diPreSaleManifest : diPreSaleManifestListT) {
                                DiMateriel diMateriel = materielMap.get(diPreSaleManifest.getMaterialVersion());
                                if (Objects.isNull(diMateriel)) {
                                    continue;
                                }
                                DiOrderListResponse.Item item = new DiOrderListResponse.Item();
                                item.setMaterielNo(diMateriel.getMaterielNo());
                                items.add(item);
                            }

                        }
                    }
                    diOrderListPageResponse.setItems(items);
                }

                return diOrderListPageResponse;
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 查询排期信息
     *
     * @param orderId
     * @return
     */
    @Override
    public SchedulingInformationResponse querySchedulingInformation(Long orderId) {

        SchedulingInformationResponse response = new SchedulingInformationResponse();
        DiOrder order = diOrderMapper.selectById(orderId);
        response.setSystemsEngineer(order.getSystemsEngineer());
        DiOrderExt ext = orderExtService.queryByOrderNo(order.getOrderNo());
        SaveSchedulingInformationRequest request = null;
        if (ext != null && StringUtils.isNotBlank(ext.getTempSchedule())) {
            request = JSON.parseObject(ext.getTempSchedule(), SaveSchedulingInformationRequest.class);
            response.setUnifiedScheduling(request.getUnifiedScheduling());
        }

        List<Long> preSaleIds = preSaleQuoteService.queryPreSaleIdListByQuoteNo(order.getPreSaleQuoteNo());
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleIds);
        Map<String, DiOrderElectricDesign> electricDesignMap = diOrderElectricDesignService.queryByOrderNo(order.getOrderNo());
        Map<String, DiOrderMachineDesign> machineDesignMap = diOrderMachineDesignService.queryByOrderNo(order.getOrderNo());
        Map<String, DiOrderMaterielPlan> materielPlansMap = diOrderMaterielPlanService.queryByOrderNo(order.getOrderNo());
        Map<String, DiOrderProduce> prodProduceMap = diOrderProduceService.queryByOrderNo(order.getOrderNo());
        Map<String, DiOrderQuality> qualityMap = diOrderQualityService.queryByOrderNo(order.getOrderNo());


        for (DiPreSale diPreSale : diPreSales) {
            PreSaleSchedulingInformationResponse preSaleSchedulingInformationResponse = new PreSaleSchedulingInformationResponse();
            preSaleSchedulingInformationResponse.setPreSaleCode(diPreSale.getPreSaleCode());
            preSaleSchedulingInformationResponse.setPreSaleName(diPreSale.getPreSaleName());
            preSaleSchedulingInformationResponse.setPreSaleId(diPreSale.getId());
            PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(diPreSale.getPreSaleType());
            PreSaleSchedulingDTO saleSchedulingDTO = null;
            if (request != null && CollectionUtils.isNotEmpty(request.getList())) {
                saleSchedulingDTO = request.getList().stream().filter(x -> x.getPreSaleId().equals(diPreSale.getId())).findFirst().orElse(null);
            }


            //电气设计
            if (StringUtils.isNotEmpty(order.getElectricDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD) {
                preSaleSchedulingInformationResponse.setElectricStatus(true);
                if (electricDesignMap.containsKey(diPreSale.getPreSaleCode()) && electricDesignMap.get(diPreSale.getPreSaleCode()).getExpectStartTime() != null) {
                    preSaleSchedulingInformationResponse.setElectricStart(electricDesignMap.get(diPreSale.getPreSaleCode()).getExpectStartTime());
                    preSaleSchedulingInformationResponse.setElectricEnd(electricDesignMap.get(diPreSale.getPreSaleCode()).getExpectEndTime());
                } else if (saleSchedulingDTO != null) {
                    preSaleSchedulingInformationResponse.setElectricStart(saleSchedulingDTO.getElectricStart());
                    preSaleSchedulingInformationResponse.setElectricEnd(saleSchedulingDTO.getElectricEnd());
                }
            }
            //机械
            if (StringUtils.isNotEmpty(order.getMachineDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD) {
                preSaleSchedulingInformationResponse.setMachineStatus(true);
                if (machineDesignMap.containsKey(diPreSale.getPreSaleCode()) && machineDesignMap.get(diPreSale.getPreSaleCode()).getExpectStartTime() != null) {
                    preSaleSchedulingInformationResponse.setMachineStart(machineDesignMap.get(diPreSale.getPreSaleCode()).getExpectStartTime());
                    preSaleSchedulingInformationResponse.setMachineEnd(machineDesignMap.get(diPreSale.getPreSaleCode()).getExpectEndTime());
                    preSaleSchedulingInformationResponse.setBomExpectEndTime(machineDesignMap.get(diPreSale.getPreSaleCode()).getBomExpectEndTime());
                    if (preSaleSchedulingInformationResponse.getBomExpectEndTime() == null) {
                        preSaleSchedulingInformationResponse.setBomExpectEndTime(DateUtils.addDays(order.getCreateTime(), 7));
                    }
                } else if (saleSchedulingDTO != null) {
                    preSaleSchedulingInformationResponse.setMachineStart(saleSchedulingDTO.getMachineStart());
                    preSaleSchedulingInformationResponse.setMachineEnd(saleSchedulingDTO.getMachineEnd());
                    preSaleSchedulingInformationResponse.setBomExpectEndTime(saleSchedulingDTO.getBomExpectEndTime());
                    if (preSaleSchedulingInformationResponse.getBomExpectEndTime() == null) {
                        preSaleSchedulingInformationResponse.setBomExpectEndTime(saleSchedulingDTO.getBomExpectEndTime());
                    }
                }
            }
            //物料
            if (StringUtils.isNotBlank(order.getPurchaseUserId())) {
                preSaleSchedulingInformationResponse.setMaterielStatus(true);
                if (materielPlansMap.containsKey(diPreSale.getPreSaleCode()) && materielPlansMap.get(diPreSale.getPreSaleCode()).getExpectStartTime() != null) {
                    preSaleSchedulingInformationResponse.setMaterielStart(materielPlansMap.get(diPreSale.getPreSaleCode()).getExpectStartTime());
                    preSaleSchedulingInformationResponse.setMaterielEnd(materielPlansMap.get(diPreSale.getPreSaleCode()).getExpectEndTime());
                } else if (saleSchedulingDTO != null) {
                    preSaleSchedulingInformationResponse.setMaterielStart(saleSchedulingDTO.getMaterielStart());
                    preSaleSchedulingInformationResponse.setMaterielEnd(saleSchedulingDTO.getMaterielEnd());
                }
            }
            //生产
            if (StringUtils.isNotBlank(order.getProduceUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {
                preSaleSchedulingInformationResponse.setProduceStatus(true);
                if (prodProduceMap.containsKey(diPreSale.getPreSaleCode()) && prodProduceMap.get(diPreSale.getPreSaleCode()).getProduceStartTime() != null) {
                    preSaleSchedulingInformationResponse.setProduceStart(prodProduceMap.get(diPreSale.getPreSaleCode()).getProduceStartTime());
                    preSaleSchedulingInformationResponse.setProduceEnd(prodProduceMap.get(diPreSale.getPreSaleCode()).getProduceEndTime());
                } else if (saleSchedulingDTO != null) {
                    preSaleSchedulingInformationResponse.setProduceStart(saleSchedulingDTO.getProduceStart());
                    preSaleSchedulingInformationResponse.setProduceEnd(saleSchedulingDTO.getProduceEnd());
                }
            }
            //        质量任务
            if (StringUtils.isNotBlank(order.getProduceQualityUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {
                preSaleSchedulingInformationResponse.setQualityStatus(true);
                if (qualityMap.containsKey(diPreSale.getPreSaleCode()) && qualityMap.get(diPreSale.getPreSaleCode()).getCheckStartTime() != null) {
                    preSaleSchedulingInformationResponse.setQualityStart(qualityMap.get(diPreSale.getPreSaleCode()).getCheckStartTime());
                    preSaleSchedulingInformationResponse.setQualityEnd(qualityMap.get(diPreSale.getPreSaleCode()).getCheckEndTime());
                } else if (saleSchedulingDTO != null) {
                    preSaleSchedulingInformationResponse.setQualityStart(saleSchedulingDTO.getQualityStart());
                    preSaleSchedulingInformationResponse.setQualityEnd(saleSchedulingDTO.getQualityEnd());
                }
            }

            response.getList().add(preSaleSchedulingInformationResponse);
        }
        return response;
    }

    /**
     * 保存排期信息
     * <p>
     * 这个地方不处理待办，单纯更新排期
     *
     * @param request
     */
    @Override
    public void saveSchedulingInformation(SaveSchedulingInformationRequest request) {
        DiOrder order = diOrderMapper.selectById(request.getOrderId());
        DiOrderExt ext = new DiOrderExt();
        ext.setOrderNo(order.getOrderNo());
        ext.setId(order.getId());
        ext.setTempSchedule(JSON.toJSONString(request));
        orderExtService.saveOrUpdate(ext);


        if (request.isFinished()) {
            DiOrder update = new DiOrder();
            update.setId(order.getId());
            // 校验所有方案都设置日期了
            checkSchedule(request);
            //更新订单为已排期
            update.setScheduleStatus(OrderScheduleStatusEnum.COMPLETED.getCode());
            order.setScheduleStatus(OrderScheduleStatusEnum.COMPLETED.getCode());
            this.updateById(update);
            if (this.canRun(order)) {
                this.createDesignTask(request);
            }
        }

        this.updateSystemEngineer(order.getId(), request.getSystemsEngineer(), order.getSystemsEngineer());

        if (request.isFinished()) {
            OrderScheduleDoneEvent event = new OrderScheduleDoneEvent();
            event.setOrderId(order.getId());
            EventBusUtils.publishEvent(event);
        }
    }

    private void updateSystemEngineer(Long id, String newUser, String oldUser) {
        if (Objects.equals(newUser, oldUser)) {
            return;
        }
        this.lambdaUpdate().eq(DiOrder::getId, id)
                .set(DiOrder::getSystemsEngineer, newUser).update();
        OrderUpdateSystemsEngineerEvent scheduledEvent = new OrderUpdateSystemsEngineerEvent();
        scheduledEvent.setOrderId(id);
        scheduledEvent.setOldSystemsEngineer(oldUser);
        scheduledEvent.setNewSystemsEngineer(newUser);
        EventBusUtils.publishEvent(scheduledEvent);
    }

    private void checkSchedule(SaveSchedulingInformationRequest request) {
        DiOrder order = diOrderMapper.selectById(request.getOrderId());
        List<Long> preSaleIds = preSaleQuoteService.queryPreSaleIdListByQuoteNo(order.getPreSaleQuoteNo());
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleIds);

        Boolean allSet = true;
        for (DiPreSale diPreSale : diPreSales) {
            PreSaleSchedulingDTO req = request.getList().stream().filter(item -> item.getPreSaleId().equals(diPreSale.getId())).findFirst().orElse(null);
            if (req == null) {
                req = new PreSaleSchedulingDTO();
                req.setPreSaleId(diPreSale.getId());
            }
            //DiPreSale diPreSale = diPreSales.stream().filter(item -> item.getId().equals(req.getPreSaleId())).findFirst().get();
            DiPreSaleManifest manifest = diPreSaleService.queryFirstManifest(req.getPreSaleId());
            PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(diPreSale.getPreSaleType());
            //电气设计
            if (StringUtils.isNotEmpty(order.getElectricDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD
                    && req.getElectricEnd() == null
            ) {
                allSet = false;
                break;
            }
            //机械
            if (StringUtils.isNotEmpty(order.getMachineDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD
                    && req.getMachineEnd() == null
            ) {

                allSet = false;
                break;
            }
            //物料
            if (StringUtils.isNotBlank(order.getPurchaseUserId()) && req.getMaterielEnd() == null
            ) {
                allSet = false;
                break;
            }
            //生产
            if (StringUtils.isNotBlank(order.getProduceUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && req.getProduceEnd() == null) {
                allSet = false;
                break;

            }
            //        质量任务
            if (StringUtils.isNotBlank(order.getProduceQualityUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && req.getQualityEnd() == null) {
                allSet = false;
                break;
            }
        }

        if (!allSet) {
            throw new ServiceException("请设置所有方案下所有任务开始时间和结束时间");
        }
    }

    public void createOrderTask(SaveSchedulingInformationRequest request) {

        if (request.getUnifiedScheduling()) {
            var first = request.getList().get(0);

            request.getList().forEach(item -> {
                item.setElectricStart(first.getElectricStart());
                item.setElectricEnd(first.getElectricEnd());
                item.setMachineStart(first.getMachineStart());
                item.setMachineEnd(first.getMachineEnd());
                item.setBomExpectEndTime(first.getBomExpectEndTime());
                item.setMaterielStart(first.getMaterielStart());
                item.setMaterielEnd(first.getMaterielEnd());
                item.setProduceStart(first.getProduceStart());
                item.setProduceEnd(first.getProduceEnd());
                item.setQualityStart(first.getQualityStart());
                item.setQualityEnd(first.getQualityEnd());
            });
        }

        DiOrder order = diOrderMapper.selectById(request.getOrderId());
        List<Long> preSaleIds = preSaleQuoteService.queryPreSaleIdListByQuoteNo(order.getPreSaleQuoteNo());
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleIds);

        List<ProduceAddVO> produceAddVOS = new ArrayList<>();
        List<OrderQualityAddVO> qualityAddVOS = new ArrayList<>();
        List<OrderMaterielAddVO> materielAddVOS = new ArrayList<>();

        for (DiPreSale diPreSale : diPreSales) {
            PreSaleSchedulingDTO req = request.getList().stream().filter(item -> item.getPreSaleId().equals(diPreSale.getId())).findFirst().orElse(null);
            if (req == null) {
                req = new PreSaleSchedulingDTO();
                req.setPreSaleId(diPreSale.getId());
            }
            //DiPreSale diPreSale = diPreSales.stream().filter(item -> item.getId().equals(req.getPreSaleId())).findFirst().get();
            DiPreSaleManifest manifest = diPreSaleService.queryFirstManifest(req.getPreSaleId());
            PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(diPreSale.getPreSaleType());

            //物料
            if (StringUtils.isNotBlank(order.getPurchaseUserId())) {
                if (req.getMaterielEnd() != null) {
                    DiMarketingNiche marketingNiche = marketingNicheService.queryByNicheId(diPreSale.getNicheId());

                    OrderMaterielAddVO add = new OrderMaterielAddVO();
                    add.setOrderNo(order.getOrderNo());
                    add.setPreSaleId(diPreSale.getId());
                    add.setPreSaleCode(diPreSale.getPreSaleCode());
                    if (!marketingNiche.getNeedTechSupport()) {
                        add.setPurchaseUserId("dyd007");
                        add.setPurchaseUserName("史秋娇");
                    } else {
                        add.setPurchaseUserId("dyd086");
                        add.setPurchaseUserName("鲍赛兰");
                    }
                    add.setExpectStartTime(req.getMaterielStart());
                    add.setExpectEndTime(req.getMaterielEnd());
                    add.setSupplyDay(manifest.getSupplyDay());
                    add.setIgnoreDingDing(false); //如果是已完成排期，才发送钉钉
                    materielAddVOS.add(add);
                }
            }
            //生产
            if (StringUtils.isNotBlank(order.getProduceUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {

                if (req.getProduceEnd() != null) {

                    OrderProduceAddVO add = new OrderProduceAddVO();
                    add.setOrderNo(order.getOrderNo());
                    add.setPreSaleId(diPreSale.getId());
                    add.setPreSaleCode(diPreSale.getPreSaleCode());
                    add.setProduceUserList(Arrays.stream(StringUtils.split("LR001", ",")).toList());
                    add.setProduceStartTime(req.getProduceStart());
                    add.setProduceEndTime(req.getProduceEnd());
                    add.setScheduleStatus(OrderScheduleStatusEnum.PENDING.getCode());
                    add.setPreSaleDay(manifest.getProduceDay());
                    add.setIgnoreDingDing(false);//如果是已完成排期，才发送钉钉
                    ProduceAddVO addVO = new ProduceAddVO();
                    addVO.setOrderProduceAddVO(add);
                    produceAddVOS.add(addVO);

                }

            }
            //        质量任务
            if (StringUtils.isNotBlank(order.getProduceQualityUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {

                if (req.getQualityEnd() != null) {

                    OrderQualityAddVO quality = new OrderQualityAddVO();
                    quality.setOrderNo(order.getOrderNo());
                    quality.setPreSaleId(diPreSale.getId());
                    quality.setPreSaleCode(diPreSale.getPreSaleCode());
                    quality.setProduceQualityUserList(Arrays.stream(StringUtils.split("dyd142", ",")).toList());
                    quality.setCheckStartTime(req.getQualityStart());
                    quality.setCheckEndTime(req.getQualityEnd());
                    quality.setIgnoreDingDing(false);//如果是已完成排期，才发送钉钉
                    //quality.setPreSaleDay(manifest.getProduceDay());
                    qualityAddVOS.add(quality);
                }
            }
        }
        for (ProduceAddVO add : produceAddVOS) {
            diOrderProduceService.add(add);
        }
        for (OrderQualityAddVO add : qualityAddVOS) {
            diOrderQualityService.add(add);
        }
        for (OrderMaterielAddVO add : materielAddVOS) {
            diOrderMaterielPlanService.add(add);
        }

        this.createDesignTask(request);
    }

    /**
     * 根据订单编号查询付款周期信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<DiOrderBillPeriod> queryBillPeriodByOrderNo(String orderNo) {
        return this.diOrderBillPeriodService.queryByOrderNo(orderNo);
    }

    @Override
    public String orderDataClean() {
        List<DiOrder> orderList = diOrderMapper.selectList(new LambdaQueryWrapper<DiOrder>().eq(DiOrder::getDelFlag, 0));
        if (CollectionUtil.isEmpty(orderList)) {
            return "无清洗数据";
        }
        //获取履约中的订单
        List<DiOrder> inProgressOrders = orderList.stream().filter(order -> order.getOrderStatus().equals(OrderStatusEnum.UNDER_IMPLEMENTATION.getCode())).toList();
        //获取订单下产品方案
        List<String> preSaleQuoteNoList = inProgressOrders.stream().map(DiOrder::getPreSaleQuoteNo).distinct().toList();
        Map<String, List<Long>> preSaleIdMap = new HashMap<>();
        preSaleQuoteNoList.forEach(preSaleQuoteNo -> {
            List<Long> preSaleIdList = preSaleQuoteService.queryPreSaleIdListByQuoteNo(preSaleQuoteNo);
            preSaleIdMap.put(preSaleQuoteNo, preSaleIdList);
        });
        LambdaUpdateChainWrapper<DiOrder> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderMapper);
        List<String> errorList = new ArrayList<>();
        for (DiOrder order : orderList) {
            updateWrapper.eq(DiOrder::getId, order.getId());
            if (Arrays.asList(OrderStatusEnum.INIT.getCode(), OrderStatusEnum.WAIT_PREPAYMENT.getCode(), OrderStatusEnum.WAIT_SETTING_PM.getCode()).contains(order.getOrderStatus())) {
                //立项阶段
                updateWrapper.set(DiOrder::getOrderStage, OrderStageEnum.ONE.getCode());
                updateWrapper.update();
            } else if (order.getOrderStatus().equals(OrderStatusEnum.FINISHED.getCode())) {
                //已完成
                updateWrapper.set(DiOrder::getOrderStage, OrderStageEnum.EIGHT.getCode());
                updateWrapper.update();
            } else if (order.getOrderStatus().equals(OrderStatusEnum.UNDER_IMPLEMENTATION.getCode())) {
                //履约中的订单暂时不处理，通过家吉那边调用监听修改订单状态
//                if (OrderScheduleStatusEnum.COMPLETED.getCode().equals(order.getScheduleStatus())) {
//                    //履约中
//                    List<Long> preSaleIdList = preSaleIdMap.get(order.getPreSaleQuoteNo());
//                    if (CollectionUtil.isEmpty(preSaleIdList)) {
//                        log.error("订单：{}，方案为空！无法清洗！", order.getOrderNo());
//                        errorList.add(order.getOrderNo() + "：方案为空");
//                        continue;
//                    }
//                    //获取方案
//                    List<DiPreSale> preSaleList = diPreSaleMapper.selectBatchIds(preSaleIdList);
//                    if (CollectionUtil.isEmpty(preSaleList)) {
//                        log.error("订单：{}，未查询到方案！无法清洗！方案ID：{}", order.getOrderNo(), JSONUtil.toJsonStr(preSaleList));
//                        errorList.add(order.getOrderNo() + "：方案未查询到，方案ID：" + JSONUtil.toJsonStr(preSaleIdList));
//                        continue;
//                    }
//                    //记录每个任务在当前订单中最大阶段
//                    List<Integer> stageList = new ArrayList<>();
//                    preSaleList.forEach(preSale -> {
//                        OrderStageEnum orderStageEnum = OrderStageEnum.getByOrderPreSaleStatus(preSale.getOrderPreSaleStatus());
//                        if (Objects.nonNull(orderStageEnum)) {
//                            stageList.add(orderStageEnum.getCode());
//                        }
//                    });
//                    if (CollectionUtil.isEmpty(stageList)) {
//                        log.error("订单：{}，不满足修改条件", order.getOrderNo());
//                        continue;
//                    }
//                    Integer orderStage = stageList.stream().max(Integer::compareTo).orElse(null);
//                    updateWrapper.set(DiOrder::getOrderStage, orderStage);
//                    updateWrapper.update();
//                }
            } else {
                log.error("订单：{}，状态不正确！无法清洗！", order.getOrderNo());
                errorList.add(order.getOrderNo() + "：订单状态不正确");
            }
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            log.error("DiOrderServiceImpl---orderDataClean()---订单清洗失败数据：{}", JSONUtil.toJsonStr(errorList));
            return JSONUtil.toJsonStr(errorList);
        } else {
            log.info("DiOrderServiceImpl---orderDataClean()---订单清洗成功！");
            return "成功";
        }
    }

    /**
     * 判断订单是否可以运行
     * 设定项目经理 且 （绑款 或提前执行）
     *
     * @param order
     * @return
     */
    @Override
    public boolean canRun(DiOrder order) {
        List<DiOrderBillPeriod> periodList = this.queryBillPeriodByOrderNo(order.getOrderNo());
        DiContract contract = contractService.queryByNo(order.getContractNo());
        boolean payed = periodList.stream().anyMatch(item -> Objects.nonNull(item.getRealPayDate()));
        boolean hasManager = com.dyd.common.core.utils.StringUtils.isNotBlank(order.getProjectManagerUserId());
        boolean advanceExecution = YES.textEquals(contract.getIsAdvanceExecution()) || YES.textEquals(order.getIsAdvanceExecution());
        boolean scheduleStatus = OrderScheduleStatusEnum.COMPLETED.getCode().equals(order.getScheduleStatus());
        boolean canExecute = hasManager && (payed || advanceExecution) && scheduleStatus;
        log.info("订单:{} 流转条件满足:{},    1. 已设定项目经理:{},    2. 已绑定预付款:{} 或提前执行:{}  [合同：{}，订单:{}],3.已排期:{}",
                order.getId(), canExecute, hasManager, payed,
                advanceExecution, YES.textEquals(contract.getIsAdvanceExecution()),
                YES.textEquals(order.getIsAdvanceExecution()), scheduleStatus);

        return canExecute;
    }

    @Override
    public String getSystemEngineer(DiOrder order) {
        String notifyUser = defaultUser;
        List<DiPreSale> preSaleList = this.quotesService.queryPreSaleByQuoteCode(order.getPreSaleQuoteNo());
        boolean allTrade = preSaleList.stream().allMatch(item -> item.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode()));
        if (allTrade) {
            notifyUser = tradeDefaultUser;
        } else {
            DiMarketingNicheDemand nicheDemand = demandService.queryByNicheNo(order.getNicheNo());
            if (nicheDemand != null) {
                String userId = dictDataService.queryDictLabel("order_schedule_user", nicheDemand.getIndustry());
                if (com.dyd.common.core.utils.StringUtils.isNotBlank(userId)) {
                    notifyUser = userId;
                }
            }
        }
        return notifyUser;
    }

    /**
     * 放弃订单
     *
     * @param request
     */
    @Override
    public void giveUp(GiveUpOrderRequest request, String opeatorCode) {
        DiOrder order = this.getById(request.getOrderId());

        if (order.getOrderStatus().equals(OrderStatusEnum.GIVE_UP.getCode())) {
            throw new ServiceException("订单已被放弃");
        }

        String operatorName = remoteUserService.queryUserNickName(opeatorCode);

        this.lambdaUpdate().eq(DiOrder::getId, request.getOrderId())
                .set(DiOrder::getOrderStatus, OrderStatusEnum.GIVE_UP.getCode())
                .set(DiOrder::getOrderStage, OrderStageEnum.GIVE_UP.getCode())
                .update();

        //this.sendOrderGiveUpNotify(order);
        //技术支持复核、电气设计、机械设计、物料计划、生产任务、质量任务，新增状态， 手动放弃
        List<String> userList = new ArrayList<>();
        userList.addAll(diOrderSupportCheckService.giveUp(order.getOrderNo()));
        userList.addAll(diOrderElectricDesignService.giveUp(order.getOrderNo()));
        userList.addAll(diOrderMachineDesignService.giveUp(order.getOrderNo()));
        userList.addAll(diOrderMaterielPlanService.giveUp(order.getOrderNo()));
        userList.addAll(diOrderProduceService.giveUp(order.getOrderNo()));
        userList.addAll(diOrderQualityService.giveUp(order.getOrderNo()));

        //关闭代办、审批
        taskService.clearTask(order.getOrderNo());
        approvalService.clearApproval(order.getOrderNo());

        DiPreSaleQuote quote = quotesService.getByNo(order.getPreSaleQuoteNo());
        List<DiPreSale> preSaleList = quotesService.queryPreSaleList(quote.getId());

        for (DiPreSale diPreSale : preSaleList) {
            taskService.clearTask(diPreSale.getPreSaleCode());
            taskService.clearTask(diPreSale.getId().toString());
        }

        //建单群钉钉消息
        dingtalkClient.sendGroupText("项目ID：" + order.getProjectNo() + " 的订单 已被放弃，详情咨询 " + operatorName,
                "项目ID：" + order.getProjectNo() + " 的订单 已被放弃，详情咨询 " + operatorName, orderCreateGroupId,
                dingtalkU9Config.getAppKey(), dingtalkU9Config.getAppSecret()
        );

        //通知项目经理
        if (StringUtils.isNotBlank(order.getProjectManagerUserId())) {
            userList.add(order.getProjectManagerUserId());
        }
        if (StringUtils.isNotBlank(order.getSystemsEngineer())) {
            userList.add(order.getSystemsEngineer());
        }
        DiMarketingNiche niche = marketingNicheService.queryByCode(order.getNicheNo());
        if (niche != null) {
            userList.add(niche.getNicheOwner());
        }
        //相关人钉钉消息
        this.sendOrderGiveUpDingDing(order.getOrderNo(), userList, operatorName);

        //退出告警
        warningService.clearByOrderGiveUp(order.getOrderNo());

        //关闭审批中的报价单？ 修改方案的履约状态
        OrderStageChangedEvent event = new OrderStageChangedEvent();
        event.setOrderId(request.getOrderId());
        event.setOrderNo(order.getOrderNo());
        event.setOrderStage(OrderStageEnum.GIVE_UP);
        EventBusUtils.publishEvent(event);
    }

    @Override
    public void sendOrderGiveUpDingDing(String orderNo, List<String> userIdList, String opeator) {

        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        List<String> userList = userIdList.stream().filter(StringUtils::isNotBlank)
                .flatMap(x -> Stream.of(x.split(","))).distinct().toList();
        DiOrder order = this.queryByOrderNo(orderNo);
        for (String u : userList) {
            if (StringUtils.isBlank(u)) {
                continue;
            }
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule(DING_TALK_BUSINESS_MODULE);
            diMessageList.setSendingTime(new Date());
            diMessageList.setSendingUser(u);

            diMessageList.setTitle(String.format(OrderDingTalkEnum.ORDER_GIVE_UP_NOTICE.getTitle(), order.getProjectNo(), opeator));
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent(String.format(OrderDingTalkEnum.ORDER_GIVE_UP_NOTICE.getMessage(), order.getProjectNo(), opeator));
            this.diMessageListService.insertDiMessageList(diMessageList);
        }


    }
}
