package com.dyd.di.pre.domain.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 报价单新增
 */
@Data
public class PreSaleQuoteAddRequest {

    /**
     * 商机编码
     */
    private String nicheCode;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系方式
     */
    private String contactsPhone;

    /**
     * 是否提前开票
     */
    private Integer preInvoice;

    /**
     * 订单期望交付日期
     */
    private LocalDate expecteDate;

    /**
     * 报价单对应产品方案
     */
    private List<PreSaleQuoteAddDetail> preSaleQuoteDetailList;

    /**
     * 账期
     */
    private List<PreSaleQuotePeriod> preSaleQuotePeriods;


    @Data
    public static class PreSaleQuotePeriod {

        /**
         * 账期
         */
        private Integer billPeriod;

        /**
         * 支付金额
         */
        private BigDecimal payAmount;

        /**
         * 账期描述
         */
        private String remark;

        /**
         * 付款比例
         */
        private BigDecimal payPercent;

        /**
         * 计划支付日
         */
        private LocalDate planPayDate;

        /**
         * 税率
         */
        private BigDecimal rate;

        /**
         * 发票情况
         */
        private String invoiceDesc;

        /**
         * 实际支付日期
         */
        private LocalDate realPayDate;
    }


    @Data
    public static class PreSaleQuoteAddDetail {

        /**
         * 产品方案ID
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;

        /**
         * 产品方案编码
         */
        private String preSaleCode;

        /**
         * 售价报价
         */
        private BigDecimal saleQuote;

        /**
         * 交期报价(天)
         */
        private BigDecimal deliveryQuote;


        /**
         * 国家
         */
        @JsonAlias("countryName")
        private String country;

        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 省
         */
        @JsonAlias("provinceName")
        private String province;

        /**
         * 省编码
         */
        private String provinceCode;

        /**
         * 市
         */
        @JsonAlias("cityName")
        private String city;

        /**
         * 市编码
         */
        private String cityCode;

        /**
         * 区
         */
        @JsonAlias("areaName")
        private String area;

        /**
         * 区编码
         */
        private String areaCode;

        /**
         * 销售服务报价
         */
        private BigDecimal saleServiceQuote;

        /**
         * 销售服务周期报价（人/天）
         */
        private BigDecimal saleServiceCycle;

        /**
         * 需求套数
         */
        private Integer num;

        /**
         * 成本测算合计
         */
        private BigDecimal costFeeSum;
    }


    /**
     * 国家
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 统一收货地址
     */
    private String commonDeliveryAddress;

    /**
     * 商务费用
     */
    private BigDecimal businessCost;

    /**
     * 共享销售列表
     */
    private List<PreSaleQuotaShareRequest> shareList;


    /**
     * 期望交期（周）
     */
    private Integer expectationDeliveryWeek;

    /**
     * 是否提前执行：1.否，2.是
     */
    private Integer isAdvanceExecution;
}
