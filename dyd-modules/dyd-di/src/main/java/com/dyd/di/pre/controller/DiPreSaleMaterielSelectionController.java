package com.dyd.di.pre.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.di.material.iservice.IDiMaterialInquiryFormService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.pre.conver.PreSaleConverUtil;
import com.dyd.di.pre.domain.request.*;
import com.dyd.di.pre.domain.response.PreSaleMaterielSelectionAddResponse;
import com.dyd.di.pre.domain.response.PreSaleMaterielSelectionResponse;
import com.dyd.di.pre.domain.response.PreSaleMaterielSelectionSummaryResponse;
import com.dyd.di.pre.entity.DiPreSaleMaterielSelection;
import com.dyd.di.pre.service.IDiPreSaleMaterielSelectionService;
import com.dyd.di.pre.service.IPreSaleBomService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 方案物料选型
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@RestController
@RequestMapping("/pre/materielSelection")
@Validated
public class DiPreSaleMaterielSelectionController {


    @Autowired
    private IDiPreSaleMaterielSelectionService selectionService;

    @Autowired
    private IPreSaleBomService preSaleBomService;

    @Autowired
    private IDiMaterialInquiryFormService inquiryFormService;

    @Autowired
    private PreSaleConverUtil preSaleConverUtil;


    /**
     * 新增 选型物料
     *
     * @param request
     * @return
     */
    @PostMapping("/addSelection")
    public R<PreSaleMaterielSelectionAddResponse> addSelection(@RequestBody PreSaleMaterielSelectionAddRequest request) {
        DiPreSaleMaterielSelection selection = preSaleConverUtil.convertToSelection(request);
        selectionService.save(selection);
        return R.ok(new PreSaleMaterielSelectionAddResponse(selection.getId()));
    }


    /**
     * 编辑 选型物料
     *
     * @param request
     * @return
     */
    @PostMapping("/updateSelection")
    public R updateSelection(@RequestBody PreSaleMaterielSelectionUpdateRequest request) {
        DiPreSaleMaterielSelection selection = preSaleConverUtil.convertToSelection(request);
        var update = Wrappers.<DiPreSaleMaterielSelection>lambdaUpdate()
                .set(DiPreSaleMaterielSelection::getMaterielName, selection.getMaterielName())
                .set(DiPreSaleMaterielSelection::getBrand, selection.getBrand())
                .set(DiPreSaleMaterielSelection::getPatternNo, selection.getPatternNo())
                .set(DiPreSaleMaterielSelection::getProductStandard, selection.getProductStandard())
                .set(DiPreSaleMaterielSelection::getSelectionExplain, selection.getSelectionExplain())
                .set(DiPreSaleMaterielSelection::getAttachmentKey, selection.getAttachmentKey())
                .set(DiPreSaleMaterielSelection::getConsumeQuantity, selection.getConsumeQuantity())
                .set(DiPreSaleMaterielSelection::getGuideCosts, selection.getGuideCosts())

                .set(DiPreSaleMaterielSelection::getGuideProductionCosts, selection.getGuideProductionCosts())
                .set(DiPreSaleMaterielSelection::getGuideMaterielDuration, selection.getGuideMaterielDuration())
                .set(DiPreSaleMaterielSelection::getSupplier, selection.getSupplier())
                .set(DiPreSaleMaterielSelection::getSupplierId, selection.getSupplierId())
                .set(DiPreSaleMaterielSelection::getValidityDate, selection.getValidityDate())
                .eq(DiPreSaleMaterielSelection::getId, selection.getId());
        selectionService.getBaseMapper().update(selection, update);
        return R.ok();
    }

    /**
     * 删除 选型物料
     *
     * @param request
     * @return
     */
    @PostMapping("/deleteSelection")
    public R deleteSelection(@RequestBody PreSaleMaterielSelectionDelRequest request) {

        DiPreSaleMaterielSelection selection = selectionService.getById(request.getId());
        if (selection == null) {
            return R.fail("数据不存在");
        }

        if (preSaleBomService.inBom(request.getId())) {
            return R.fail("该数据已被添加进bom，请先移除bom中该数据，再删除");
        }
        if ((selection.getQuotedStatus() != 2 && selection.getQuotedStatus() != 4) && inquiryFormService.inInquiryForm(request.getId())) {
            return R.fail("该物料已发起选型单，不允许删除");
        }
        selectionService.removeById(request.getId());
        return R.ok();
    }

    /**
     * 获取 选型物料 列表
     *
     * @param qry
     * @return
     */
    @PostMapping("SelectionQryByPre")
    public R<List<PreSaleMaterielSelectionResponse>> selectionQryByPre(@Valid @RequestBody PreSaleMaterielSelectionQry qry) {
        List<PreSaleMaterielSelectionResponse> list = selectionService.selectionQryByPre(qry);
        return R.ok(list);
    }

    /**
     * 查询物料选型按钮状态
     *
     * @param qry
     * @return
     */
    @PostMapping("SelectionSummaryQryByPre")
    public R<PreSaleMaterielSelectionSummaryResponse> SelectionSummaryQryByPre(@Valid @RequestBody PreSaleMaterielSelectionQry qry) {
        List<PreSaleMaterielSelectionResponse> list = selectionService.selectionQryByPre(qry);
        PreSaleMaterielSelectionSummaryResponse response = new PreSaleMaterielSelectionSummaryResponse();
        if (CollectionUtils.isEmpty(list)) {
            response.setCanInquiry(false);
        } else {
            response.setCanInquiry(list.stream().anyMatch(PreSaleMaterielSelectionResponse::isCanInquiry));
        }
        return R.ok(response);
    }
}
