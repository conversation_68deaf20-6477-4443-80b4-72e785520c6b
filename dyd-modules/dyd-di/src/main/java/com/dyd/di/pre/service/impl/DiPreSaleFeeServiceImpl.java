package com.dyd.di.pre.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.MoneyUtils;
import com.dyd.di.common.DiHelper;
import com.dyd.di.order.enums.OrderPreSaleStatusEnum;
import com.dyd.di.pre.domain.request.SaveManifestRealFeeRequest;
import com.dyd.di.pre.domain.response.FeeItem;
import com.dyd.di.pre.domain.response.PreSaleDetailResponse;
import com.dyd.di.pre.domain.response.PreSaleRealFeeResponse;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.enums.PreSaleStatusEnum;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.mapper.DiPreSaleManifestMapper;
import com.dyd.di.pre.service.DiPreSaleFeeService;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class DiPreSaleFeeServiceImpl implements DiPreSaleFeeService {

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private IDiPreSaleService diPreSaleService;

    /**
     * 查询费用
     *
     * @param diPreSaleManifest
     * @return
     */
    public PreSaleRealFeeResponse queryFeeForReal(DiPreSaleManifest diPreSaleManifest) {

        PreSaleRealFeeResponse response = new PreSaleRealFeeResponse();
        BeanUtils.copyProperties(diPreSaleManifest, response);
        return response;
    }

    /**
     * 更新费用
     *
     * @param preSale
     */
    @Override
    public void updatePreSaleFee(DiPreSale preSale) {
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDelFlag, 0).eq(DiPreSaleManifest::getDiPreSaleId, preSale.getId()));

        //List<PreSaleDetailResponse.Fee> feeList = new ArrayList<>();
        List<BigDecimal> guideDurations = Lists.newArrayList();
        BigDecimal costFeeTotal = BigDecimal.ZERO;
        for (DiPreSaleManifest diPreSaleManifest : diPreSaleManifests) {
            PreSaleDetailResponse.Fee fee = diPreSaleService.getFee(diPreSaleManifest);
            this.saveCalculateFee(diPreSaleManifest, fee);
            //feeList.add(fee);
            PreSaleTypeEnum type = PreSaleTypeEnum.of(preSale.getPreSaleType());
            if (type == PreSaleTypeEnum.BIG_NON_STANDARD) {
                FeeItem feeItem = this.getFeeItemFromReal(diPreSaleManifest);
                costFeeTotal = costFeeTotal.add(feeItem.getFeeTotal() == null ? BigDecimal.ZERO : feeItem.getFeeTotal());
                guideDurations.add(feeItem.computeGuideDurationForProject());
            } else if (type == PreSaleTypeEnum.TRADE) {
                costFeeTotal = costFeeTotal.add(fee.getFeeTotal());
                guideDurations.add(fee.getGuideDuration());
            } else {
                costFeeTotal = costFeeTotal.add(fee.getFeeTotal());
                guideDurations.add(fee.computeGuideDurationForProject());
            }
        }
        //更新总费用
        BigDecimal guideDuration = guideDurations.stream().filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);

        diPreSaleService.lambdaUpdate()
                .set(DiPreSale::getGuideDuration, guideDuration)
                .set(DiPreSale::getCostFeeTotal, costFeeTotal)
                .eq(DiPreSale::getId, preSale.getId())
                .update();
    }

    private void saveCalculateFee(DiPreSaleManifest fest, PreSaleDetailResponse.Fee fee) {
        diPreSaleManifestMapper.update(Wrappers.<DiPreSaleManifest>lambdaUpdate()
                .set(DiPreSaleManifest::getTechnicalSupportQuotation, fee.getTechSupportFee())
                .set(DiPreSaleManifest::getMechanicalDesignQuotation, fee.getMechineDesignFee())
                .set(DiPreSaleManifest::getElectricalDesignQuotation, fee.getElectricDesignFee())
                .set(DiPreSaleManifest::getMaterialFee, fee.getMaterialFee())
                .set(DiPreSaleManifest::getGuideProductionCosts, fee.getGuideProductionCosts())
                .set(DiPreSaleManifest::getTemporaryMaterialCosts, fee.getTemporaryMaterialCosts())
                .set(DiPreSaleManifest::getTemporaryProductionCosts, fee.getTemporaryProductionCosts())
                //.set(DiPreSaleManifest::getProduceFee, fee.getProductFee()) 确认废弃
                .set(DiPreSaleManifest::getImplementFee, fee.getDeliveryDebugFee())
                .set(DiPreSaleManifest::getPackageFee, fee.getPackageFee())
                .set(DiPreSaleManifest::getRiskCost, fee.getRiskFee())
                .set(DiPreSaleManifest::getTechnicalSupportDuration, fee.getTechSupportDay() != null ? fee.getTechSupportDay().intValue() : null)
                .set(DiPreSaleManifest::getMechanicalDesignDuration, fee.getMechineDay() != null ? fee.getMechineDay().intValue() : null)
                .set(DiPreSaleManifest::getElectricalDesignPeriod, fee.getElectricDay() != null ? fee.getElectricDay().intValue() : null)
                .set(DiPreSaleManifest::getSupplyDay, fee.getSupplyDay() != null ? fee.getSupplyDay().intValue() : null)
                .set(DiPreSaleManifest::getProduceDay, fee.getProductDay() != null ? fee.getProductDay().intValue() : null)
                .set(DiPreSaleManifest::getImplementDay, fee.getDeliveryDebugDay() != null ? fee.getDeliveryDebugDay().intValue() : null)
                .set(DiPreSaleManifest::getMaterielGuideDuration, fee.getGuideDuration())
                .set(DiPreSaleManifest::getExpirationDateEnd, fee.getEndTime())
                .set(DiPreSaleManifest::getFeeTotal, fee.getFeeTotal())
                .set(DiPreSaleManifest::getBomDesignDay, fee.getBomDesignDay())
                .eq(DiPreSaleManifest::getId, fest.getId())
        );

        //diPreSaleManifestMapper.updateById(fest);
    }

    @Override
    public FeeItem queryCalcFee(DiPreSaleManifest request) {
        FeeItem feesItem = new FeeItem();
        feesItem.setTechSupportFee(request.getTechnicalSupportQuotation());
        feesItem.setMechineDesignFee(request.getMechanicalDesignQuotation());
        feesItem.setElectricDesignFee(request.getElectricalDesignQuotation());
        feesItem.setMaterialFee(request.getMaterialFee());
        feesItem.setGuideProductionCosts(request.getGuideProductionCosts());
        feesItem.setTemporaryMaterialCosts(request.getTemporaryMaterialCosts());
        feesItem.setTemporaryProductionCosts(request.getTemporaryProductionCosts());
        //feesItem.setProductFee(request.getProduceFee());
        feesItem.setDeliveryDebugFee(request.getImplementFee());
        feesItem.setPackageFee(request.getPackageFee());
        feesItem.setRiskFee(request.getRiskCost());
        feesItem.setTechSupportDay(request.getTechnicalSupportDuration() == null ? BigDecimal.ZERO : new BigDecimal(request.getTechnicalSupportDuration()));
        feesItem.setMechineDay(request.getMechanicalDesignDuration() == null ? BigDecimal.ZERO : new BigDecimal(request.getMechanicalDesignDuration()));
        feesItem.setElectricDay(request.getElectricalDesignPeriod() == null ? BigDecimal.ZERO : new BigDecimal(request.getElectricalDesignPeriod()));
        feesItem.setSupplyDay(request.getSupplyDay() == null ? BigDecimal.ZERO : new BigDecimal(request.getSupplyDay()));
        feesItem.setProductDay(request.getProduceDay() == null ? BigDecimal.ZERO : new BigDecimal(request.getProduceDay()));
        feesItem.setDeliveryDebugDay(request.getImplementDay() == null ? BigDecimal.ZERO : new BigDecimal(request.getImplementDay()));
        feesItem.setGuideDuration(request.getMaterielGuideDuration());
        feesItem.setEndTime(request.getExpirationDateEnd());
        feesItem.setFeeTotal(request.getFeeTotal());
        feesItem.setBomDesignDay(request.getBomDesignDay());
        return feesItem;
    }

    /**
     * 保存售前方案清单实金额
     *
     * @param request
     */
    @Override
    public void saveRealFee(SaveManifestRealFeeRequest request) {

        DiPreSaleManifest diPreSaleManifest = diPreSaleManifestMapper.selectDiPreSaleManifestById(request.getManifestId());

        BigDecimal feeTotal = MoneyUtils.sum(request.getRealTechSupportFee(),
                request.getRealMachineDesignFee(),
                request.getRealElectricDesignFee(),
                request.getRealMaterialFee(),
                request.getRealGuideProductionCosts(),
                request.getRealTemporaryMaterialCosts(),
                request.getRealTemporaryProductionCosts(),
                request.getRealProductFee(),
                request.getRealDeliveryDebugFee(),
                request.getRealPackageFee(),
                request.getRealRiskFee());

        BigDecimal preSaleGuideDuration = DiHelper.computeGuideDurationForProject(
                request.getRealTechSupportDay(),
                request.getRealElectricDay(),
                request.getRealMachineDay(),
                request.getRealSupplyDay(),
                request.getRealProductDay(),
                request.getRealDeliveryDebugDay(),
                request.getRealBomDesignDay());


        this.diPreSaleManifestMapper.update(Wrappers.lambdaUpdate(DiPreSaleManifest.class)
                .eq(DiPreSaleManifest::getId, request.getManifestId())
                .set(DiPreSaleManifest::getRealTechSupportFee, request.getRealTechSupportFee())
                .set(DiPreSaleManifest::getRealMachineDesignFee, request.getRealMachineDesignFee())
                .set(DiPreSaleManifest::getRealElectricDesignFee, request.getRealElectricDesignFee())
                .set(DiPreSaleManifest::getRealMaterialFee, request.getRealMaterialFee())
                .set(DiPreSaleManifest::getRealGuideProductionCosts, request.getRealGuideProductionCosts())
                .set(DiPreSaleManifest::getRealTemporaryMaterialCosts, request.getRealTemporaryMaterialCosts())
                .set(DiPreSaleManifest::getRealTemporaryProductionCosts, request.getRealTemporaryProductionCosts())
                .set(DiPreSaleManifest::getRealProductFee, request.getRealProductFee())
                .set(DiPreSaleManifest::getRealDeliveryDebugFee, request.getRealDeliveryDebugFee())
                .set(DiPreSaleManifest::getRealPackageFee, request.getRealPackageFee())
                .set(DiPreSaleManifest::getRealRiskFee, request.getRealRiskFee())
                .set(DiPreSaleManifest::getRealTechSupportDay, request.getRealTechSupportDay())
                .set(DiPreSaleManifest::getRealMachineDay, request.getRealMachineDay())
                .set(DiPreSaleManifest::getRealElectricDay, request.getRealElectricDay())
                .set(DiPreSaleManifest::getRealSupplyDay, request.getRealSupplyDay())
                .set(DiPreSaleManifest::getRealProductDay, request.getRealProductDay())
                .set(DiPreSaleManifest::getRealDeliveryDebugDay, request.getRealDeliveryDebugDay())
                .set(DiPreSaleManifest::getRealGuideDuration, request.getRealGuideDuration())
                .set(DiPreSaleManifest::getRealValidTime, request.getRealValidTime())
                .set(DiPreSaleManifest::getRealFeeTotal, feeTotal)
                .set(DiPreSaleManifest::getRealBomDesignDay, request.getRealBomDesignDay())
        );

        DiPreSale diPreSale = diPreSaleService.getById(diPreSaleManifest.getDiPreSaleId());
        if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
            //更新总费用 cost_fee_total，手动工期
            diPreSaleService.lambdaUpdate().eq(DiPreSale::getId, diPreSaleManifest.getDiPreSaleId()).set(DiPreSale::getCostFeeTotal, feeTotal).set(DiPreSale::getGuideDuration, preSaleGuideDuration).update();
        }
    }

    /**
     * 根据费用类型获取费用
     *
     * @param preSale
     * @param diPreSaleManifests
     * @return
     */
    @Override
    public FeeItem getFeeItem(DiPreSale preSale, DiPreSaleManifest diPreSaleManifests) {
        FeeItem feeItem = new FeeItem();
        if (PreSaleTypeEnum.of(preSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
            return this.getFeeItemFromReal(diPreSaleManifests);
        } else if (reCalcStatus(preSale)) {
            var fee = diPreSaleService.getFee(diPreSaleManifests);
            BeanUtils.copyProperties(fee, feeItem);
        } else {
            feeItem = this.queryCalcFee(diPreSaleManifests);
        }
        return feeItem;
    }

    /**
     * 重新计算状态
     *
     * @param preSale
     * @return
     */
    @Override
    public Boolean reCalcStatus(DiPreSale preSale) {
        if (PreSaleStatusEnum.DEMAND_AND_VERIFICATION.getCode().equals(preSale.getPreSaleStatus())
                || PreSaleStatusEnum.TECHNICAL_SUPPORT.getCode().equals(preSale.getPreSaleStatus())
                || PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(preSale.getPreSaleStatus())
                || (preSale.getOrderPreSaleStatus() != null && !OrderPreSaleStatusEnum.FIVE.getCode().equals(preSale.getOrderPreSaleStatus())) // 非终态的售中
        ) {
            return true;
        } else {
            return false;
        }
    }

    private FeeItem getFeeItemFromReal(DiPreSaleManifest request) {
        FeeItem feesItem = new FeeItem();
        feesItem.setTechSupportFee(request.getRealTechSupportFee());
        feesItem.setMechineDesignFee(request.getRealMachineDesignFee());
        feesItem.setElectricDesignFee(request.getRealElectricDesignFee());
        feesItem.setMaterialFee(request.getRealMaterialFee());
        feesItem.setGuideProductionCosts(request.getRealGuideProductionCosts());
        feesItem.setTemporaryMaterialCosts(request.getRealTemporaryMaterialCosts());
        feesItem.setTemporaryProductionCosts(request.getRealTemporaryProductionCosts());
        //feesItem.setProductFee(request.getRealProductFee());
        feesItem.setDeliveryDebugFee(request.getRealDeliveryDebugFee());
        feesItem.setPackageFee(request.getRealPackageFee());
        feesItem.setRiskFee(request.getRealRiskFee());
        feesItem.setTechSupportDay(request.getRealTechSupportDay());
        feesItem.setMechineDay(request.getRealMachineDay());
        feesItem.setElectricDay(request.getRealElectricDay());
        feesItem.setSupplyDay(request.getRealSupplyDay());
        feesItem.setProductDay(request.getRealProductDay());
        feesItem.setDeliveryDebugDay(request.getRealDeliveryDebugDay());
        feesItem.setGuideDuration(request.getRealGuideDuration());
        feesItem.setEndTime(DiHelper.localDateFromDate(request.getRealValidTime()));
        feesItem.setFeeTotal(request.getRealFeeTotal());
        feesItem.setBomDesignDay(request.getRealBomDesignDay() == null ? BigDecimal.ZERO : request.getRealBomDesignDay());
        return feesItem;
    }
}
