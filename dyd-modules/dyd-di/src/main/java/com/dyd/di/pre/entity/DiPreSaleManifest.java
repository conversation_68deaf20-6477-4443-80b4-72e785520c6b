package com.dyd.di.pre.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.dyd.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dyd.common.core.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 售前方案清单对象 di_pre_sale_manifest
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DiPreSaleManifest extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 售前方案id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long diPreSaleId;

    /**
     * 方案清单版本
     */
    private Integer manifestVersion;

    /**
     * 物料版本
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long materialVersion;

    /**
     * 售卖分类
     */
    private String sellCategory;

    /**
     * 产品分类
     */
    private String productType;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 物料号
     */
    private String materialCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 有效期止
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationDateEnd;

    /**
     * 费用合计
     */
    private BigDecimal feeTotal;

    /**
     * 设计费用
     */
    private BigDecimal deviseFee;

    /**
     * 物料费用
     */
    private BigDecimal materialFee;

    /**
     * 生产费用
     */
    //废弃
    //private BigDecimal produceFee;

    /**
     * 实施费用
     */
    private BigDecimal implementFee;

    /**
     * 包装费用
     */
    private BigDecimal packageFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 研发周期(天)
     */
    private Integer rdDay;

    /**
     * 供应货期(天)
     */
    private Integer supplyDay;

    /**
     * 生产周期(天)
     */
    private Integer produceDay;

    /**
     * 实施周期(天)
     */
    private Integer implementDay;

    /**
     * 收货地址
     */
    private String deliveryAddress;

    /**
     * 链路id
     */
    private String traceId;

    /**
     * 物料长
     */
    private BigDecimal materielLength;

    /**
     * 物料宽
     */
    private BigDecimal materielWidth;

    /**
     * 物料高
     */
    private BigDecimal materielHigh;

    /**
     * 机械设计报价
     */
    private BigDecimal mechanicalDesignQuotation;

    /**
     * 机械设计工期
     */
    private Integer mechanicalDesignDuration;

    /**
     * 电器设计报价
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal electricalDesignQuotation;

    /**
     * 电器设计工期
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer electricalDesignPeriod;

    /**
     * 技术支持报价
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal technicalSupportQuotation;

    /**
     * 技术支持工期
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer technicalSupportDuration;

    /**
     * 风险费用
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal riskCost;

    /**
     * 需求套数
     */
    private Integer useNum;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;


    /**
     * 物料制作费
     */
    private BigDecimal guideProductionCosts;

    /**
     * 物料工期
     */
    private BigDecimal materielGuideDuration;

    /**
     * [手动]技术支持费用
     */
    private BigDecimal realTechSupportFee;
    /**
     * [手动]机械设计费用
     */
    private BigDecimal realMachineDesignFee;
    /**
     * [手动]电气设计费用
     */
    private BigDecimal realElectricDesignFee;
    /**
     * [手动]物料费用
     */
    private BigDecimal realMaterialFee;
    /**
     * [手动]物料制作费
     */
    private BigDecimal realGuideProductionCosts;
    /**
     * [手动]临时物料费
     */
    private BigDecimal realTemporaryMaterialCosts;
    /**
     * [手动]临时制作费
     */
    private BigDecimal realTemporaryProductionCosts;
    /**
     * [手动]生产费用
     */
    private BigDecimal realProductFee;
    /**
     * [手动]交付调试费用
     */
    private BigDecimal realDeliveryDebugFee;
    /**
     * [手动]包装费用
     */
    private BigDecimal realPackageFee;
    /**
     * [手动]风险费用
     */
    private BigDecimal realRiskFee;
    /**
     * [手动]技术支持周期
     */
    private BigDecimal realTechSupportDay;
    /**
     * [手动]机械方案周期
     */
    private BigDecimal realMachineDay;
    /**
     * [手动]电气方案周期
     */
    private BigDecimal realElectricDay;
    /**
     * [手动]供应货期
     */
    private BigDecimal realSupplyDay;
    /**
     * [手动]生产周期
     */
    private BigDecimal realProductDay;
    /**
     * [手动]交付调试周期
     */
    private BigDecimal realDeliveryDebugDay;
    /**
     * [手动]物料工期
     */
    private BigDecimal realGuideDuration;

    /**
     * [手动]费用合计
     */
    private BigDecimal realFeeTotal;


    /**
     * [手动]物料有效期
     */
    private Date realValidTime;

    /**
     * 临时物料费
     */
    private BigDecimal temporaryMaterialCosts;

    /**
     * 临时制作费
     */
    private BigDecimal temporaryProductionCosts;

    /**
     * 报价合计
     */
    private BigDecimal quoteTotal;

    /**
     * u9领料单号
     */
    private String materialsRequisition;
    /**
     * bom设计周期
     */
    private BigDecimal bomDesignDay;
    /**
     * [手动]bom设计周期
     */
    private BigDecimal realBomDesignDay;
}
