package com.dyd.di.marketing.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.RelationTypeEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.ServletUtils;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.enums.ApprovalNoticeEnum;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.marketing.constants.NicheApprovalConstants;
import com.dyd.di.marketing.domain.*;
import com.dyd.di.marketing.domain.dto.FullAddressDTO;
import com.dyd.di.pre.domain.request.ApprovalProcessRequest;
import com.dyd.di.pre.domain.request.NicheApprovalRequest;
import com.dyd.system.api.vo.*;
import com.dyd.di.marketing.enums.NicheDingTalkEnum;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.marketing.service.*;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.process.service.IDiProjectRelationService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.RemoteYuDaoService;
import com.dyd.system.api.domain.SysDictData;
import com.dyd.system.api.domain.SysUser;
import com.dydtec.base.camunda.api.domain.dto.CheckUserPermissionDTO;
import com.dydtec.infras.core.base.bean.BaseResponse;
import com.google.common.base.Joiner;
import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 编辑商机管理器
 */
@Slf4j
@Component
@RefreshScope
public class DiMarketingNicheEditManage {

    private final ThreadLocal<DiMarketingNiche> nicheLocal = ThreadLocal.withInitial(DiMarketingNiche::new);
    private final ThreadLocal<List<String>> msgUserListLocal = ThreadLocal.withInitial(Lists::newArrayList);
    private final ThreadLocal<List<DiMarketingContacts>> contactListLocal = ThreadLocal.withInitial(Lists::newArrayList);


    @Value("${niche.importanceAEmployees:dyd003,dyd010}")
    private String importanceAEmployee;

    @Value("${niche.importance.processDefinitionId}")
    private String importanceProcessDefinitionId;

    @Value("${niche.no_importance.processDefinitionId}")
    private String noImportanceProcessDefinitionId;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private IDiMarketingNicheService diMarketingNicheService;

    @Autowired
    private IDiMarketingContactsService diMarketingContactsService;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Resource
    private CommonService commonService;

    @Resource
    private IDiProjectRelationService diProjectRelationService;
    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private IDiMessageListService iDiMessageListService;

    @Autowired
    private DiMarketingNicheDemandService diMarketingNicheDemandService;

    @Autowired
    private DiMarketingNicheDemandFileService diMarketingNicheDemandFileService;

    @Autowired
    private DiMarketingNicheDemandIntentionService diMarketingNicheDemandIntentionService;

    @Autowired
    private DiMarketingNicheFlameAccessoryService diMarketingNicheFlameAccessoryService;

    @Autowired
    private DiMarketingNicheRequirementAccessoryService diMarketingNicheRequirementAccessoryService;

    @Autowired
    private DiMarketingNicheRequirementCombustorService diMarketingNicheRequirementCombustorService;

    @Autowired
    private DiMarketingNicheRequirementElectrollService diMarketingNicheRequirementElectrollService;

    @Autowired
    private DiMarketingNicheRequirementHotstoveService diMarketingNicheRequirementHotstoveService;

    @Autowired
    private DiMarketingNicheRequirementValveService diMarketingNicheRequirementValveService;

    @Autowired
    private IDiProcessProjectService diProcessProjectService;

    @Autowired
    private com.dyd.di.contract.iservice.IDiContractService iDiContractService;

//    @Autowired
//    private IDiMessageListService iDiMessageListService;

    @Autowired
    private DiMarketingNicheMapper diMarketingNicheMapper;

    @Autowired
    private DiFeasibilityVerifyProcessService diFeasibilityVerifyProcessService;
    @Autowired
    private IDiMarketingCustomerService diMarketingCustomerService;

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteYuDaoService remoteYuDaoService;

    @Autowired
    private DiAgencyApprovalService diAgencyApprovalService;

    public Boolean checkApprovalBy(NicheApprovalRequest request) {
        //获取当前登录者
        if (null == SecurityUtils.getLoginUser() || null == SecurityUtils.getLoginUser().getSysUser()) {
            throw new ServiceException("用户信息不正确");
        }
        //获取登录用户角色权限
        String userId = SecurityUtils.getLoginUser().getSysUser().getUserId().toString();

        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(request.getId());
        String approvalUserIdBys = diMarketingNiche.getApprovalUserIdBys();
        if (StringUtils.isNotBlank(approvalUserIdBys)) {
            return Arrays.stream(approvalUserIdBys.split(",")).anyMatch((Predicate<String>) userId::equals);
        }
        return false;
    }


    /**
     * 新增商机处理器
     *
     * @param diMarketingNiche 商机
     * @return 结果
     */
    @Transactional
    public JSONObject manage(DiMarketingNiche diMarketingNiche) {
        // 参数校验
        this.valid(diMarketingNiche);

        // 数据初始化 | 调整
        this.init(diMarketingNiche);

        // 数据入库
        JSONObject json = this.doManage(diMarketingNiche);

        // 后置处理 -> 代办 钉钉
        this.post(diMarketingNiche, json);

        return json;
    }



    /**
     * 校验入参
     *
     * @param diMarketingNiche
     */
    private void valid(DiMarketingNiche diMarketingNiche) {
        if (Objects.isNull(diMarketingNiche.getId())) {
            throw new ServiceException("商机ID不能为空");
        }
        //获取商机信息
        DiMarketingNiche niche = diMarketingNicheService.selectDiMarketingNicheById(diMarketingNiche.getId(), false);
        if (null == niche) {
            throw new ServiceException("商机不存在");
        }
        //判断商机状态
        if (!niche.getNicheStatus().equals("0") && !niche.getNicheStatus().equals("1") && !niche.getNicheStatus().equals("8")
                && !niche.getNicheStatus().equals("2")&& !niche.getNicheStatus().equals("6")) {
            throw new ServiceException("当前商机状态不允许编辑");
        }
        //判断是否生成了销售报价单
        List<DiPreSaleQuote> preSaleQuoteList = diPreSaleQuoteMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuote>()
                .eq(DiPreSaleQuote::getNicheCode, diMarketingNiche.getNicheNo())
                .ne(DiPreSaleQuote::getQuoteStatus, 2));
        if (CollectionUtil.isNotEmpty(preSaleQuoteList)) {
            if(!diMarketingNiche.getNicheStatus().equals("4") && !diMarketingNiche.getNicheStatus().equals("5")){
                preSaleQuoteList.forEach(preSaleQuote -> {
                    if (preSaleQuote.getApprovalStatus().equals(1) || preSaleQuote.getApprovalStatus().equals(2)) {
                        throw new ServiceException("存在销售报价不允许编辑");
                    }
                });
            }
        }
        nicheLocal.set(niche);
    }

    /**
     * 初始化数据
     *
     * @param diMarketingNiche
     */
    private void init(DiMarketingNiche diMarketingNiche) {
        DiMarketingNiche cacheNiche = nicheLocal.get();
        if (("4".equals(diMarketingNiche.getNicheStatus()) || "5".equals(diMarketingNiche.getNicheStatus()))) {
            lostAndAbandonInit(diMarketingNiche, cacheNiche);
        }

        //获取老的销售
        String oldNicheOwner = StringUtils.isNotBlank(cacheNiche.getNicheOwner()) ? cacheNiche.getNicheOwner() : null;

        //获取老的共享销售
        String oldSharedBy = StringUtils.isNotBlank(cacheNiche.getSharedBy()) ? cacheNiche.getSharedBy() : null;

        diMarketingNiche.setUpdateBy(SecurityUtils.getUsername());
        diMarketingNiche.setUpdateTime(DateUtils.getNowDate());

        List<DiMarketingContacts> contactsList = diMarketingNiche.getDiMarketingContactsList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(contactsList)) {
            contactListLocal.get().addAll(contactsList);
        }

        //记录共享销售和主要销售(待办任务，勿删)
        List<String> msgUserList = msgUserListLocal.get();
        msgUserList.add(diMarketingNiche.getNicheOwner());

        String sharedBy = ListUtils.emptyIfNull(contactsList).stream().filter(Objects::nonNull).map(item -> {
            item.setBelonging("商机");
            item.setBusinessId(diMarketingNiche.getNicheNo());
            msgUserList.add(item.getContactsOwner());
            return item.getContactsOwner();
        }).filter(StringUtils::isNoneEmpty).collect(Collectors.joining(","));

        diMarketingNiche.setSharedBy(sharedBy);

        DiMarketingNicheDemand nicheDemand = diMarketingNiche.getDiMarketingNicheDemand();
        if (Objects.nonNull(nicheDemand)) {
            FullAddressDTO useFullAddress = nicheDemand.getUseFullAddress();
            if (Objects.nonNull(useFullAddress)) {
                String countryCode = Optional.ofNullable(useFullAddress.getCountryCode()).orElse("");
                String countryName = Optional.ofNullable(useFullAddress.getCountryName()).orElse("");
                String nation = Joiner.on("/").join(countryCode, countryName);
                nicheDemand.setUseAddressNation(nation);

                String provinceCode = Optional.ofNullable(useFullAddress.getProvinceCode()).orElse("");
                String provinceName = Optional.ofNullable(useFullAddress.getProvinceName()).orElse("");
                String cityCode = Optional.ofNullable(useFullAddress.getCityCode()).orElse("");
                String cityName = Optional.ofNullable(useFullAddress.getCityName()).orElse("");
                String areaCode = Optional.ofNullable(useFullAddress.getAreaCode()).orElse("");
                String areaName = Optional.ofNullable(useFullAddress.getAreaName()).orElse("");
                String area = Joiner.on("/").join(
                        provinceCode, provinceName, cityCode, cityName, areaCode, areaName
                );
                nicheDemand.setUseAddressDist(area);

                nicheDemand.setUseAddressInfo(useFullAddress.getAddress());
            }

            FullAddressDTO deliveryFullAddress = nicheDemand.getDeliveryFullAddress();
            if (Objects.nonNull(deliveryFullAddress)) {
                String countryCode = Optional.ofNullable(deliveryFullAddress.getCountryCode()).orElse("");
                String countryName = Optional.ofNullable(deliveryFullAddress.getCountryName()).orElse("");
                String nation = Joiner.on("/").join(countryCode, countryName);
                nicheDemand.setDeliveryAddressNation(nation);

                String provinceCode = Optional.ofNullable(deliveryFullAddress.getProvinceCode()).orElse("");
                String provinceName = Optional.ofNullable(deliveryFullAddress.getProvinceName()).orElse("");
                String cityCode = Optional.ofNullable(deliveryFullAddress.getCityCode()).orElse("");
                String cityName = Optional.ofNullable(deliveryFullAddress.getCityName()).orElse("");
                String areaCode = Optional.ofNullable(deliveryFullAddress.getAreaCode()).orElse("");
                String areaName = Optional.ofNullable(deliveryFullAddress.getAreaName()).orElse("");
                String area = Joiner.on("/").join(
                        provinceCode, provinceName, cityCode, cityName, areaCode, areaName
                );
                nicheDemand.setDeliveryAddressDist(area);

                nicheDemand.setDeliveryAddressInfo(deliveryFullAddress.getAddress());
            }

            List<String> demandScopeList = nicheDemand.getDemandScopeList();
            String demandScope = ListUtils.emptyIfNull(demandScopeList).stream().filter(Objects::nonNull).collect(Collectors.joining(","));
            nicheDemand.setDemandScope(Optional.of(demandScope).orElse(""));
        }
    }

    private void lostAndAbandonInit(DiMarketingNiche diMarketingNiche, DiMarketingNiche cacheNiche) {
        if ("0".equals(cacheNiche.getImportance())) {
            //询价定性标签 为A+ 商机时，若该商机状态 变更为 丢单、放弃 状态时， 需要推送钉钉消息，并触发审批流程
            //推送钉钉
            SysUser sysUser = new SysUser();
            sysUser.setUserName(cacheNiche.getNicheOwner());
            List<SysUser> userListByCondition = remoteUserService.findUserListByCondition(sysUser);
            String ownerName = Optional.ofNullable(userListByCondition).filter(list -> !list.isEmpty()).map(list -> list.get(0).getNickName()).orElse("");
            String status = "放弃";
            if("4".equals(diMarketingNiche.getNicheStatus())){
                status = "丢单";
            }
            //推送钉钉消息
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule("商机");
            diMessageList.setTitle("商机ID：" + diMarketingNiche.getNicheNo());
            diMessageList.setSendingTime(new Date());
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent("归属销售："+ownerName+"的A+商机，已被“"+status+"”，请注意进行丢单分析");
            for (String no : importanceAEmployee.split(",")) {
                diMessageList.setSendingUser(no);
                iDiMessageListService.insertDiMessageList(diMessageList);
            }
        }
    }

    private Map<Long,String> createAgencyApproval(String taskId, DiMarketingNiche cacheNiche) {
        List<BpmTaskRespVO> tasklist = getTaskListByProcessInstanceId(taskId);
        Map<Long,String> approvalBys = Maps.newHashMap();
        if (Objects.nonNull(tasklist)) {
            List<DiAgencyApproval> saveList = new ArrayList<>();
            tasklist.forEach(bpmTaskRespVO -> {
                if (Objects.equals(bpmTaskRespVO.getStatus(), 1)) {
                    DiAgencyApproval agencyApproval = new DiAgencyApproval();
                    agencyApproval.setBusinessKey(cacheNiche.getNicheNo());
                    agencyApproval.setJumpKey(cacheNiche.getNicheNo());
                    agencyApproval.setApprovalType(ApprovalTypeEnum.NiCHE_LOST_AND_ABANDON_APPROVAL.getTypeDesc());
                    agencyApproval.setApprovalTypeCode(ApprovalTypeEnum.NiCHE_LOST_AND_ABANDON_APPROVAL.getTypeCode());
                    //发起人
                    agencyApproval.setLaunchBy("super_admin");
                    //责任人目前是对应的审批人
                    SysUser user = remoteUserService.querySysUser(bpmTaskRespVO.getAssigneeUser().getId());
                    approvalBys.put(user.getUserId(), user.getNickName());
                    agencyApproval.setLiabilityBy(user.getUserName());
                    agencyApproval.setLiabilityRoleId(bpmTaskRespVO.getAssigneeUser().getDeptId() + "");
                    agencyApproval.setCreateBy("super_admin");
                    agencyApproval.setUpdateBy("super_admin");
                    agencyApproval.setCamundaProcessInstanceId(taskId);
                    agencyApproval.setCamundaTaskId("0".equals(cacheNiche.getImportance()) ?
                            importanceProcessDefinitionId : noImportanceProcessDefinitionId);
                    saveList.add(agencyApproval);
                    //发送钉钉通知
                    sendDingTalkNotice(cacheNiche.getNicheNo(), user.getUserName(), "super_admin", agencyApproval);
                }
            });
            diAgencyApprovalService.saveBatch(saveList);
        }
        return approvalBys;
    }

    private List<BpmTaskRespVO> getTaskListByProcessInstanceId(String taskId) {
        R<List<BpmTaskRespVO>> r = remoteYuDaoService.getTaskListByProcessInstanceId(taskId);
        if (Objects.nonNull(r) && r.getCode() == 0) {
            return r.getData();
        }
        return null;
    }

    public void createApproval(DiMarketingNiche reqVO) {
        DiMarketingNiche dbDiMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(reqVO.getId());
        // 触发审批流程
        String taskId = createApproval(importanceProcessDefinitionId, dbDiMarketingNiche.getImportance());
        if (StringUtils.isNotBlank(taskId)) {
            dbDiMarketingNiche.setProcessInstanceId(taskId);
            dbDiMarketingNiche.setOldNicheStatus(reqVO.getNicheStatus());
            dbDiMarketingNiche.setApprovalStatus("1");
            dbDiMarketingNiche.setInvalidReason(reqVO.getInvalidReason());
            //发送待办
            Map<Long, String> approvalBys = createAgencyApproval(taskId, dbDiMarketingNiche);
            if (!approvalBys.isEmpty()) {
                dbDiMarketingNiche.setApprovalBys(Joiner.on(",").join(approvalBys.values()));
                dbDiMarketingNiche.setApprovalUserIdBys((Joiner.on(",").join(approvalBys.keySet())));
            } else {
                dbDiMarketingNiche.setApprovalBys("");
                dbDiMarketingNiche.setApprovalUserIdBys("");
            }
        } else {
            throw new ServiceException("商机编辑失败，请重试");
        }
        diMarketingNicheMapper.updateDiMarketingNiche(dbDiMarketingNiche);
    }


    // 触发审批流程
    private String createApproval(String processDefinitionId, String level) {
        Map<String, Object> params = new HashMap<>();
        params.put("level", level);
        R<String> approval = remoteYuDaoService.createApproval
                (BpmProcessInstanceCreateReqVO.builder().processDefinitionId(processDefinitionId)
                                .variables(params)
                                .build(), SecurityUtils.getUserId(), SecurityUtils.getUsername());
        if (Objects.nonNull(approval) && approval.getCode() == 0) {
            return approval.getData();
        }
        return null;
    }

    /**
     * 发送审批钉钉消息
     *
     * @param sendUser     发送人
     * @param launchByName 发起人姓名
     */
    private void sendDingTalkNotice(String materielNo, String sendUser, String launchByName, DiAgencyApproval agencyApproval) {
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule(ApprovalNoticeEnum.NICHE_LOST_APPROVAL_NOTICE.getModule());
        String title = StrUtil.format(ApprovalNoticeEnum.NICHE_LOST_APPROVAL_NOTICE.getTitle(), materielNo);
        diMessageList.setTitle(title);
        diMessageList.setSendingTime(new Date());
        diMessageList.setRemarks(diMessageList.getTitle());
        String content = StrUtil.format(ApprovalNoticeEnum.NICHE_LOST_APPROVAL_NOTICE.getMessage(), materielNo, launchByName);
        diMessageList.setContent(content);
        diMessageList.setSendingUser(sendUser);
        String jumpPathArg = null;
        try {
            jumpPathArg = "&businessKey=" + agencyApproval.getBusinessKey() +
                    "&jumpKey=" + agencyApproval.getJumpKey() +
                    "&approvalTypeCode=" + agencyApproval.getApprovalTypeCode() +
                    "&agencyType=2";
        } catch (Exception e) {
            log.error("MaterialCodeAgencyApprovalAddListener---sendDingTalkNotice()---构建跳转路径失败，失败原因{}", e.toString());
        }
        if (StringUtils.isNotBlank(jumpPathArg)) {
            diMessageList.setJumpPathArg(jumpPathArg);
        }
        iDiMessageListService.insertDiMessageList(diMessageList);
    }

    /**
     * 数据写库
     *
     * @param diMarketingNiche
     * @return
     */
    private JSONObject doManage(DiMarketingNiche diMarketingNiche) {
        JSONObject jsonResult = new JSONObject();

//        //获取修改前的商机数据
//        boolean isUpdateDemandType = false;
//        DiMarketingNicheDemand marketingNicheDemand = diMarketingNicheDemandService.getOne(new LambdaQueryWrapper<DiMarketingNicheDemand>().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo()));
//        if (null != marketingNicheDemand && null != diMarketingNiche.getDiMarketingNicheDemand() &&
//                marketingNicheDemand.getDemandType() != diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
//            isUpdateDemandType = true;
//        }

//        DiMarketingNiche cacheNiche = nicheLocal.get();
        DiMarketingNiche old = diMarketingNicheMapper.selectDiMarketingNicheById(diMarketingNiche.getId());
        if (StringUtils.isNotEmpty(old.getApprovalStatus()) && StringUtils.isNotEmpty(diMarketingNiche.getApprovalStatus()) &&
                Integer.parseInt(old.getApprovalStatus()) > Integer.parseInt(diMarketingNiche.getApprovalStatus())
        ) {
            diMarketingNiche.setApprovalStatus(null);
        }
        boolean saveNiche = diMarketingNicheService.saveOrUpdate(diMarketingNiche);

        jsonResult.put("cnt", saveNiche ? 1 : 0);

        //重置联系人
        this.resetContacts(diMarketingNiche);

        boolean editCommandFlag = saveNiche && Boolean.TRUE.equals(diMarketingNiche.getNeedTechSupport());

        //新增需求信息
        if (editCommandFlag) {
//            if (isUpdateDemandType) {
//                deleteAgencyTask(diMarketingNiche.getNicheNo(), null);
////                technicalSupportDingTalk(diMarketingNiche);
//            } else {
//                //由于下面是直接删除的，这里要先把销售商机需求意向表里的数据查询出来（这个一定在修改前面查询，不然数据对比永远是未修改）
//                List<DiMarketingNicheDemandIntention> nicheDemandIntentionList = diMarketingNicheDemandIntentionService.list(new LambdaQueryWrapper<DiMarketingNicheDemandIntention>()
//                        .eq(DiMarketingNicheDemandIntention::getNicheNo, diMarketingNiche.getNicheNo())
//                        .eq(DiMarketingNicheDemandIntention::getDelFlag, "0"));
//                //判断数据是否修改
//                if (CollectionUtil.isEmpty(nicheDemandIntentionList)) {
////                    technicalSupportDingTalk(diMarketingNiche);
//                } else {
//                    if (CollectionUtil.isEmpty(diMarketingNiche.getDiMarketingNicheDemandIntentionList())) {
//                        deleteAgencyTask(diMarketingNiche.getNicheNo(), null);
//                    } else {
//                        updateOperateAgencyTaskDingTalk(diMarketingNiche, nicheDemandIntentionList);
//                    }
//                }
//            }

            //变更数据
            editNicheDemand(diMarketingNiche);
        }

        return jsonResult;
    }


    private void setOldDemandIntention(DiMarketingNiche diMarketingNiche, List<DiMarketingNicheDemandIntention> nicheDemandIntentionList) {
        Map<String, String> dictMap = null;
        if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            dictMap = remoteDictDataService.queryAsMap("fuel_accessory_category");
        }
        if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            dictMap = remoteDictDataService.queryAsMap("flame_category");
        }
        if (CollectionUtil.isNotEmpty(dictMap)) {
            if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                Map<String, String> finalDictMap = dictMap;
                nicheDemandIntentionList.forEach(item -> {
                    if (finalDictMap.containsKey(item.getFuelAccessoryCategory())) {
                        item.setFuelAccessoryCategoryName(finalDictMap.get(item.getFuelAccessoryCategory()));
                    }
                });
            }
            if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                Map<String, String> finalDictMap = dictMap;
                nicheDemandIntentionList.forEach(item -> {
                    if (finalDictMap.containsKey(item.getFlameCategory())) {
                        item.setFlameCategoryName(finalDictMap.get(item.getFlameCategory()));
                    }
                });
            }
        }
    }

    private List<String> formSaveCodeList(DiMarketingNiche diMarketingNiche) {
        List<String> saveCodeList = new ArrayList<>();
        List<DiMarketingNicheDemandIntention> saveDtoList = diMarketingNiche.getDiMarketingNicheDemandIntentionList().stream().filter(item -> null == item.getId()).toList();
        if (CollectionUtil.isNotEmpty(saveDtoList)) {
            if (1 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                for (DiMarketingNicheDemandIntention intention : saveDtoList) {
                    if (CollectionUtil.isEmpty(intention.getIndustryInfo())) {
                        continue;
                    }
                    saveCodeList.add(intention.getIndustryInfo().get(intention.getIndustryInfo().size() - 1));
                }
            }
            if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                for (DiMarketingNicheDemandIntention intention : saveDtoList) {
                    if (StringUtils.isBlank(intention.getFuelAccessoryCategoryName())) {
                        continue;
                    }
                    saveCodeList.add(intention.getFuelAccessoryCategoryName());
                }
            }
            if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                for (DiMarketingNicheDemandIntention intention : saveDtoList) {
                    if (StringUtils.isBlank(intention.getFlameCategoryName())) {
                        continue;
                    }
                    saveCodeList.add(intention.getFlameCategoryName());
                }
            }
        }
        return saveCodeList;
    }

    private void updateFormInfo(List<DiMarketingNicheDemandIntention> updateDtoList, List<DiMarketingNicheDemandIntention> nicheDemandIntentionList,
                                List<String> unchangedCodeList, List<String> saveCodeList, List<String> deleteCodeList, int demandType) {
        Map<Long, DiMarketingNicheDemandIntention> updateMap = updateDtoList.stream().collect(Collectors.toMap(DiMarketingNicheDemandIntention::getId, item -> item));
        if (1 == demandType) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : nicheDemandIntentionList) {
                if (updateMap.containsKey(nicheDemandIntention.getId())) {
                    Set<String> codeList = new LinkedHashSet<>();
                    if (StringUtils.isNotBlank(nicheDemandIntention.getIndustry())) {
                        codeList.add(nicheDemandIntention.getIndustry());
                    }
                    if (StringUtils.isNotBlank(nicheDemandIntention.getApplication())) {
                        codeList.add(nicheDemandIntention.getApplication());
                    }
                    if (StringUtils.isNotBlank(nicheDemandIntention.getScene())) {
                        codeList.add(nicheDemandIntention.getScene());
                    }
                    if (CollectionUtil.isNotEmpty(codeList) && CollectionUtil.isNotEmpty(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo())) {
                        if (JSONUtil.toJsonStr(codeList).equals(JSONUtil.toJsonStr(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo()))) {
                            unchangedCodeList.add(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().get(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().size() - 1));
                        } else {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().get(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().size() - 1));
                            formDeleteCode(nicheDemandIntention, deleteCodeList);
                        }
                    } else {
                        if (CollectionUtil.isEmpty(codeList) && CollectionUtil.isNotEmpty(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo())) {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().get(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo().size() - 1));
                        }
                        if (CollectionUtil.isEmpty(updateMap.get(nicheDemandIntention.getId()).getIndustryInfo()) && CollectionUtil.isNotEmpty(codeList)) {
                            formDeleteCode(nicheDemandIntention, deleteCodeList);
                        }
                    }
                } else {
                    formDeleteCode(nicheDemandIntention, deleteCodeList);
                }
            }
        }
        if (2 == demandType) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : nicheDemandIntentionList) {
                if (updateMap.containsKey(nicheDemandIntention.getId())) {
                    if (StringUtils.isNotBlank(nicheDemandIntention.getFuelAccessoryCategoryName()) && StringUtils.isNotBlank(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName())) {
                        if (nicheDemandIntention.getFuelAccessoryCategoryName().equals(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName())) {
                            unchangedCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName());
                        } else {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName());
                            deleteCodeList.add(nicheDemandIntention.getFuelAccessoryCategoryName());
                        }
                    } else {
                        if (StringUtils.isBlank(nicheDemandIntention.getFuelAccessoryCategoryName()) && StringUtils.isNotBlank(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName())) {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName());
                        }
                        if (StringUtils.isBlank(updateMap.get(nicheDemandIntention.getId()).getFuelAccessoryCategoryName()) && StringUtils.isNotBlank(nicheDemandIntention.getFuelAccessoryCategoryName())) {
                            deleteCodeList.add(nicheDemandIntention.getFuelAccessoryCategoryName());
                        }
                    }
                } else {
                    deleteCodeList.add(nicheDemandIntention.getFuelAccessoryCategoryName());
                }
            }
        }
        if (3 == demandType) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : nicheDemandIntentionList) {
                if (updateMap.containsKey(nicheDemandIntention.getId())) {
                    if (StringUtils.isNotBlank(nicheDemandIntention.getFlameCategoryName()) && StringUtils.isNotBlank(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName())) {
                        if (nicheDemandIntention.getFlameCategoryName().equals(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName())) {
                            unchangedCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName());
                        } else {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName());
                            deleteCodeList.add(nicheDemandIntention.getFlameCategoryName());
                        }
                    } else {
                        if (StringUtils.isBlank(nicheDemandIntention.getFlameCategoryName()) && StringUtils.isNotBlank(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName())) {
                            saveCodeList.add(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName());
                        }
                        if (StringUtils.isBlank(updateMap.get(nicheDemandIntention.getId()).getFlameCategoryName()) && StringUtils.isNotBlank(nicheDemandIntention.getFlameCategoryName())) {
                            deleteCodeList.add(nicheDemandIntention.getFlameCategoryName());
                        }
                    }
                } else {
                    deleteCodeList.add(nicheDemandIntention.getFlameCategoryName());
                }
            }
        }
    }

    private void deleteAgencyTask(String nicheNo, List<String> userList) {
        //删除待办
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(nicheNo);
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
        agencyTaskInfoDto.setType("2");
        if (CollectionUtil.isNotEmpty(userList)) {
            agencyTaskInfoDto.setLiabilityByList(userList);
        }
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    private void formDeleteCode(DiMarketingNicheDemandIntention nicheDemandIntention, List<String> deleteCodeList) {
        if (StringUtils.isNotBlank(nicheDemandIntention.getScene())) {
            deleteCodeList.add(nicheDemandIntention.getScene());
            return;
        }
        if (StringUtils.isNotBlank(nicheDemandIntention.getApplication())) {
            deleteCodeList.add(nicheDemandIntention.getApplication());
            return;
        }
        if (StringUtils.isNotBlank(nicheDemandIntention.getIndustry())) {
            deleteCodeList.add(nicheDemandIntention.getIndustry());
        }
    }

    /**
     * 后置处理
     *
     * @param diMarketingNiche
     * @param jsonResult
     */
    private void post(DiMarketingNiche diMarketingNiche, JSONObject jsonResult) {
        DiMarketingNiche cacheNiche = nicheLocal.get();
        List<String> msgUserList = msgUserListLocal.get();

        //判断商机状态，如果是丢单和放弃修改待办任务(待办任务，勿删)[成单放在订单那里处理]
        if ("4".equals(diMarketingNiche.getNicheStatus()) || "5".equals(diMarketingNiche.getNicheStatus())) {

            lostAndAbandonPost(diMarketingNiche);
            //编辑商机发送钉钉消息
            updateSendDingTalkMessage(diMarketingNiche, cacheNiche);
        } else {
            //普通修改，增加待办任务
            saveAgencyTaskSendMessage(msgUserList, diMarketingNiche, 1);
            //编辑商机发送钉钉消息
            updateSendDingTalkMessage(diMarketingNiche, cacheNiche);
        }

        //销售发生变更同步项目参与人
        synchroSale(diMarketingNiche, "oldNicheOwner", "");

        //
        syncProjectCustomerNo(diMarketingNiche, cacheNiche);

        //判断是否修改了商机关联客户

        removeLocal();
    }

    private void lostAndAbandonPost(DiMarketingNiche diMarketingNiche) {
        updateAgencyTaskSendMessage(diMarketingNiche.getNicheNo());
        deleteAgencyTask(diMarketingNiche.getNicheNo(), null);

        //销售报价单、产品方案、合同状态修改
        iDiContractService.discardProcess(diMarketingNiche.getNicheNo());

        //去除卡片视角
        diProcessProjectService.updateCardFlagProcessProject(diMarketingNiche.getNicheNo());

        //判断是否开启可行性验证审批，如果开启，则将审批流取消并删除待办审批
        if (NicheApprovalConstants.STR_SIX.equals(diMarketingNiche.getNicheStatus())
                && NicheApprovalConstants.STR_TWO.equals(diMarketingNiche.getFeasibilityVerifyState())
                && StringUtils.isNotBlank(diMarketingNiche.getFeasibilityVerifyProcessId())) {
            closeFeasibilityVerify(diMarketingNiche.getId(), diMarketingNiche.getNicheNo(), diMarketingNiche.getFeasibilityVerifyProcessId());
        } else {
            log.info("DiMarketingNicheServiceImpl---updateDiMarketingNiche()---商机不满足关闭可行性验证流程条件，商机号：{}", diMarketingNiche.getNicheNo());
        }

        //删除商机管理_报价阶段代办
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setType("2");
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE_QUOTE);
        agencyTaskInfoDto.setBusinessKey(diMarketingNiche.getNicheNo());
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }


    private void resetContacts(DiMarketingNiche diMarketingNiche) {
        int del = diMarketingContactsService.deleteDiMarketingContacts(diMarketingNiche.getNicheNo());

        List<DiMarketingContacts> contactsList = diMarketingNiche.getDiMarketingContactsList();

        ListUtils.emptyIfNull(contactsList).stream().filter(Objects::nonNull)
                .forEach(item -> diMarketingContactsService.insertDiMarketingContacts(item));
    }

    private void synchroSale(DiMarketingNiche diMarketingNiche, String oldNicheOwner, String oldSharedBy) {
        if (null == diMarketingNiche || StringUtils.isBlank(diMarketingNiche.getNicheNo())) {
            return;
        }
        //根据商机号获取项目编号
        DiProjectRelation relation = diProjectRelationService.getOne(new LambdaQueryWrapper<DiProjectRelation>().eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS).eq(DiProjectRelation::getRelationNo, diMarketingNiche.getNicheNo()));
        if (null == relation) {
            log.info("DiMarketingNicheServiceImpl---synchroSale()---没有获取到商机关联的项目数据,商机号：{}", diMarketingNiche.getNicheNo());
            return;
        }
        //需要删除的销售
        List<String> oldPersonnelList = new ArrayList<>();
        //需要新增的销售
        List<String> newPersonnelList = new ArrayList<>();
        //判断原本是否有销售
        if (StringUtils.isNotBlank(oldNicheOwner)) {
            //将原本的销售删除
            oldPersonnelList.add(oldNicheOwner);
        }
        if (StringUtils.isNotBlank(oldSharedBy)) {
            String[] oldSharedByArr = oldSharedBy.split(",");
            oldPersonnelList.addAll(Arrays.asList(oldSharedByArr));
        }
        if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
            //将本次修改的销售维护到项目参与人中
            newPersonnelList.add(diMarketingNiche.getNicheOwner());
        }
        DiProcessProject diProcessProject = diProcessProjectService.lambdaQuery().eq(DiProcessProject::getProjectNo, relation.getProjectNo()).one();
        if (Objects.nonNull(diProcessProject)) {
            diProcessProject.setSalesDeptId(NumberUtils.toLong(diMarketingNiche.getNicheOwnerDept()));
            diProcessProject.setSaleUserId(diMarketingNiche.getNicheOwner());
            diProcessProjectService.updateDiProcessProject(diProcessProject);
        }
        if (!CollectionUtils.isEmpty(diMarketingNiche.getDiMarketingContactsList())) {
            List<String> contactsOwnerList = diMarketingNiche.getDiMarketingContactsList().stream().map(DiMarketingContacts::getContactsOwner).filter(StringUtils::isNotBlank).toList();
            newPersonnelList.addAll(contactsOwnerList);
        }
        if (!CollectionUtils.isEmpty(oldPersonnelList)) {
            commonService.personnelSaveProject(relation.getProjectNo(), oldPersonnelList, "2");
        }
        if (!CollectionUtils.isEmpty(newPersonnelList)) {
            commonService.personnelSaveProject(relation.getProjectNo(), newPersonnelList, "0");
        }
    }

    private void syncProjectCustomerNo(DiMarketingNiche diMarketingNiche, DiMarketingNiche niche) {
//        if (null == diMarketingNiche || StringUtils.isBlank(diMarketingNiche.getNicheStatus())) {
//            return;
//        }
//        //修改状态不处理
//        if (!diMarketingNiche.getNicheStatus().equals(niche.getNicheStatus())) {
//            return;
//        }
        DiProjectRelation relation = diProjectRelationService.getOne(new LambdaQueryWrapper<DiProjectRelation>().eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS).eq(DiProjectRelation::getRelationNo, diMarketingNiche.getNicheNo()));
        //无归属项目
        if (Objects.isNull(relation)) {
            return;
        }
        diProcessProjectService.updateProjectCustomerNo(relation.getProjectNo(), diMarketingNiche.getCustomerNo());
        //判断是否修改了商机关联客户
        boolean isUpdateCustomerNo = false;
        if (StringUtils.isBlank(niche.getCustomerNo())) {
            if (StringUtils.isNotBlank(diMarketingNiche.getCustomerNo())) {
                isUpdateCustomerNo = true;
            }
        } else {
            if (!niche.getCustomerNo().equals(diMarketingNiche.getCustomerNo())) {
                isUpdateCustomerNo = true;
            }
        }
        if (isUpdateCustomerNo) {
            //更新di_project_relation表中数据
            DiProjectRelation customerNoRelation = diProjectRelationService.getOne(new LambdaQueryWrapper<DiProjectRelation>().eq(DiProjectRelation::getRelationType, RelationTypeEnum.CUSTOMER)
                    .eq(DiProjectRelation::getProjectNo, relation.getProjectNo()));
            if (Objects.isNull(customerNoRelation)) {
                log.info("DiMarketingNicheEditManage---syncProjectCustomerNo()---根据项目编号没有获取到项目关联客户数据,项目编号：{}", relation.getProjectNo());
                return;
            }
            customerNoRelation.setRelationNo(diMarketingNiche.getCustomerNo());
            diProjectRelationService.updateById(customerNoRelation);
        }
    }

    private void editNicheDemand(DiMarketingNiche diMarketingNiche) {
        /*DiMarketingNicheDemand diMarketingNicheDemand = diMarketingNicheDemandService.getOne(Wrappers.<DiMarketingNicheDemand>lambdaQuery().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNiche.getDiMarketingNicheDemand().setId(diMarketingNicheDemand.getId());
        diMarketingNiche.getDiMarketingNicheDemand().setNicheNo(diMarketingNicheDemand.getNicheNo());
        diMarketingNiche.getDiMarketingNicheDemand().setCreateBy(diMarketingNicheDemand.getCreateBy());
        diMarketingNiche.getDiMarketingNicheDemand().setCreateTime(diMarketingNicheDemand.getCreateTime());
        diMarketingNiche.getDiMarketingNicheDemand().setUpdateBy(SecurityUtils.getUsername());
        diMarketingNiche.getDiMarketingNicheDemand().setUpdateTime(DateUtils.getNowDate());
        diMarketingNicheDemandService.updateById(diMarketingNiche.getDiMarketingNicheDemand());*/

        diMarketingNicheDemandService.remove(Wrappers.<DiMarketingNicheDemand>lambdaQuery().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheDemandFileService.remove(Wrappers.<DiMarketingNicheDemandFile>lambdaQuery().eq(DiMarketingNicheDemandFile::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheDemandIntentionService.remove(Wrappers.<DiMarketingNicheDemandIntention>lambdaQuery().eq(DiMarketingNicheDemandIntention::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheRequirementAccessoryService.remove(Wrappers.<DiMarketingNicheRequirementAccessory>lambdaQuery().eq(DiMarketingNicheRequirementAccessory::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheFlameAccessoryService.remove(Wrappers.<DiMarketingNicheFlameAccessory>lambdaQuery().eq(DiMarketingNicheFlameAccessory::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheRequirementCombustorService.remove(Wrappers.<DiMarketingNicheRequirementCombustor>lambdaQuery().eq(DiMarketingNicheRequirementCombustor::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheRequirementElectrollService.remove(Wrappers.<DiMarketingNicheRequirementElectroll>lambdaQuery().eq(DiMarketingNicheRequirementElectroll::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheRequirementHotstoveService.remove(Wrappers.<DiMarketingNicheRequirementHotstove>lambdaQuery().eq(DiMarketingNicheRequirementHotstove::getNicheNo, diMarketingNiche.getNicheNo()));

        diMarketingNicheRequirementValveService.remove(Wrappers.<DiMarketingNicheRequirementValve>lambdaQuery().eq(DiMarketingNicheRequirementValve::getNicheNo, diMarketingNiche.getNicheNo()));

        addNicheDemand(diMarketingNiche);
    }

    private void addNicheDemand(DiMarketingNiche diMarketingNiche) {
        String nicheNo = diMarketingNiche.getNicheNo();

        DiMarketingNicheDemand nicheDemand = Optional.ofNullable(diMarketingNiche.getDiMarketingNicheDemand()).orElse(new DiMarketingNicheDemand());
        nicheDemand.setId(null);
        nicheDemand.setNicheNo(nicheNo);
        diMarketingNicheDemandService.save(nicheDemand);

        Long demandId = nicheDemand.getId();

        List<DiMarketingNicheDemandFile> diMarketingNicheDemandFileList = diMarketingNiche.getDiMarketingNicheDemandFileList();
        ListUtils.emptyIfNull(diMarketingNicheDemandFileList).stream().filter(Objects::nonNull)
                .forEach(item -> {
                    item.setId(null);
                    item.setDemandId(demandId);
                    item.setNicheNo(nicheNo);
                    item.setCreateBy(SecurityUtils.getUsername());
                    item.setCreateTime(DateUtils.getNowDate());
                });
        if (!CollectionUtils.isEmpty(diMarketingNicheDemandFileList)) {
            diMarketingNicheDemandFileService.saveBatch(diMarketingNicheDemandFileList);
        }

        List<DiMarketingNicheDemandIntention> diMarketingNicheDemandIntentionList = diMarketingNiche.getDiMarketingNicheDemandIntentionList();
        List<DiMarketingNicheDemandIntention> filterIntentionList = ListUtils.emptyIfNull(diMarketingNicheDemandIntentionList).stream().filter(
                item -> StringUtils.isNoneEmpty(item.getFuelType())
                        || (Objects.nonNull(item.getRequiredNumber()) && item.getRequiredNumber() > 0)
                        || StringUtils.isNoneEmpty(item.getIndustry())
                        || StringUtils.isNoneEmpty(item.getMachinePower())
                        || StringUtils.isNoneEmpty(item.getRemark())
        ).toList();

        filterIntentionList.stream().filter(Objects::nonNull)
                .forEach(item -> {
                    item.setId(null);
                    item.setDemandId(demandId);
                    item.setNicheNo(nicheNo);
                    item.setCreateBy(SecurityUtils.getUsername());
                    item.setCreateTime(DateUtils.getNowDate());
                });
        if (!CollectionUtils.isEmpty(filterIntentionList)) {
            diMarketingNicheDemandIntentionService.saveBatch(filterIntentionList);
        }

        List<DiMarketingNicheFlameAccessory> diMarketingNicheFlameAccessoryList = diMarketingNiche.getDiMarketingNicheFlameAccessoryList();
        List<DiMarketingNicheFlameAccessory> filterFlameAccessoryList = ListUtils.emptyIfNull(diMarketingNicheFlameAccessoryList).stream()
                .filter(item -> StringUtils.isNoneEmpty(item.getAccessoryType())
                        || StringUtils.isNoneEmpty(item.getRemark()))
                .toList();

        filterFlameAccessoryList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterFlameAccessoryList)) {
            if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
                diMarketingNicheFlameAccessoryService.saveBatch(filterFlameAccessoryList);
            }
        }

        List<DiMarketingNicheRequirementAccessory> diMarketingNicheRequirementAccessoryList = diMarketingNiche.getDiMarketingNicheRequirementAccessoryList();
        List<DiMarketingNicheRequirementAccessory> filterAccessoryList = ListUtils.emptyIfNull(diMarketingNicheRequirementAccessoryList).stream()
                .filter(item -> StringUtils.isNoneEmpty(item.getAccessoryType())
                        || StringUtils.isNoneEmpty(item.getRemark()))
                .toList();

        filterAccessoryList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterAccessoryList)) {
            diMarketingNicheRequirementAccessoryService.saveBatch(filterAccessoryList);
        }

        List<DiMarketingNicheRequirementCombustor> diMarketingNicheRequirementCombustorList = diMarketingNiche.getDiMarketingNicheRequirementCombustorList();
        List<DiMarketingNicheRequirementCombustor> filterCombustorList = ListUtils.emptyIfNull(diMarketingNicheRequirementCombustorList).stream()
                .filter(item ->
                        StringUtils.isNoneEmpty(item.getBrand())
                                || (Objects.nonNull(item.getNumber()) && item.getNumber() > 0)
                                || StringUtils.isNoneEmpty(item.getTurndown())
                                || StringUtils.isNoneEmpty(item.getProportionType())
                                || StringUtils.isNoneEmpty(item.getNoxEmission())
                                || StringUtils.isNoneEmpty(item.getFuelHeat())
                                || StringUtils.isNoneEmpty(item.getFuelSupplyPressure())
                                || StringUtils.isNoneEmpty(item.getRemark())
                ).toList();

        filterCombustorList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterCombustorList)) {
            diMarketingNicheRequirementCombustorService.saveBatch(filterCombustorList);
        }

        List<DiMarketingNicheRequirementElectroll> diMarketingNicheRequirementElectrollList = diMarketingNiche.getDiMarketingNicheRequirementElectrollList();
        List<DiMarketingNicheRequirementElectroll> filterElectrollList = ListUtils.emptyIfNull(diMarketingNicheRequirementElectrollList).stream().filter(
                item -> StringUtils.isNoneEmpty(item.getCabinetType())
                        || StringUtils.isNoneEmpty(item.getElectricControll())
                        || StringUtils.isNoneEmpty(item.getElectricCommunication())
                        || StringUtils.isNoneEmpty(item.getTemperatureControll())
                        || StringUtils.isNoneEmpty(item.getTemperatureSensor())
        ).toList();
        filterElectrollList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterElectrollList)) {
            diMarketingNicheRequirementElectrollService.saveBatch(filterElectrollList);
        }

        List<DiMarketingNicheRequirementHotstove> diMarketingNicheRequirementHotstoveList = diMarketingNiche.getDiMarketingNicheRequirementHotstoveList();
        List<DiMarketingNicheRequirementHotstove> filterHotstoveList = ListUtils.emptyIfNull(diMarketingNicheRequirementHotstoveList).stream().filter(
                item -> StringUtils.isNoneEmpty(item.getHotstoveType())
                        || StringUtils.isNoneEmpty(item.getHotstoveShape())
                        || StringUtils.isNoneEmpty(item.getHotstoveForm())
                        || StringUtils.isNoneEmpty(item.getAirSupply())
                        || StringUtils.isNoneEmpty(item.getInsulationThickness())
        ).toList();
        filterHotstoveList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterHotstoveList)) {
            diMarketingNicheRequirementHotstoveService.saveBatch(filterHotstoveList);
        }

        List<DiMarketingNicheRequirementValve> diMarketingNicheRequirementValveList = diMarketingNiche.getDiMarketingNicheRequirementValveList();
        List<DiMarketingNicheRequirementValve> filterValveList = ListUtils.emptyIfNull(diMarketingNicheRequirementValveList).stream().filter(
                item -> StringUtils.isNoneEmpty(item.getDistributeType())
                        || StringUtils.isNoneEmpty(item.getWiringType())
                        || StringUtils.isNoneEmpty(item.getFireCheck())
                        || StringUtils.isNoneEmpty(item.getCombustorSupport())
        ).toList();
        filterValveList.forEach(item -> {
            item.setId(null);
            item.setDemandId(demandId);
            item.setNicheNo(nicheNo);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
        });
        if (!CollectionUtils.isEmpty(filterValveList)) {
            diMarketingNicheRequirementValveService.saveBatch(filterValveList);
        }
    }



    private void structureCode(DiMarketingNiche diMarketingNiche, List<String> codeList) {
        if (1 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : diMarketingNiche.getDiMarketingNicheDemandIntentionList()) {
                if (CollectionUtil.isEmpty(nicheDemandIntention.getIndustryInfo())) {
                    continue;
                }
                codeList.add(nicheDemandIntention.getIndustryInfo().get(nicheDemandIntention.getIndustryInfo().size() - 1));
            }
        }
        if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : diMarketingNiche.getDiMarketingNicheDemandIntentionList()) {
                if (StringUtils.isBlank(nicheDemandIntention.getFuelAccessoryCategoryName())) {
                    continue;
                }
                codeList.add(nicheDemandIntention.getFuelAccessoryCategoryName());
            }
        }
        if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            for (DiMarketingNicheDemandIntention nicheDemandIntention : diMarketingNiche.getDiMarketingNicheDemandIntentionList()) {
                if (StringUtils.isBlank(nicheDemandIntention.getFlameCategoryName())) {
                    continue;
                }
                codeList.add(nicheDemandIntention.getFlameCategoryName());
            }
        }
    }


    public List<String> getUserJob(List<String> codeList, DiMarketingNiche diMarketingNiche) {
        Map<String, String> dictMap = getOwnerInfo(diMarketingNiche);
        if (CollectionUtil.isEmpty(dictMap)) {
            log.info("DiMarketingNicheEditManage---getUserJob---未获取到对应负责人");
            return null;
        }
        List<String> sendUserList = new ArrayList<>();
        codeList.forEach(code -> {
            if (StringUtils.isNotBlank(dictMap.get(code))) {
                sendUserList.addAll(Arrays.stream(dictMap.get(code).split(",")).toList());
            }
        });
        if (CollectionUtil.isEmpty(sendUserList)) {
            log.info("DiMarketingNicheEditManage---getUserJob---发送人为空");
            return null;
        }
        return sendUserList.stream().distinct().toList();
    }

    private Map<String, String> getOwnerInfo(DiMarketingNiche diMarketingNiche) {
        if (1 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("industry_owner");
        }
        if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("fuel_accessory_owner");
        }
        if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("flame_owner");
        }
        return null;
    }

    /**
     * 发送修改待办任务消息
     *
     * @param nicheNo 商机编号
     */
    private void updateAgencyTaskSendMessage(String nicheNo) {
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(nicheNo);
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE);
        agencyTaskInfoDto.setType("2");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 修改线索发送钉钉消息
     *
     * @param diMarketingNiche 入参商机
     * @param oldNiche         数据库中商机
     */
    private void updateSendDingTalkMessage(DiMarketingNiche diMarketingNiche, DiMarketingNiche oldNiche) {
        if (null == diMarketingNiche || StringUtils.isBlank(diMarketingNiche.getNicheStatus())) {
            return;
        }
        //发送人
        List<String> sendByList = new ArrayList<>();
        //商机创建人默认发送
        sendByList.add(oldNiche.getCreateBy());
        List<DiMessageList> messageList = new ArrayList<>();
        //由于修改数据和修改状态是一个接口，修改状态时只会传状态数据，这里先判断是不是修改状态
        if (!diMarketingNiche.getNicheStatus().equals(oldNiche.getNicheStatus())) {
            //判断是否有共享销售
            if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                String[] sharedBys = oldNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
            }
            //丢单发送钉钉消息
            if ("4".equals(diMarketingNiche.getNicheStatus())) {
                String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_DISCARD.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            }
            //放弃发送钉钉消息
            if ("5".equals(diMarketingNiche.getNicheStatus())) {
                String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_ABANDON.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            }
            if (StringUtils.isNotBlank(oldNiche.getNicheOwner())) {
                sendByList.add(oldNiche.getNicheOwner());
            }
            //判断是否有共享销售
            if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                String[] sharedBys = oldNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                sendByList.addAll(sharedByList);
            }
        } else {
            //判断主要销售
            if (!diMarketingNiche.getNicheOwner().equals(oldNiche.getNicheOwner())) {
                List<String> nicheOwnerList = new ArrayList<>();
                nicheOwnerList.add(diMarketingNiche.getNicheOwner());
                nicheOwnerList.add(oldNiche.getNicheOwner());
                //获取主要销售中文名
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(nicheOwnerList).build());
                if (userListResult.isSuccess()) {
                    Map<String, String> userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                    //设置主要销售发送钉钉消息
                    String content = StrUtil.format(NicheDingTalkEnum.UPDATE_MAIN_SALE.getMessage(), userNameMap.get(oldNiche.getNicheOwner()), userNameMap.get(diMarketingNiche.getNicheOwner()), SecurityUtils.getLoginUser().getSysUser().getNickName());
                    DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                    messageList.add(message);
                } else {
                    log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---变更---获取主要销售名称失败，主要销售工号：{}，返回：{}", JSONUtil.toJsonStr(nicheOwnerList), JSONUtil.toJsonStr(userListResult));
                }
            }
            //判断关联客户
            if (StringUtils.isBlank(diMarketingNiche.getCustomerNo())) {
                //判断原来是否绑定客户
                if (StringUtils.isNotBlank(oldNiche.getCustomerNo())) {
                    //获取客户信息
                    DiMarketingCustomer diMarketingCustomer = diMarketingCustomerService.getOne(new LambdaQueryWrapper<DiMarketingCustomer>().eq(DiMarketingCustomer::getCustomerNo, oldNiche.getCustomerNo()));
                    if (null != diMarketingCustomer) {
                        //将关联客户删除
                        String content = StrUtil.format(NicheDingTalkEnum.DELETE_RELATION_COMPANY.getMessage(), diMarketingCustomer.getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(oldNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---删除---根据客户号未获取到客户信息，客户号：{}", oldNiche.getNicheNo());
                    }
                }
            } else {
                //判断原来是否绑定客户
                if (StringUtils.isBlank(oldNiche.getCustomerNo())) {
                    //新增关联客户
                    String content = StrUtil.format(NicheDingTalkEnum.SAVE_RELATION_COMPANY.getMessage(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                    DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                    messageList.add(message);
                } else {
                    if (!oldNiche.getCustomerNo().equals(diMarketingNiche.getDiMarketingCustomer().getCustomerNo())) {
                        //获取客户信息
                        DiMarketingCustomer diMarketingCustomer = diMarketingCustomerService.getOne(new LambdaQueryWrapper<DiMarketingCustomer>().eq(DiMarketingCustomer::getCustomerNo, oldNiche.getCustomerNo()));
                        if (null != diMarketingCustomer) {
                            //修改关联客户
                            String content = StrUtil.format(NicheDingTalkEnum.UPDATE_RELATION_COMPANY.getMessage(), diMarketingCustomer.getCompanyName(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                            DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                            messageList.add(message);
                        } else {
                            log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---修改---根据客户号未获取到客户信息，客户号：{}", oldNiche.getCustomerNo());
                        }
                    }
                }
            }
            //判断共享销售
            if (StringUtils.isBlank(diMarketingNiche.getSharedBy())) {
                //判断原本是否绑定共享销售
                if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                    //共享销售被删除
                    String[] sharedBys = oldNiche.getSharedBy().split(",");
                    List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                    //获取共享销售中文名
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                    if (userListResult.isSuccess()) {
                        //将共享销售中文名转为字符串
                        String sharedByName = userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(","));
                        String content = StrUtil.format(NicheDingTalkEnum.DELETE_SHARE_SALE.getMessage(), sharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---删除---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                    }
                }
            } else {
                if (StringUtils.isBlank(oldNiche.getSharedBy())) {
                    //共享销售新增
                    String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
                    List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                    //获取共享销售中文名
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                    if (userListResult.isSuccess()) {
                        //将共享销售中文名转为字符串
                        String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(",")), SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---新增---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                    }
                } else {
                    //都不等于空，判断是否修改
                    if (!oldNiche.getSharedBy().equals(diMarketingNiche.getSharedBy())) {
                        //转为集合
                        List<String> oldSharedBys = Arrays.stream(oldNiche.getSharedBy().split(",")).distinct().toList();
                        List<String> newSharedBys = Arrays.stream(diMarketingNiche.getSharedBy().split(",")).distinct().toList();
                        //获取新增和删除的共享销售
                        List<String> deleteSharedBys = oldSharedBys.stream().filter(sharedBy -> !newSharedBys.contains(sharedBy)).toList();
                        List<String> saveSharedBys = newSharedBys.stream().filter(sharedBy -> !oldSharedBys.contains(sharedBy)).toList();
                        //获取共享销售中文名
                        List<String> sharedByList = new ArrayList<>();
                        sharedByList.addAll(saveSharedBys);
                        sharedByList.addAll(deleteSharedBys);
                        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                        if (userListResult.isSuccess()) {
                            //获取新增共享销售中文名
                            if (!CollectionUtils.isEmpty(saveSharedBys)) {
                                String saveSharedByName = userListResult.getData().stream().filter(ddUserDTO -> saveSharedBys.contains(ddUserDTO.getJobNumber())).map(DdUserDTO::getName).collect(Collectors.joining(","));
                                String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), saveSharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                                messageList.add(message);
                            }
                            //获取删除共享销售中文名
                            if (!CollectionUtils.isEmpty(deleteSharedBys)) {
                                String deleteSharedByName = userListResult.getData().stream().filter(ddUserDTO -> deleteSharedBys.contains(ddUserDTO.getJobNumber())).map(DdUserDTO::getName).collect(Collectors.joining(","));
                                String content = StrUtil.format(NicheDingTalkEnum.DELETE_SHARE_SALE.getMessage(), deleteSharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                                messageList.add(message);
                            }
                        } else {
                            log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---修改---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
                sendByList.add(diMarketingNiche.getNicheOwner());
            }
            //判断是否有共享销售
            if (StringUtils.isNotBlank(diMarketingNiche.getSharedBy())) {
                String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                sendByList.addAll(sharedByList);
            }
        }
        //判断是否有可以发送的消息
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        //发送钉钉消息
        List<String> sendBys = sendByList.stream().distinct().toList();
        for (String sendBy : sendBys) {
            for (DiMessageList message : messageList) {
                message.setSendingUser(sendBy);
                iDiMessageListService.insertDiMessageList(message);
            }
        }
    }

    /**
     * 关闭可行性验证审批
     *
     * @param nicheId                    商机ID
     * @param nicheNo                    商机号
     * @param feasibilityVerifyProcessId 可行性验证记录ID
     */
    private void closeFeasibilityVerify(String nicheId, String nicheNo, String feasibilityVerifyProcessId) {
        log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---丢单弃单_商机可行性验证取消流程开始---商机号：{}", nicheNo);
        //获取可行性验证流程记录
        DiFeasibilityVerifyProcess feasibilityVerifyProcess = diFeasibilityVerifyProcessService.getById(feasibilityVerifyProcessId);
        if (null == feasibilityVerifyProcess) {
            log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---可行性验证审批流程不存在，商机号：{}", nicheNo);
            return;
        }
        if (StringUtils.isBlank(feasibilityVerifyProcess.getBusinessApprovalProcessId()) || StringUtils.isBlank(feasibilityVerifyProcess.getTechniqueApprovalProcessId())) {
            log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---商机可行性验证信息不正确，商机号：{}", nicheNo);
            return;
        }
        //判断状态
        if (NicheApprovalConstants.STR_ZERO.equals(feasibilityVerifyProcess.getBusinessApprovalState())) {
            //关闭可行性验证-商务流程并删除待办审批
            diFeasibilityVerifyProcessService.closeProcessAndDelAgency(feasibilityVerifyProcess.getBusinessApprovalProcessId(), NicheApprovalConstants.BUSINESS_APPROVAL_ERROR_DISCARD_CANCEL, NicheApprovalConstants.FEASIBILITY_BUSINESS_APPROVAL_PROCESS, feasibilityVerifyProcess.getId());
        }
        if (NicheApprovalConstants.STR_ZERO.equals(feasibilityVerifyProcess.getTechniqueApprovalState())) {
            //关闭可行性验证-技术流程并删除待办审批
            diFeasibilityVerifyProcessService.closeProcessAndDelAgency(feasibilityVerifyProcess.getTechniqueApprovalProcessId(), NicheApprovalConstants.TECHNICAL_APPROVAL_ERROR_DISCARD_CANCEL, NicheApprovalConstants.FEASIBILITY_TECHNIQUE_APPROVAL_PROCESS, feasibilityVerifyProcess.getId());
        }
        //将商机可行性验证状态改为已取消
        LambdaUpdateChainWrapper<DiMarketingNiche> nicheUpdateWrapper = new LambdaUpdateChainWrapper<>(diMarketingNicheMapper);
        nicheUpdateWrapper.eq(DiMarketingNiche::getId, nicheId);
        nicheUpdateWrapper.set(DiMarketingNiche::getFeasibilityVerifyState, NicheApprovalConstants.STR_FIVE);
        nicheUpdateWrapper.update();
        log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---丢单弃单_商机可行性验证取消流程结束---商机号：{}", nicheNo);
    }

    /**
     * 发送新增待办任务消息
     *
     * @param byList           销售人员
     * @param diMarketingNiche 商机信息
     */
    private void saveAgencyTaskSendMessage(List<String> byList, DiMarketingNiche diMarketingNiche, Integer type) {
        if (CollectionUtils.isEmpty(byList)) {
            return;
        }
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(diMarketingNiche.getNicheNo());
        if (1 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE);
            if (null != diMarketingNiche.getDiMarketingCustomer() && StringUtils.isNotBlank(diMarketingNiche.getDiMarketingCustomer().getCompanyName())) {
                agencyTaskInfoDto.setTaskName(diMarketingNiche.getDiMarketingCustomer().getCompanyName());
            }
        }
        if (2 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
            agencyTaskInfoDto.setTaskName(diMarketingNiche.getProjectName());
        }
        //获取状态中文描述
        SysDictData dictData = new SysDictData();
        dictData.setDictType("niche_status");
        dictData.setDictValue(diMarketingNiche.getNicheStatus());
        String nicheStatusDesc = remoteDictDataService.selectDictLabel(dictData);
        agencyTaskInfoDto.setTaskStateDesc(nicheStatusDesc);
        agencyTaskInfoDto.setLiabilityByList(byList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 新增商机发送钉钉消息
     *
     * @param diMarketingNiche 商机实体
     */
    private void saveSendDingTalkMessage(DiMarketingNiche diMarketingNiche) {
        List<String> sendByList = new ArrayList<>();
        //商机创建人默认发送
        sendByList.add(diMarketingNiche.getCreateBy());
        List<DiMessageList> messageList = new ArrayList<>();
        //判断是否设置主要销售
        if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
            //获取主要销售中文名
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Collections.singletonList(diMarketingNiche.getNicheOwner())).build());
            if (userListResult.isSuccess()) {
                //设置主要销售发送钉钉消息
                String content = StrUtil.format(NicheDingTalkEnum.SET_MAIN_SALE.getMessage(), userListResult.getData().get(0).getName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---获取主要销售名称失败，主要销售工号：{}，返回：{}", diMarketingNiche.getNicheOwner(), JSONUtil.toJsonStr(userListResult));
            }
        }
        //判断是否关联了公司信息
        if (StringUtils.isNotBlank(diMarketingNiche.getCustomerNo())) {
            if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingCustomer().getCompanyName())) {
                String content = StrUtil.format(NicheDingTalkEnum.SAVE_RELATION_COMPANY.getMessage(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---公司名称为空，公司信息：{}", JSONUtil.toJsonStr(diMarketingNiche.getDiMarketingCustomer()));
            }
        }
        //判断是否设置了共享销售
        if (StringUtils.isNotBlank(diMarketingNiche.getSharedBy())) {
            String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
            List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
            //获取共享销售中文名
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
            if (userListResult.isSuccess()) {
                //将共享销售中文名转为字符串
                String sharedByName = userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(","));
                String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), sharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
            }
            sendByList.addAll(sharedByList);
        }
        //判断是否有可以发送的消息
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        //发送钉钉消息
        List<String> sendBys = sendByList.stream().distinct().toList();
        for (String sendBy : sendBys) {
            for (DiMessageList message : messageList) {
                message.setSendingUser(sendBy);
                iDiMessageListService.insertDiMessageList(message);
            }
        }
    }

    private String getCurDateFormatter(String formatter) {
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern(formatter);
        LocalDate currentDate = LocalDate.now();
        return currentDate.format(monthFormatter);
    }

    /**
     * 构建钉钉消息体
     *
     * @param nicheNo 商机编号
     * @param content 消息内容
     * @return 消息体
     */
    private DiMessageList formMessageInfo(String nicheNo, String content) {
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("商机");
        diMessageList.setTitle("商机ID：" + nicheNo);
        diMessageList.setSendingTime(new Date());
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        return diMessageList;
    }

    private void removeLocal() {
        nicheLocal.remove();
        msgUserListLocal.remove();
        contactListLocal.remove();
    }

    @Transactional
    public Map<String, Object> approval(String nicheId) {
        HashMap<String,Object> map = Maps.newHashMap();
        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(nicheId);
        BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
        reqVO.setId(diMarketingNiche.getProcessInstanceId());
        reqVO.setReason("通过");
        R<Boolean> booleanR = remoteYuDaoService.approveTask(reqVO, SecurityUtils.getUserId(), SecurityUtils.getUsername());
        if (Objects.nonNull(booleanR) && booleanR.getCode() == 0 && booleanR.getData()) {
            R<BpmApprovalDetailRespVO> approvalDetail = remoteYuDaoService.getApprovalDetail(
                    BpmApprovalDetailReqVO.builder()
                            .processInstanceId(diMarketingNiche.getProcessInstanceId())
                            .processVariables(reqVO.getVariables())
                            .build(), SecurityUtils.getUserId(), SecurityUtils.getUsername());
            if (Objects.nonNull(approvalDetail) && approvalDetail.getCode() == 0) {
                BpmApprovalDetailRespVO data = approvalDetail.getData();
                Integer status = data.getStatus();
                if (!Objects.equals(status, 2)) {
                    diAgencyApprovalService.deleteAgencyApproval(diMarketingNiche, "1");
                    Map<Long, String> approvalBys = createAgencyApproval(diMarketingNiche.getProcessInstanceId(), diMarketingNiche);
                    DiMarketingNiche update = new DiMarketingNiche();
                    update.setId(diMarketingNiche.getId());
                    if (!approvalBys.isEmpty()) {
                        update.setApprovalBys(Joiner.on(",").join(approvalBys.values()));
                        update.setApprovalUserIdBys(Joiner.on(",").join(approvalBys.keySet()));
                    }else {
                        update.setApprovalBys("");
                        update.setApprovalUserIdBys("");
                    }
                    diMarketingNicheMapper.updateDiMarketingNiche(update);
                }else {
                    DiMarketingNiche update = new DiMarketingNiche();
                    update.setId(diMarketingNiche.getId());
                    update.setApprovalBys("");
                    update.setApprovalUserIdBys("");
                    update.setApprovalStatus("2");
                    diMarketingNicheMapper.updateDiMarketingNiche(update);
                    diAgencyApprovalService.deleteAgencyApproval(diMarketingNiche, "2");
                }
                map.put("status", status);
                map.put("nicheStatus", diMarketingNiche.getOldNicheStatus());
                map.put("invalidReason", diMarketingNiche.getInvalidReason());
                return map;
            }
            return null;
        }
        return null;
    }

    public boolean rejectTask(String nicheId) {
        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(nicheId);
        BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
        reqVO.setId(diMarketingNiche.getProcessInstanceId());
        reqVO.setReason("不通过");
        R<Boolean> booleanR = remoteYuDaoService.rejectTask(BpmTaskRejectReqVO.builder().id(reqVO.getId())
                        .reason(reqVO.getReason())
                        .variables(reqVO.getVariables())
                        .build(), SecurityUtils.getUserId(), SecurityUtils.getUsername());
        boolean b = Objects.nonNull(booleanR) && booleanR.getCode() == 0 && booleanR.getData();
        if (b) {
            DiMarketingNiche update = new DiMarketingNiche();
            update.setId(diMarketingNiche.getId());
            update.setApprovalStatus("3");
            update.setApprovalBys("");
            update.setApprovalUserIdBys("");
            diMarketingNicheMapper.updateDiMarketingNiche(update);
            diAgencyApprovalService.deleteAgencyApproval(diMarketingNiche, "3");
        }
        return b;
    }
}
