package com.dyd.di.matrix.vo;

import lombok.Data;

@Data
public class FileTypeVo {
    /**
     * 分类ID
     */
    private String id;

    /**
     * 文件key(删除文件用)
     */
    private String fileKey;

    /**
     * 所属业务：
     * 图纸资料 :0
     * 产品素材: 1
     * 外形尺寸: 2
     * 操作说明:3
     * 工作范围：4
     * 故障手册：5
     * 版图介绍：6
     * 业务照片：7
     * 烧嘴介绍：8
     */
    private String belonging;

    /**
     * 关联ID
     */
    private String belongingId;

    /**
     * 分类开关（0关闭 1打开）
     */
    private Integer typeSwitch;
}
