package com.dyd.di.agency.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.ApprovalNoticeEnum;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.order.domain.DiOrderDeliveryChange;
import com.dyd.di.order.mapper.DiOrderDeliveryChangeMapper;
import com.dyd.di.order.service.OrderCommonService;
import com.dyd.system.api.RemoteRoleService;
import com.dyd.system.api.domain.SysUser;
import com.dydtec.base.camunda.api.constant.CamundaConstant;
import com.dydtec.base.camunda.api.domain.dto.CreateAgencyApprovalDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@RocketMQMessageListener(
        consumerGroup = AgencyConstants.POD_CHANGE_ADD_APPROVAL_GROUP,
        topic = CamundaConstant.POD_CHANGE_ADD_AGENCY_NOTICE,
        selectorExpression = CamundaConstant.ADD_AGENCY_APPROVAL_TAG
)
@Slf4j
@ConditionalOnProperty(prefix = "mq.consumer", name = "enable", havingValue = "true")
public class PODChangeAgencyApprovalAddListener implements RocketMQListener<String> {

    @Resource
    private DiOrderDeliveryChangeMapper diOrderDeliveryChangeMapper;

    @Resource
    private DiAgencyApprovalService diAgencyApprovalService;

    @Resource
    private RemoteRoleService remoteRoleService;

    @Resource
    private IDiMessageListService iDiMessageListService;

    @Autowired
    private OrderCommonService orderCommonService;

    @Override
    public void onMessage(String message) {
        if (StringUtils.isBlank(message)) {
            log.info("订单-项目交付日期变更待办审批新增监听器---消费者接受到的消息为空");
            return;
        }
        log.info("订单-项目交付日期变更待办审批新增监听器---消费者接受到消息：" + message);
        CreateAgencyApprovalDTO agencyApprovalDto = JSONUtil.toBean(message, CreateAgencyApprovalDTO.class);
        DiOrderDeliveryChange orderDeliveryChange = diOrderDeliveryChangeMapper.selectById(agencyApprovalDto.getBusinessKey());
        if (null == orderDeliveryChange) {
            log.info("订单-项目交付日期变更待办审批新增监听器---订单项目交付日期变更记录不存在，主键ID：{}", agencyApprovalDto.getBusinessKey());
            return;
        }
//        if (CollectionUtil.isEmpty(orderDeliveryChange.getPreSaleIdList())) {
//            log.info("订单-项目交付日期变更待办审批新增监听器---订单项目交付日期变更记录不存在，主键ID：{}", agencyApprovalDto.getBusinessKey());
//            return;
//        }
        LambdaUpdateChainWrapper<DiOrderDeliveryChange> updateWrapper = new LambdaUpdateChainWrapper<>(diOrderDeliveryChangeMapper);
        updateWrapper.eq(DiOrderDeliveryChange::getId, orderDeliveryChange.getId());
        updateWrapper.set(DiOrderDeliveryChange::getCamundaTaskId, agencyApprovalDto.getTaskId());
        updateWrapper.update();
        //增加防止重复消费的逻辑
        List<DiAgencyApproval> approvalList = diAgencyApprovalService.list(new LambdaQueryWrapper<DiAgencyApproval>()
                .eq(DiAgencyApproval::getBusinessKey, orderDeliveryChange.getOrderNo())
                .eq(DiAgencyApproval::getCamundaTaskId, agencyApprovalDto.getTaskId())
                .eq(DiAgencyApproval::getCamundaProcessInstanceId, agencyApprovalDto.getProcessInstanceId()));
        if (CollectionUtil.isNotEmpty(approvalList)) {
            log.info("订单-项目交付日期变更待办审批新增监听器---交付日期变更已生成对应代办审批，订单编码：{}，任务ID：{}", orderDeliveryChange.getOrderNo(), agencyApprovalDto.getTaskId());
            return;
        }
        SysUser user = new SysUser();
        user.setRoleId(Long.valueOf(agencyApprovalDto.getAssignee()));
        List<SysUser> userList = remoteRoleService.internalGetRoleUser(user);
        if (CollectionUtil.isEmpty(userList)) {
            log.info("订单-项目交付日期变更待办审批新增监听器---角色下没有用户，角色ID：{}", agencyApprovalDto.getAssignee());
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("orderNo", orderDeliveryChange.getOrderNo());
        map.put("projectNo", orderDeliveryChange.getProjectNo());
        //创建代办审批数据
        List<DiAgencyApproval> saveList = new ArrayList<>();
        userList.forEach(userItem -> {
            DiAgencyApproval agencyApproval = new DiAgencyApproval();
            agencyApproval.setBusinessKey(orderDeliveryChange.getOrderNo());
            agencyApproval.setJumpKey(orderDeliveryChange.getOrderNo());
            agencyApproval.setApprovalType(ApprovalTypeEnum.POD_CHANGE_APPROVAL.getTypeDesc());
            agencyApproval.setApprovalTypeCode(ApprovalTypeEnum.POD_CHANGE_APPROVAL.getTypeCode());
            agencyApproval.setCamundaTaskId(agencyApprovalDto.getTaskId());
            agencyApproval.setCamundaProcessInstanceId(agencyApprovalDto.getProcessInstanceId());
            //发起人
            agencyApproval.setLaunchBy(agencyApprovalDto.getLaunchBy());
            //责任人目前是对应的审批人
            agencyApproval.setLiabilityBy(userItem.getUserName());
            agencyApproval.setLiabilityRoleId(agencyApprovalDto.getAssignee());
            agencyApproval.setUpdateBy(orderDeliveryChange.getCreateBy());
            agencyApproval.setCreateBy(orderDeliveryChange.getCreateBy());
            agencyApproval.setContext(JSONUtil.toJsonStr(map));
            saveList.add(agencyApproval);
            //发送钉钉通知
            orderCommonService.sendPodChangeApprovalNotice(orderDeliveryChange.getOrderNo(), userItem.getUserName(),
                    StringUtils.isNotBlank(agencyApprovalDto.getLaunchByName()) ? agencyApprovalDto.getLaunchByName() : "", agencyApproval);
        });
        diAgencyApprovalService.saveBatch(saveList);
        log.info("订单-项目交付日期变更待办审批新增监听器---生成代办事项成功");
    }

//    /**
//     * 发送审批钉钉消息
//     *
//     * @param orderNo      订单编码
//     * @param sendUser     发送人
//     * @param launchByName 发起人姓名
//     */
//    private void sendDingTalkNotice(String orderNo, String sendUser, String launchByName, DiAgencyApproval agencyApproval) {
//        DiMessageList diMessageList = new DiMessageList();
//        diMessageList.setBusinessModule(ApprovalNoticeEnum.POD_CHANGE_APPROVAL_NOTICE.getModule());
//        String title = StrUtil.format(ApprovalNoticeEnum.POD_CHANGE_APPROVAL_NOTICE.getTitle(), orderNo);
//        diMessageList.setTitle(title);
//        diMessageList.setSendingTime(new Date());
//        diMessageList.setRemarks(diMessageList.getTitle());
//        String content = StrUtil.format(ApprovalNoticeEnum.POD_CHANGE_APPROVAL_NOTICE.getMessage(), orderNo, launchByName);
//        diMessageList.setContent(content);
//        diMessageList.setSendingUser(sendUser);
//        String jumpPathArg = null;
//        try {
//            jumpPathArg = "&businessKey=" + agencyApproval.getBusinessKey() +
//                    "&jumpKey=" + agencyApproval.getJumpKey() +
//                    "&approvalTypeCode=" + agencyApproval.getApprovalTypeCode() +
//                    "&agencyType=2";
//        } catch (Exception e) {
//            log.error("PODChangeAgencyApprovalAddListener---sendDingTalkNotice()---构建跳转路径失败，失败原因{}", e.toString());
//        }
//        if (StringUtils.isNotBlank(jumpPathArg)) {
//            diMessageList.setJumpPathArg(jumpPathArg);
//        }
//        iDiMessageListService.insertDiMessageList(diMessageList);
//    }

}
