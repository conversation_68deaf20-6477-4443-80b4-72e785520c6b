package com.dyd.di.pre.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.PreSaleQuoteStatusEnum;
import com.dyd.common.core.enums.SequenceEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.*;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyApprovalInfoDTO;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.domain.vo.TaskArgs;
import com.dyd.di.agency.domain.vo.TaskChangeResult;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.contract.constants.ContractConstants;
import com.dyd.di.contract.entity.DiContract;
import com.dyd.di.contract.mapper.DiContractMapper;
import com.dyd.di.home.domain.DdUser;
import com.dyd.di.home.mapper.DdUserMapper;
import com.dyd.di.marketing.domain.DiMarketingCustomer;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.domain.DiMarketingNicheDemand;
import com.dyd.di.marketing.domain.dto.FullAddressDTO;
import com.dyd.di.marketing.enums.NicheStatusEnum;
import com.dyd.di.marketing.service.DiMarketingNicheDemandService;
import com.dyd.di.marketing.service.IDiMarketingCustomerService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.domain.vo.DiMaterialStorageVo;
import com.dyd.di.material.entity.DiMaterialStockInfo;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.material.service.DiMaterialStockInfoService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.domain.DiMaterielPrice;
import com.dyd.di.materiel.pojo.dto.MaterielChecklistNewDTO;
import com.dyd.di.materiel.pojo.dto.MaterielFeeDTO;
import com.dyd.di.materiel.pojo.dto.MaterielFeeQueryDTO;
//import com.dyd.di.materiel.service.DiMaterielVersionService;
import com.dyd.di.materiel.service.IDiMaterielBomService;
import com.dyd.di.materiel.service.IDiMaterielPriceService;
import com.dyd.di.materiel.service.IDiMaterielService;
//import com.dyd.di.materiel.service.impl.DiMaterielVersionBomServiceImpl;
import com.dyd.di.materiel.service.IMultiVersionMaterielService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.enums.OrderPreSaleStatusEnum;
import com.dyd.di.order.service.IDiOrderService;
import com.dyd.di.pre.conver.PreSaleConverUtil;
import com.dyd.di.pre.domain.DiPreSaleCustomized;
import com.dyd.di.pre.domain.DiPreSaleManifestWrapper;
import com.dyd.di.pre.domain.DiPreSaleQuoteEstimatingGp;
import com.dyd.di.pre.domain.DiPreSaleQuoteGpGroup;
import com.dyd.di.pre.domain.request.*;
import com.dyd.di.pre.domain.response.*;
import com.dyd.di.pre.entity.*;
import com.dyd.di.pre.enums.*;
import com.dyd.di.pre.mapper.*;
import com.dyd.di.pre.service.*;
import com.dyd.di.process.mapper.DiProjectRelationMapper;
import com.dyd.di.process.service.DiProjectRelationUserService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.di.tools.dto.query.MultiFeeCalcQry;
import com.dyd.di.tools.dto.resp.FeeResp;
import com.dyd.di.tools.dto.resp.PreSaleFee;
import com.dyd.di.tools.dto.resp.QuoteFeeResp;
import com.dyd.di.tools.entity.ToolsDesignCost;
import com.dyd.di.tools.service.QuotationCalcService;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteRoleService;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.domain.SysDictData;
import com.dyd.system.api.domain.SysRole;
import com.dyd.system.api.model.QueryDict;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dyd.di.materiel.service.impl.DiMaterielBomServiceImpl.getShowVersionNo;

/**
 * 销售报价单service
 */
@Service
@Slf4j
public class PreSaleQuoteService {

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Autowired
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private IDiPreSaleService diPreSaleService;

    @Autowired
    private IDiOrderService diOrderService;


    @Autowired
    private IDiMarketingNicheService iDiMarketingNicheService;

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private IDiMaterielService iDiMaterielService;

    @Autowired
    private DiMaterialStockInfoService diMaterialStockInfoService;

    @Resource
    private IDiMaterielPriceService diMaterielPriceService;

    @Resource
    private CommonService commonService;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private IDiMarketingCustomerService diMarketingCustomerService;

    @Autowired
    private DiProjectRelationUserService diProjectRelationUserService;

    @Autowired
    private DiProjectRelationMapper diProjectRelationMapper;

    @Autowired
    private DiPreSaleQuotePeriodMapper diPreSaleQuotePeriodMapper;

    @Autowired
    private DiPreSaleCustomizedMapper diPreSaleCustomizedMapper;

    @Autowired
    private DiAgencyApprovalService diAgencyApprovalService;

    @Autowired
    private RemoteRoleService remoteRoleService;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private DiPreSaleUrlMapper diPreSaleUrlMapper;

    @Autowired
    private DiPreSaleLabelMapper diPreSaleLabelMapper;

//    @Autowired
//    private DiMaterielVersionService diMaterielVersionService;

    @Autowired
    private IMultiVersionMaterielService multiVersionMaterielService;

    @Autowired
    private QuotationCalcService quotationCalcService;

    @Autowired
    private RemoteSysService remoteSysService;

    @Autowired
    private IDiPreSaleMaterielSelectionService selectionService;

    @Autowired
    private DiMarketingNicheDemandService demandService;

    @Autowired
    private IDiPreSaleQuoteShareService saleQuoteShareService;

    @Autowired
    private PreSaleConverUtil preSaleConverUtil;

    @Autowired
    private DiPreSaleQuoteGpService diPreSaleQuoteGpService;

    @Autowired
    private DiPreSaleHistoryService preSaleHistoryService;

    @Autowired
    private IDiMessageListService diMessageListService;
    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private IDiMaterielBomService diMaterielBomService;

    @Autowired
    private IDiPreSaleService iDiPreSaleService;

    @Autowired
    private IPreSaleQuoteService preSaleQuoteService;

    @Autowired
    private DiPreSaleFeeService feedService;

    @Autowired
    private DiContractMapper diContractMapper;

    @Autowired
    private DiAgencyTaskService diAgencyTaskService;


    /**
     * 新增销售报价单
     */
    @Transactional
    public String add(PreSaleQuoteAddRequest request) {


        DiPreSaleQuote diPreSaleQuote = new DiPreSaleQuote();
        //增加校验
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(request.getNicheCode());
        if (Objects.isNull(diMarketingNicheT)) {
            throw new RuntimeException("商机不存在");
        }
        if (CollectionUtil.isEmpty(diMarketingNicheT.getDiMarketingContactsList())) {
            throw new RuntimeException("客户联系人不存在");
        }
        List<String> contactInfoList = diMarketingNicheT.getDiMarketingContactsList().stream().map(contacts -> contacts.getContactsName() + "-" + contacts.getContactsPhone()).toList();
        if (!contactInfoList.contains(request.getContactsName() + "-" + request.getContactsPhone())) {
            throw new RuntimeException("客户联系人不正确");
        }
        diPreSaleQuote.setCompanyName(diMarketingNicheT.getCompanyName());
        diPreSaleQuote.setContactsName(request.getContactsName());
        diPreSaleQuote.setContactsPhone(request.getContactsPhone());


        saleQuoteShareService.checkShare(request.getShareList());


        diPreSaleQuote.setPreSaleQuoteCode(sequenceService.getSequenceNo(SequenceEnum.DYD_XSBJ.getCode()));
        diPreSaleQuote.setNicheCode(request.getNicheCode());
        diPreSaleQuote.setCreateBy(SecurityUtils.getUsername());
        diPreSaleQuote.setPreInvoice(request.getPreInvoice());

        for (PreSaleQuoteAddRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetail : request.getPreSaleQuoteDetailList()) {
            diPreSaleService.updateNum(String.valueOf(preSaleQuoteAddDetail.getPreSaleId()), preSaleQuoteAddDetail.getNum());
        }
        //产品方案
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(request.getPreSaleQuoteDetailList().stream().map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getPreSaleId).collect(Collectors.toList()));
        List<String> preSalesStatus = diPreSales.stream().map(DiPreSale::getPreSaleStatus).toList();
        if (preSalesStatus.contains(PreSaleStatusEnum.WAIVER.getCode())) {
            throw new RuntimeException("已放弃的产品方案不能发起报价");
        }
        //diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().in(DiPreSale::getPreSaleCode, request.getPreSaleQuoteDetailList().stream().map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getPreSaleCode).collect(Collectors.toList())));

        //销售报价合计
        BigDecimal saleQuoteTotal = request.getPreSaleQuoteDetailList().stream().filter(x -> Objects.nonNull(x.getSaleQuote()))
                .map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getSaleQuote).reduce(BigDecimal.ZERO, BigDecimal::add);
        //理论成本合计->测算成本合计
        //BigDecimal costFeeTotal = new BigDecimal("0");
        var tradePreSaleIdList = diPreSales.stream().filter(x -> x.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode())).map(x -> x.getId()).toList();

        if (CollectionUtils.isNotEmpty(tradePreSaleIdList)) {
            //贸易类方案发起报价时需要校验，方案中已到期物料清单是否为空，如果为空，则允许报价，如果非空，则提示该方案尚有物料价格、货期或有效期不合法，请询价后再试
            Set<Long> badPreSaleIdList = new HashSet<>();
            List<MaterielChecklistNewDTO> list = iDiPreSaleService.queryTobeQuotedMaterialList(tradePreSaleIdList, badPreSaleIdList);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                String preSaleCodes = diPreSales.stream().filter(x -> badPreSaleIdList.contains(x.getId()))
                        .map(x -> x.getPreSaleCode()).collect(Collectors.joining(","));
                throw new ServiceException("发起失败!方案[" + preSaleCodes + "]尚有物料价格、货期或有效期不合法，请询价后再试");
            }
        }

        // 从新计算成本
        PreSaleQuotationFeeCalcTempRequest requestTemp = new PreSaleQuotationFeeCalcTempRequest();
        requestTemp.setCountryCode(request.getCountryCode());
        requestTemp.setProvinceCode(request.getProvinceCode());
        requestTemp.setCityCode(request.getCityCode());
        requestTemp.setAreaCode(request.getAreaCode());
        requestTemp.setBusinessCost(request.getBusinessCost());
        requestTemp.setNicheCode(request.getNicheCode());
        requestTemp.setPreSaleInfoList(request.getPreSaleQuoteDetailList().stream().map(x -> {
            PreSaleQuotationFeeCalcTempRequest.preSaleInfo preSaleInfo = new PreSaleQuotationFeeCalcTempRequest.preSaleInfo();
            preSaleInfo.setPreSaleId(x.getPreSaleId());
            preSaleInfo.setQuantity(BigDecimal.valueOf(x.getNum()));
            preSaleInfo.setPreSaleQuote(x.getSaleQuote());
            return preSaleInfo;
        }).collect(Collectors.toList()));
        PreSaleQuotationFeeCalcResponse calcResponse = this.preSaleQuotationFeeCalcTemp(requestTemp);


        //成本测算合计
        BigDecimal totalEstimate = calcResponse.getQuoteSummary().getPreSaleFeeMap().values().stream()
                .map(x -> x.getTotalWithoutGp2()).reduce(BigDecimal.ZERO, BigDecimal::add);

        /**
         request.getPreSaleQuoteDetailList().stream()
         .filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getCostFeeSum()))
         .map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getCostFeeSum)
         .reduce(BigDecimal.ZERO, BigDecimal::add).add(request.getBusinessCost());
         */

        diPreSaleQuote.setCostFeeTotal(totalEstimate);


        diPreSaleQuote.setSaleQuoteTotal(request.getPreSaleQuoteDetailList().stream()
                .filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getSaleQuote()))
                .map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getSaleQuote)
                .reduce(BigDecimal.ZERO, BigDecimal::add));


        //6. 毛利额，计算逻辑修改为 对应销售报价-成本测算合计
        diPreSaleQuote.setGrossProfitTotal(diPreSaleQuote.getSaleQuoteTotal().subtract(totalEstimate));
        if (Objects.nonNull(diPreSaleQuote.getGrossProfitTotal()) && Objects.nonNull(diPreSaleQuote.getSaleQuoteTotal()) && BigDecimal.ZERO.compareTo(diPreSaleQuote.getSaleQuoteTotal()) != 0) {
            diPreSaleQuote.setGrossMarginTotal(diPreSaleQuote.getGrossProfitTotal().divide(diPreSaleQuote.getSaleQuoteTotal(), 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP));
        }

        //销售服务报价合计
        BigDecimal saleServiceQuoteTotal = BigDecimal.ZERO;// request.getPreSaleQuoteDetailList().stream()
//                .filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getSaleServiceQuote()))
//                .map(PreSaleQuoteAddRequest.PreSaleQuoteAddDetail::getSaleServiceQuote)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        diPreSaleQuote.setSaleServiceQuoteTotal(saleServiceQuoteTotal);

        diPreSaleQuote.setExpecteDate(request.getExpecteDate());
        diPreSaleQuote.setProvinceCode(request.getProvinceCode());
        diPreSaleQuote.setProvinceName(request.getProvinceName());
        diPreSaleQuote.setCityName(request.getCityName());
        diPreSaleQuote.setCityCode(request.getCityCode());
        diPreSaleQuote.setAreaName(request.getAreaName());
        diPreSaleQuote.setAreaCode(request.getAreaCode());
        diPreSaleQuote.setCountryName(request.getCountryName());
        diPreSaleQuote.setCountryCode(request.getCountryCode());
        diPreSaleQuote.setCommonDeliveryAddress(request.getCommonDeliveryAddress());
        diPreSaleQuote.setBusinessCost(request.getBusinessCost());
        if (null != request.getIsAdvanceExecution()) {
            diPreSaleQuote.setIsAdvanceExecution(request.getIsAdvanceExecution());
        }
        if (request.getExpecteDate() != null) {
            diPreSaleQuote.setExpectationDeliveryWeek(request.getExpectationDeliveryWeek());
        } else if (null != diMarketingNicheT.getExpectationDeliveryWeek()) {
            diPreSaleQuote.setExpectationDeliveryWeek(diMarketingNicheT.getExpectationDeliveryWeek());
        }
        Boolean deliveryReviewFlag = diPreSales.stream().anyMatch(x -> PreSaleDeliveryFlagEnum.NEED.getCode().equals(x.getDeliveryReviewFlag()));
        diPreSaleQuote.setFinallyDeliveryWeek(preSaleQuoteService.queryAfterCheckDeliveryTimeUnitWeek(diPreSales));
        diPreSaleQuote.setDeliveryReviewFlag(deliveryReviewFlag ? PreSaleDeliveryFlagEnum.NEED.getCode() : PreSaleDeliveryFlagEnum.NO_NEED.getCode());
        diPreSaleQuoteMapper.insert(diPreSaleQuote);


        //产品方案map
        Map<String, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));

        //产品方案物料map，key为产品方案id
        Map<Long, List<DiPreSaleManifest>> diPreSaleManifestMap = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId)
                .collect(Collectors.toList()))).stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));
        //报价单明细
        List<PreSaleQuoteAddRequest.PreSaleQuoteAddDetail> preSaleQuoteAddDetailList = request.getPreSaleQuoteDetailList();

        //销售服务周期报价成本合计
        BigDecimal saleServiceCycleTotalT = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(preSaleQuoteAddDetailList)) {
            List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = new ArrayList<>();
            for (PreSaleQuoteAddRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetail : preSaleQuoteAddDetailList) {
                DiPreSaleQuoteDetail diPreSaleQuoteDetail = new DiPreSaleQuoteDetail();
                diPreSaleQuoteDetail.setPreSaleQuoteId(diPreSaleQuote.getId());
                diPreSaleQuoteDetail.setPreSaleId(preSaleQuoteAddDetail.getPreSaleId());
                diPreSaleQuoteDetail.setPreSaleCode(preSaleQuoteAddDetail.getPreSaleCode());

                DiPreSale diPreSale = preSaleMap.get(preSaleQuoteAddDetail.getPreSaleCode());

                String industry = quotationCalcService.toToolsIndustry(diPreSale.getIndustryCategories());
                if (StringUtils.isBlank(industry)) {
                    industry = diMarketingNicheT.getIndustryCategories();
                }
                ToolsDesignCost conf = quotationCalcService.getDesignConf(industry,
                        diPreSale.getDifficultyLevel(),
                        diPreSale.getMechanicalDifficulty(),
                        diPreSale.getElectricalDifficulty(),
                        diPreSale.getProductionDifficulty(),
                        diPreSale.getAfterSalesDifficulty());
                BigDecimal gp = this.findEstimatingCost(calcResponse, diPreSale.getPreSaleCode());
                this.calcQuoteItemFee(diPreSaleQuoteDetail, preSaleQuoteAddDetail.getSaleQuote(), gp, diPreSale, conf);
                if (null != diPreSaleQuoteDetail.getSaleServiceCycle()) {
                    saleServiceCycleTotalT = saleServiceCycleTotalT.add(diPreSaleQuoteDetail.getSaleServiceCycle());
                }
                if (null != diPreSaleQuoteDetail.getSaleServiceQuote()) {
                    saleServiceQuoteTotal = saleServiceQuoteTotal.add(diPreSaleQuoteDetail.getSaleServiceQuote());
                }
                preSaleQuoteDetailList.add(diPreSaleQuoteDetail);
            }
            diPreSaleQuoteDetailMapper.insertBatchSomeColumn(preSaleQuoteDetailList);

            for (DiPreSaleQuoteDetail diPreSaleQuoteDetail : preSaleQuoteDetailList) {
                diPreSaleService.updateToBusinessQuotes(diPreSaleQuoteDetail.getPreSaleId());
            }
        }

        //更新报价单销售服务周期报价成本合计
        diPreSaleQuote.setSaleServiceCycleTotal(saleServiceCycleTotalT);
        diPreSaleQuote.setSaleServiceQuoteTotal(saleServiceQuoteTotal);
        DiPreSaleQuote diPreSaleQuoteT = new DiPreSaleQuote();
        diPreSaleQuoteT.setSaleServiceCycleTotal(saleServiceCycleTotalT);
        diPreSaleQuoteT.setSaleServiceQuoteTotal(saleServiceQuoteTotal);
        diPreSaleQuoteT.setId(diPreSaleQuote.getId());
        diPreSaleQuoteT.setCalculateDeliveryWeek(preSaleQuoteService.queryCalculateDeliveryTimeUnitWeek(diPreSales));
        diPreSaleQuoteMapper.updateById(diPreSaleQuoteT);

        //账期
        List<PreSaleQuoteAddRequest.PreSaleQuotePeriod> preSaleQuotePeriods = request.getPreSaleQuotePeriods();
        if (CollectionUtil.isNotEmpty(preSaleQuotePeriods)) {
            //支付金额合计
            BigDecimal payAmountTotal = preSaleQuotePeriods.stream().map(PreSaleQuoteAddRequest.PreSaleQuotePeriod::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (payAmountTotal.compareTo(diPreSaleQuote.getSaleQuoteTotal()) != 0) {
                throw new RuntimeException("支付金额之和不等于 销售整单总报价");
            }
            //销售报价合计
            //BigDecimal saleTotal = null;
//            if (Objects.nonNull(diPreSaleQuote.getSaleQuoteTotal()) && Objects.nonNull(diPreSaleQuote.getSaleServiceQuoteTotal())) {
//                saleTotal = diPreSaleQuote.getSaleQuoteTotal();
//            }

            for (PreSaleQuoteAddRequest.PreSaleQuotePeriod preSaleQuotePeriod : preSaleQuotePeriods) {
                DiPreSaleQuotePeriod diPreSaleQuotePeriod = new DiPreSaleQuotePeriod();
                diPreSaleQuotePeriod.setDiPreSaleQuoteId(diPreSaleQuote.getId());
                diPreSaleQuotePeriod.setBillPeriod(preSaleQuotePeriod.getBillPeriod());
                diPreSaleQuotePeriod.setPayAmount(preSaleQuotePeriod.getPayAmount());
                diPreSaleQuotePeriod.setPayPercent(preSaleQuotePeriod.getPayPercent());
                diPreSaleQuotePeriod.setRemark(preSaleQuotePeriod.getRemark());
                diPreSaleQuotePeriod.setPlanPayDate(preSaleQuotePeriod.getPlanPayDate());
                diPreSaleQuotePeriod.setRate(preSaleQuotePeriod.getRate());
                diPreSaleQuotePeriod.setInvoiceDesc(preSaleQuotePeriod.getInvoiceDesc());
                diPreSaleQuotePeriod.setRealPayDate(preSaleQuotePeriod.getRealPayDate());
                diPreSaleQuotePeriodMapper.insert(diPreSaleQuotePeriod);
            }

        }
        saleQuoteShareService.compareAndSaveShare(Long.valueOf(diPreSaleQuote.getId()), request.getShareList());

        //将GP1234落库
        List<DiPreSaleQuoteGp> gpList = PreSaleQuoteService.feeCalcResult2QuoteGp(diPreSaleQuote, calcResponse);
        diPreSaleQuoteGpService.upset(gpList);

        //判断是否提前执行
        if (diPreSaleQuote.getIsAdvanceExecution() != null && diPreSaleQuote.getIsAdvanceExecution().equals(2)) {
            DiMarketingNiche marketingNiche = new DiMarketingNiche();
            marketingNiche.setId(diMarketingNicheT.getId());
            marketingNiche.setNicheStatus("7");
            iDiMarketingNicheService.updateById(marketingNiche);
        }
        return diPreSaleQuote.getPreSaleQuoteCode();
    }


    /**
     * 编辑报价单
     *
     * @param request
     */
    @Transactional
    public void edit(PreSaleQuoteEditRequest request) {


        saleQuoteShareService.checkShare(request.getShareList());
        DiPreSaleQuote diPreSaleQuoteT = diPreSaleQuoteMapper.selectById(request.getId());
        if (null != diPreSaleQuoteT.getExpectationDeliveryWeek()) {
            if (!diPreSaleQuoteT.getExpectationDeliveryWeek().equals(request.getExpectationDeliveryWeek())
                    && !diPreSaleQuoteT.getApprovalStatus().equals(0) && !diPreSaleQuoteT.getApprovalStatus().equals(3)) {
                throw new RuntimeException("当前状态不允许编辑期望交期（周）");
            }
        }

        DiPreSaleQuote diPreSaleQuote = new DiPreSaleQuote();
        diPreSaleQuote.setPreSaleQuoteCode(diPreSaleQuoteT.getPreSaleQuoteCode());
        //增加校验
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(diPreSaleQuoteT.getNicheCode());
        if (Objects.isNull(diMarketingNicheT)) {
            throw new RuntimeException("商机不存在");
        }
        if (CollectionUtil.isEmpty(diMarketingNicheT.getDiMarketingContactsList())) {
            throw new RuntimeException("客户联系人不存在");
        }
        List<String> contactInfoList = diMarketingNicheT.getDiMarketingContactsList().stream().map(contacts -> contacts.getContactsName() + "-" + contacts.getContactsPhone()).toList();
        if (!contactInfoList.contains(request.getContactsName() + "-" + request.getContactsPhone())) {
            throw new RuntimeException("客户联系人不正确");
        }
        diPreSaleQuote.setCompanyName(diMarketingNicheT.getCompanyName());
        diPreSaleQuote.setContactsName(request.getContactsName());
        diPreSaleQuote.setContactsPhone(request.getContactsPhone());


        diPreSaleQuote.setId(request.getId());
        diPreSaleQuote.setUpdateBy(SecurityUtils.getUsername());
        diPreSaleQuote.setPreInvoice(request.getPreInvoice());

        PreSaleQuotationFeeCalcTempRequest requestTemp = new PreSaleQuotationFeeCalcTempRequest();
        requestTemp.setCountryCode(request.getCountryCode());
        requestTemp.setProvinceCode(request.getProvinceCode());
        requestTemp.setCityCode(request.getCityCode());
        requestTemp.setAreaCode(request.getAreaCode());
        requestTemp.setBusinessCost(request.getBusinessCost());
        requestTemp.setNicheCode(diPreSaleQuoteT.getNicheCode());
        requestTemp.setPreSaleInfoList(request.getPreSaleQuoteDetailList().stream().map(x -> {
            PreSaleQuotationFeeCalcTempRequest.preSaleInfo preSaleInfo = new PreSaleQuotationFeeCalcTempRequest.preSaleInfo();
            preSaleInfo.setPreSaleId(Long.valueOf(x.getPreSaleId()));
            preSaleInfo.setQuantity(BigDecimal.valueOf(x.getNum()));
            preSaleInfo.setPreSaleQuote(x.getSaleQuote());
            return preSaleInfo;
        }).collect(Collectors.toList()));
        PreSaleQuotationFeeCalcResponse calcResponse = this.preSaleQuotationFeeCalcTemp(requestTemp);

        //成本测算合计
        BigDecimal totalEstimate = calcResponse.getQuoteSummary().getPreSaleFeeMap().values().stream()
                .map(x -> x.getTotalWithoutGp2()).reduce(BigDecimal.ZERO, BigDecimal::add);


        //产品方案
        //成本测算合计
//        BigDecimal totalEstimate = request.getPreSaleQuoteDetailList().stream()
//                .filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getCostFeeSum()))
//                .map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getCostFeeSum)
//                .reduce(BigDecimal.ZERO, BigDecimal::add).add(request.getBusinessCost());

        BigDecimal saleQuoteTotal = request.getPreSaleQuoteDetailList().stream().filter(x -> Objects.nonNull(x.getSaleQuote()))
                .map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getSaleQuote).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<Long> preIds = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().in(DiPreSaleQuoteDetail::getId, request.getPreSaleQuoteDetailList().stream().map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getId).collect(Collectors.toList()))).stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList());
        //产品方案
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preIds);
        //产品方案map
        Map<String, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));

        var tradePreSaleIdList = diPreSales.stream().filter(x -> x.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode())).map(DiPreSale::getId).toList();
        if (CollectionUtils.isNotEmpty(tradePreSaleIdList)) {
            Set<Long> preSaleIds = new HashSet<>();

            List<MaterielChecklistNewDTO> list = iDiPreSaleService.queryTobeQuotedMaterialList(tradePreSaleIdList, preSaleIds);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                String preSaleQuoteCodes = diPreSales.stream().filter(x -> preSaleIds.contains(x.getId()))
                        .map(DiPreSale::getPreSaleCode).collect(Collectors.joining(","));
                throw new ServiceException("发起失败!方案[" + preSaleQuoteCodes + "]尚有物料价格、货期或有效期不合法，请询价后再试");
            }
        }

        diPreSaleQuote.setCostFeeTotal(totalEstimate);

        diPreSaleQuote.setSaleQuoteTotal(request.getPreSaleQuoteDetailList().stream().filter(preSaleQuoteEditDetail -> Objects.nonNull(preSaleQuoteEditDetail.getSaleQuote()))
                .map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getSaleQuote)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        diPreSaleQuote.setGrossProfitTotal(diPreSaleQuote.getSaleQuoteTotal().subtract(totalEstimate));
        if (Objects.nonNull(diPreSaleQuote.getGrossProfitTotal()) && Objects.nonNull(diPreSaleQuote.getSaleQuoteTotal()) && BigDecimal.ZERO.compareTo(diPreSaleQuote.getSaleQuoteTotal()) != 0) {
            diPreSaleQuote.setGrossMarginTotal(diPreSaleQuote.getGrossProfitTotal().divide(diPreSaleQuote.getSaleQuoteTotal(), 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP));
        }
        diPreSaleQuote.setContactsName(request.getContactsName());
        diPreSaleQuote.setContactsPhone(request.getContactsPhone());
        //销售服务报价合计
        BigDecimal saleServiceQuoteTotal = request.getPreSaleQuoteDetailList().stream().filter(preSaleQuoteEditDetail -> Objects.nonNull(preSaleQuoteEditDetail.getSaleServiceQuote())).map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getSaleServiceQuote).reduce(BigDecimal.ZERO, BigDecimal::add);
        diPreSaleQuote.setSaleServiceQuoteTotal(saleServiceQuoteTotal);
        //地址
        diPreSaleQuote.setCountryName(request.getCountryName());
        diPreSaleQuote.setCountryCode(request.getCountryCode());
        diPreSaleQuote.setProvinceName(request.getProvinceName());
        diPreSaleQuote.setProvinceCode(request.getProvinceCode());
        diPreSaleQuote.setCityName(request.getCityName());
        diPreSaleQuote.setCityCode(request.getCityCode());
        diPreSaleQuote.setAreaName(request.getAreaName());
        diPreSaleQuote.setAreaCode(request.getAreaCode());
        diPreSaleQuote.setCommonDeliveryAddress(request.getCommonDeliveryAddress());
        diPreSaleQuote.setBusinessCost(request.getBusinessCost());
        if (null != request.getExpectationDeliveryWeek()) {
            diPreSaleQuote.setExpectationDeliveryWeek(request.getExpectationDeliveryWeek());
        }

        //产品方案物料map，key为产品方案id

        //报价单明细
        List<PreSaleQuoteEditRequest.PreSaleQuoteEditDetail> preSaleQuoteEditDetailList = request.getPreSaleQuoteDetailList();
        if (CollectionUtils.isNotEmpty(preSaleQuoteEditDetailList)) {
            //报价单明细map,key为id
            Map<Integer, DiPreSaleQuoteDetail> quoteDetailMap = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery()
                    .in(DiPreSaleQuoteDetail::getId, preSaleQuoteEditDetailList.stream().map(PreSaleQuoteEditRequest.PreSaleQuoteEditDetail::getId)
                            .collect(Collectors.toList()))).stream().collect(Collectors.toMap(DiPreSaleQuoteDetail::getId, Function.identity()));
            List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = new ArrayList<>();

            BigDecimal saleServiceCycleTotalT = BigDecimal.ZERO;
            for (PreSaleQuoteEditRequest.PreSaleQuoteEditDetail preSaleQuoteEditDetail : preSaleQuoteEditDetailList) {
                //更新需求套数
                diPreSaleService.updateNum(preSaleQuoteEditDetail.getPreSaleId(), preSaleQuoteEditDetail.getNum());

                DiPreSaleQuoteDetail diPreSaleQuoteDetail = new DiPreSaleQuoteDetail();
                diPreSaleQuoteDetail.setId(preSaleQuoteEditDetail.getId());
                diPreSaleQuoteDetail.setSaleQuote(preSaleQuoteEditDetail.getSaleQuote());
                diPreSaleQuoteDetail.setDeliveryQuote(preSaleQuoteEditDetail.getDeliveryQuote());

                DiPreSaleQuoteDetail diPreSaleQuoteDetailT = quoteDetailMap.get(preSaleQuoteEditDetail.getId());
                DiPreSale diPreSale = preSaleMap.get(diPreSaleQuoteDetailT.getPreSaleCode());

                String industry = quotationCalcService.toToolsIndustry(diPreSale.getIndustryCategories());
                if (StringUtils.isBlank(industry)) {
                    industry = diMarketingNicheT.getIndustryCategories();
                }
                ToolsDesignCost conf = quotationCalcService.getDesignConf(industry, diPreSale.getDifficultyLevel(),
                        diPreSale.getMechanicalDifficulty(),
                        diPreSale.getElectricalDifficulty(),
                        diPreSale.getProductionDifficulty(),
                        diPreSale.getAfterSalesDifficulty());

                BigDecimal gp = this.findEstimatingCost(calcResponse, diPreSale.getPreSaleCode());
                this.calcQuoteItemFee(diPreSaleQuoteDetail, preSaleQuoteEditDetail.getSaleQuote(), gp, diPreSale, conf);
                if (null != diPreSaleQuoteDetail.getSaleServiceCycle()) {
                    saleServiceCycleTotalT = saleServiceCycleTotalT.add(diPreSaleQuoteDetail.getSaleServiceCycle());
                }
                if (null != diPreSaleQuoteDetail.getSaleServiceQuote()) {
                    saleServiceQuoteTotal = saleServiceQuoteTotal.add(diPreSaleQuoteDetail.getSaleServiceQuote());
                }


                preSaleQuoteDetailList.add(diPreSaleQuoteDetail);
            }
            for (DiPreSaleQuoteDetail diPreSaleQuoteDetail : preSaleQuoteDetailList) {
                diPreSaleQuoteDetailMapper.updateById(diPreSaleQuoteDetail);
            }
            diPreSaleQuote.setSaleServiceCycleTotal(saleServiceCycleTotalT);
            diPreSaleQuote.setExpecteDate(request.getExpecteDate());
            diPreSaleQuoteMapper.updateById(diPreSaleQuote);
        }


        diPreSaleQuotePeriodMapper.delete(Wrappers.<DiPreSaleQuotePeriod>lambdaUpdate().eq(DiPreSaleQuotePeriod::getDiPreSaleQuoteId, request.getId()));
        List<PreSaleQuoteEditRequest.PreSaleQuotePeriod> preSaleQuotePeriods = request.getPreSaleQuotePeriods();

        //BigDecimal saleTotal = null;
//        if (Objects.nonNull(diPreSaleQuote.getSaleQuoteTotal()) && Objects.nonNull(diPreSaleQuote.getSaleServiceQuoteTotal())) {
//            saleTotal = diPreSaleQuote.getSaleQuoteTotal().add(diPreSaleQuote.getSaleServiceQuoteTotal());
//        }
        for (PreSaleQuoteEditRequest.PreSaleQuotePeriod preSaleQuotePeriod : preSaleQuotePeriods) {
            DiPreSaleQuotePeriod diPreSaleQuotePeriod = new DiPreSaleQuotePeriod();
            diPreSaleQuotePeriod.setDiPreSaleQuoteId(diPreSaleQuote.getId());
            diPreSaleQuotePeriod.setBillPeriod(preSaleQuotePeriod.getBillPeriod());
            diPreSaleQuotePeriod.setPayAmount(preSaleQuotePeriod.getPayAmount());
            diPreSaleQuotePeriod.setPayPercent(preSaleQuotePeriod.getPayPercent());
            diPreSaleQuotePeriod.setRemark(preSaleQuotePeriod.getRemark());
            diPreSaleQuotePeriod.setPlanPayDate(preSaleQuotePeriod.getPlanPayDate());
            diPreSaleQuotePeriod.setRate(preSaleQuotePeriod.getRate());
            diPreSaleQuotePeriod.setInvoiceDesc(preSaleQuotePeriod.getInvoiceDesc());
            diPreSaleQuotePeriod.setRealPayDate(preSaleQuotePeriod.getRealPayDate());
            diPreSaleQuotePeriodMapper.insert(diPreSaleQuotePeriod);
        }

        saleQuoteShareService.compareAndSaveShare(Long.valueOf(diPreSaleQuote.getId()), request.getShareList());

        //更新GP1234
        List<DiPreSaleQuoteGp> gpList = PreSaleQuoteService.feeCalcResult2QuoteGp(diPreSaleQuote, calcResponse);
        diPreSaleQuoteGpService.upset(gpList);
    }

    BigDecimal findEstimatingCost(PreSaleQuotationFeeCalcResponse calcResponse, String preSaleCode) {
        PreSaleFee preSaleFee = null;
        if (calcResponse.getQuote4Project() != null && MapUtils.isNotEmpty(calcResponse.getQuote4Project().getPreSaleFeeMap())) {
            if (calcResponse.getQuote4Project().getPreSaleFeeMap().containsKey(preSaleCode)) {
                preSaleFee = calcResponse.getQuote4Project().getPreSaleFeeMap().get(preSaleCode);
            }
        }
        if (calcResponse.getQuote4Trade() != null && MapUtils.isNotEmpty(calcResponse.getQuote4Trade().getPreSaleFeeMap())) {
            if (calcResponse.getQuote4Trade().getPreSaleFeeMap().containsKey(preSaleCode)) {
                preSaleFee = calcResponse.getQuote4Trade().getPreSaleFeeMap().get(preSaleCode);
            }
        }

        if (preSaleFee == null) {
            return BigDecimal.ZERO;
        }

        return MoneyUtils.sum(
                preSaleFee.getMaterialCost(),
                preSaleFee.getProcessCost(),
                preSaleFee.getPackageCost(),
                preSaleFee.getDesignCost(),
                preSaleFee.getAfterSaleCost(),
                preSaleFee.getLogisticsCost()
        );
    }

    /**
     * @param diPreSaleQuoteDetail
     * @param saleQuote            销售报价
     * @param preSaleEstimate      测算成本
     */
    public void calcQuoteItemFee(DiPreSaleQuoteDetail diPreSaleQuoteDetail,
                                 BigDecimal saleQuote, BigDecimal preSaleEstimate,
                                 DiPreSale diPreSale,
                                 ToolsDesignCost conf
    ) {

        //1. 隐藏单套售后施工费、单套售后施工周期（天）、售后施工费报价、售后施工周期报价（天）、单套理论成本，销售交期报价（天）六个字段。
        diPreSaleQuoteDetail.setSaleQuote(saleQuote);
        diPreSaleQuoteDetail.setDeliveryQuote(BigDecimal.ZERO); // 无交期报价


        diPreSaleQuoteDetail.setCostFee(preSaleEstimate);
        diPreSaleQuoteDetail.setGrossProfit(saleQuote.subtract(preSaleEstimate));
        if (Objects.nonNull(diPreSaleQuoteDetail.getGrossProfit())
                && BigDecimal.ZERO.compareTo(saleQuote) != 0) {
            diPreSaleQuoteDetail.setGrossMargin(diPreSaleQuoteDetail.getGrossProfit().divide(saleQuote, 2, RoundingMode.HALF_UP));
        } else if (Objects.nonNull(diPreSaleQuoteDetail.getGrossProfit())
                && Objects.nonNull(diPreSaleQuoteDetail.getSaleQuote())
                && BigDecimal.ZERO.compareTo(diPreSaleQuoteDetail.getSaleQuote()) == 0) {
            diPreSaleQuoteDetail.setGrossMargin(diPreSaleQuoteDetail.getGrossProfit());
        }
        //单套理论服务周期
        if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
            DiPreSaleManifest mani = iDiPreSaleService.queryFirstManifest(diPreSale.getId());
            diPreSaleQuoteDetail.setSystemServiceFee(MoneyUtils.sum(BigDecimal.ZERO, mani.getRealDeliveryDebugFee()));
            diPreSaleQuoteDetail.setSystemServiceCycle(MoneyUtils.sum(BigDecimal.ZERO, mani.getRealDeliveryDebugDay()).setScale(0, RoundingMode.CEILING));
            diPreSaleQuoteDetail.setSaleServiceQuote(diPreSaleQuoteDetail.getSystemServiceFee().multiply(new BigDecimal(diPreSale.getNum())));
            diPreSaleQuoteDetail.setSaleServiceCycle(diPreSaleQuoteDetail.getSystemServiceCycle().multiply(new BigDecimal(diPreSale.getNum())));
        } else if (conf != null) {
            diPreSaleQuoteDetail.setSystemServiceFee(conf.getAfterSaleRate().multiply(new BigDecimal("1000")));
            if (diPreSaleQuoteDetail.getSystemServiceFee() != null) {
                diPreSaleQuoteDetail.setSaleServiceQuote(diPreSaleQuoteDetail.getSystemServiceFee().multiply(new BigDecimal(diPreSale.getNum())));
            }
            diPreSaleQuoteDetail.setSystemServiceCycle(conf.getAfterSaleRate().setScale(0, RoundingMode.CEILING));
            diPreSaleQuoteDetail.setSaleServiceCycle(diPreSaleQuoteDetail.getSystemServiceCycle().multiply(new BigDecimal(diPreSale.getNum())));
        }
    }


    /**
     * 报价单列表分页
     *
     * @param request
     * @return
     */
    public PageWrapper<List<PreSaleQuoteListResponse>> getQuoteListPage(PreSaleQuoteListRequest request) {


        //数据权限处理
//        List<String> jobNumberList = commonService.findLoginJobNumberList();
//        if (CollectionUtil.isNotEmpty(jobNumberList)) {
//            request.setJobNumberList(jobNumberList);
//        }

        Page page = PageHelper.startPage(request.getPageNum(), request.getPageSize());

        List<PreSaleQuoteListResponse> preSaleQuoteListResponses = new ArrayList<>();
        //查询
        List<DiPreSaleQuote> diPreSaleQuotes = diPreSaleQuoteMapper.selectPreSaleQuote(request);
        if (CollectionUtils.isNotEmpty(diPreSaleQuotes)) {
            //报价单明细
            List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().in(DiPreSaleQuoteDetail::getPreSaleQuoteId, diPreSaleQuotes.stream().map(DiPreSaleQuote::getId).collect(Collectors.toList())));
            //销售报价单明细map，key为报价单id
            Map<Integer, List<DiPreSaleQuoteDetail>> quoteDetailMap = preSaleQuoteDetails.stream().collect(Collectors.groupingBy(DiPreSaleQuoteDetail::getPreSaleQuoteId));

            //产品方案map
            Map<Long, DiPreSale> preSaleMap = diPreSaleService.queryDiPreSaleByIds(preSaleQuoteDetails.stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));

            for (DiPreSaleQuote diPreSaleQuote : diPreSaleQuotes) {
                PreSaleQuoteListResponse preSaleQuoteListResponse = new PreSaleQuoteListResponse();
                preSaleQuoteListResponse.setPreSaleQuoteCode(diPreSaleQuote.getPreSaleQuoteCode());
                preSaleQuoteListResponse.setId(diPreSaleQuote.getId());
                preSaleQuoteListResponse.setNicheCode(diPreSaleQuote.getNicheCode());
                preSaleQuoteListResponse.setCreateBy(diPreSaleQuote.getCreateBy());
                preSaleQuoteListResponse.setCreateTime(diPreSaleQuote.getCreateTime());
                preSaleQuoteListResponse.setCostFeeTotal(diPreSaleQuote.getCostFeeTotal());
                preSaleQuoteListResponse.setSaleQuoteTotal(diPreSaleQuote.getSaleQuoteTotal());
                preSaleQuoteListResponse.setGrossProfitTotal(diPreSaleQuote.getGrossProfitTotal());
                preSaleQuoteListResponse.setGrossMarginTotal(diPreSaleQuote.getGrossMarginTotal());
                preSaleQuoteListResponse.setQuoteStatus(diPreSaleQuote.getQuoteStatus());
                preSaleQuoteListResponse.setApprovalStatus(diPreSaleQuote.getApprovalStatus());
                preSaleQuoteListResponse.setIsAdvanceExecution(diPreSaleQuote.getIsAdvanceExecution());
                if (null != diPreSaleQuote.getExpectationDeliveryWeek()) {
                    preSaleQuoteListResponse.setExpectationDeliveryWeek(diPreSaleQuote.getExpectationDeliveryWeek());
                }

                preSaleQuoteListResponse.setIsGenerateContract(diPreSaleQuote.getIsGenerateContract());
                //已生成合同,查询合同状态，如已放弃允许重新生成
                if (diPreSaleQuote.getIsGenerateContract() == 2) {
                    Long count = diContractMapper.selectCount(Wrappers.<DiContract>lambdaQuery().ne(DiContract::getContractState, "4").eq(DiContract::getNicheNo, diPreSaleQuote.getNicheCode()));
                    if (count == 0) {
                        preSaleQuoteListResponse.setIsGenerateContract(1);
                    }
                }

                List<BigDecimal> guideDurations = new ArrayList<>();
                //方案编码
                List<Long> preSaleIds = Lists.newArrayList();
                if (quoteDetailMap.containsKey(diPreSaleQuote.getId())) {
                    preSaleIds = quoteDetailMap.get(diPreSaleQuote.getId()).stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList());
                }
                for (Long preSaleId : preSaleIds) {
                    if (Objects.nonNull(preSaleMap.get(preSaleId))) {
                        guideDurations.add(preSaleMap.get(preSaleId).getGuideDuration());
                    }
                }
                if (CollectionUtils.isNotEmpty(guideDurations)) {
                    preSaleQuoteListResponse.setLeadTime(String.valueOf(guideDurations.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0)));

                }

                List<BigDecimal> details = Lists.newArrayList();
                if (quoteDetailMap.containsKey(diPreSaleQuote.getId())) {
                    details = quoteDetailMap.get(diPreSaleQuote.getId()).stream().map(DiPreSaleQuoteDetail::getDeliveryQuote).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(details)) {
                    preSaleQuoteListResponse.setDeliveryQuote(String.valueOf(details.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0)));
                }
                if (StringUtils.isNotEmpty(diPreSaleQuote.getCreateBy())) {
                    List<DdUser> ddUsers = ddUserMapper.selectList(Wrappers.<DdUser>lambdaQuery().eq(DdUser::getJobNumber, diPreSaleQuote.getCreateBy()));
                    if (CollectionUtils.isNotEmpty(ddUsers)) {
                        preSaleQuoteListResponse.setCreateBy(ddUsers.get(0).getName());
                    }
                }
                preSaleQuoteListResponses.add(preSaleQuoteListResponse);
            }
        }

        return PageHelp.render(page, preSaleQuoteListResponses);
    }

    /**
     * 获取详情
     *
     * @param idParam
     * @return
     */
    public PreSaleQuoteInfoResponse info(Integer idParam, String code) {

        PreSaleQuoteInfoResponse preSaleQuoteInfoResponse = new PreSaleQuoteInfoResponse();
        DiPreSaleQuote diPreSaleQuote = null;
        if (Objects.nonNull(idParam)) {
            diPreSaleQuote = diPreSaleQuoteMapper.selectById(idParam);
        } else if (StringUtils.isNotEmpty(code)) {
            diPreSaleQuote = diPreSaleQuoteMapper.selectOne(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getPreSaleQuoteCode, code));
        }
        if (Objects.isNull(diPreSaleQuote)) {
            return preSaleQuoteInfoResponse;
        }

        //商机查询合同
        //商机查询合同
        //List<DiProjectRelation> relationList = diProjectRelationMapper.selectList(Wrappers.<DiProjectRelation>lambdaQuery().eq(DiProjectRelation::getRelationNo, diPreSaleQuote.getNicheCode()));
//        checkDetailPurview(diPreSaleQuote.getCreateBy(), relationList.get(0).getProjectNo());

        Integer id = diPreSaleQuote.getId();

        preSaleQuoteInfoResponse.setPreSaleQuoteCode(diPreSaleQuote.getPreSaleQuoteCode());

        preSaleQuoteInfoResponse.setSaleQuoteTotal(diPreSaleQuote.getSaleQuoteTotal());
        preSaleQuoteInfoResponse.setGrossProfitTotal(diPreSaleQuote.getGrossProfitTotal());
        preSaleQuoteInfoResponse.setGrossMarginTotal(diPreSaleQuote.getGrossMarginTotal());
        preSaleQuoteInfoResponse.setQuoteStatus(diPreSaleQuote.getQuoteStatus());
        preSaleQuoteInfoResponse.setNicheCode(diPreSaleQuote.getNicheCode());
        preSaleQuoteInfoResponse.setId(id);
        preSaleQuoteInfoResponse.setCompanyName(diPreSaleQuote.getCompanyName());
        preSaleQuoteInfoResponse.setContactsName(diPreSaleQuote.getContactsName());
        preSaleQuoteInfoResponse.setContactsPhone(diPreSaleQuote.getContactsPhone());
        preSaleQuoteInfoResponse.setApprovalStatus(diPreSaleQuote.getApprovalStatus());
        preSaleQuoteInfoResponse.setSaleServiceQuoteTotal(diPreSaleQuote.getSaleServiceQuoteTotal());
        preSaleQuoteInfoResponse.setSaleServiceCycleTotal(diPreSaleQuote.getSaleServiceCycleTotal());
        preSaleQuoteInfoResponse.setExpecteDate(diPreSaleQuote.getExpecteDate());
        preSaleQuoteInfoResponse.setPreInvoice(String.valueOf(diPreSaleQuote.getPreInvoice()));
        preSaleQuoteInfoResponse.setCountryName(diPreSaleQuote.getCountryName());
        preSaleQuoteInfoResponse.setCountryCode(diPreSaleQuote.getCountryCode());
        preSaleQuoteInfoResponse.setProvinceName(diPreSaleQuote.getProvinceName());
        preSaleQuoteInfoResponse.setProvinceCode(diPreSaleQuote.getProvinceCode());
        preSaleQuoteInfoResponse.setCityName(diPreSaleQuote.getCityName());
        preSaleQuoteInfoResponse.setCityCode(diPreSaleQuote.getCityCode());
        preSaleQuoteInfoResponse.setAreaName(diPreSaleQuote.getAreaName());
        preSaleQuoteInfoResponse.setAreaCode(diPreSaleQuote.getAreaCode());
        preSaleQuoteInfoResponse.setCommonDeliveryAddress(diPreSaleQuote.getCommonDeliveryAddress());
        preSaleQuoteInfoResponse.setBusinessCost(diPreSaleQuote.getBusinessCost());
        preSaleQuoteInfoResponse.setDeliveryReviewFlag(diPreSaleQuote.getDeliveryReviewFlag());
        preSaleQuoteInfoResponse.setIsAdvanceExecution(diPreSaleQuote.getIsAdvanceExecution());

        if (StringUtils.isNotEmpty(diPreSaleQuote.getCostApprovalProcessId())) {
            preSaleQuoteInfoResponse.setCostApprovalProcessId(diPreSaleQuote.getCostApprovalProcessId());
        }
        if (StringUtils.isNotEmpty(diPreSaleQuote.getCamundaTaskId())) {
            preSaleQuoteInfoResponse.setCamundaTaskId(diPreSaleQuote.getCamundaTaskId());
        }

        if (StringUtils.isEmpty(diPreSaleQuote.getCompanyName())) {
            //查询商机
            DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(diPreSaleQuote.getNicheCode());
            if (Objects.nonNull(diMarketingNicheT)) {
                preSaleQuoteInfoResponse.setCompanyName(diMarketingNicheT.getCompanyName());
            }
        }

        //查询报价单明细
        List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, id));


        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleQuoteDetails.stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList()));
        List<Long> tradePreSaleIds = diPreSales.stream().filter(x -> PreSaleTypeEnum.TRADE.getCode().equals(x.getPreSaleType())).map(DiPreSale::getId).collect(Collectors.toList());

        //产品方案map
        Map<String, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));

        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
        //产品方案物料map
        Map<Long, List<DiPreSaleManifest>> manifestMap = diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));

        //物料集合
        List<DiMateriel> materielList = iDiMaterielService.list(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList())));
        Map<Long, DiMateriel> materielMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materielList)) {
            materielMap = materielList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        }

        List<PreSaleQuoteInfoResponse.PreSaleQuoteDetail> preSaleQuoteDetailList = new ArrayList<>();
        BigDecimal costFeeTotal = new BigDecimal(0L);
        BigDecimal projectQuoteTotal = new BigDecimal(0L);
        BigDecimal tradeQuoteTotal = new BigDecimal(0L);
        for (DiPreSaleQuoteDetail diPreSaleQuoteDetail : preSaleQuoteDetails) {
            PreSaleQuoteInfoResponse.PreSaleQuoteDetail preSaleQuoteDetail = new PreSaleQuoteInfoResponse.PreSaleQuoteDetail();
            preSaleQuoteDetail.setPreSaleId(diPreSaleQuoteDetail.getPreSaleId());
            preSaleQuoteDetail.setPreSaleCode(diPreSaleQuoteDetail.getPreSaleCode());
            preSaleQuoteDetail.setNicheCode(diPreSaleQuote.getNicheCode());
            DiPreSale diPreSale = preSaleMap.get(diPreSaleQuoteDetail.getPreSaleCode());
            if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.TRADE) {
                tradeQuoteTotal = tradeQuoteTotal.add(diPreSaleQuoteDetail.getSaleQuote());
            } else {
                projectQuoteTotal = projectQuoteTotal.add(diPreSaleQuoteDetail.getSaleQuote());
            }
            PreSaleDetailResponse.Fee historyFee = diPreSaleService.queryHistoryFeeFirst(diPreSale.getId(), diPreSale.getVersion());
            preSaleQuoteDetail.setPreSaleName(diPreSale.getPreSaleName());
            preSaleQuoteDetail.setOrderType(Integer.valueOf(diPreSale.getOrderType()));
            preSaleQuoteDetail.setPreSaleType(diPreSale.getPreSaleType());
            preSaleQuoteDetail.setId(diPreSaleQuoteDetail.getId());
            //物料
            DiPreSaleManifest diPreSaleManifest = manifestMap.get(diPreSale.getId()).get(0);
            preSaleQuoteDetail.setMaterialCode(diPreSaleManifest.getMaterialCode());
            preSaleQuoteDetail.setProductName(diPreSaleManifest.getProductName());
            DiMateriel materiel = materielMap.get(diPreSaleManifest.getMaterialVersion());
            if (materiel != null) {
                preSaleQuoteDetail.setShowVersionNo(materiel.getShowVersionNo());
                preSaleQuoteDetail.setShowMaterialNo(materiel.getShowMaterielNo());
            }
            preSaleQuoteDetail.setNum(diPreSale.getNum());

            if (materiel != null) {
                DiMaterialStorageVo materialStockInfo = diMaterialStockInfoService.getMaterialStockInfo(String.valueOf(materiel.getId()));
                if (Objects.nonNull(materialStockInfo)) {
                    preSaleQuoteDetail.setStockNum(String.valueOf(materialStockInfo.getMaterialStock()));
                }
            }
            if (StringUtils.isEmpty(preSaleQuoteDetail.getStockNum())) {
                preSaleQuoteDetail.setStockNum("0");
            }
            //PreSaleManifestFee fee = diPreSaleService.queryPreSaleManifestFee(diPreSaleManifest);

            preSaleQuoteDetail.setCostFee(diPreSale.getCostFeeTotal());
            //costFeeTotal = costFeeTotal.add(diPreSaleQuoteDetail.getCostFee());
            if (Objects.equals(2, diPreSaleQuote.getApprovalStatus())
                    && historyFee != null
                    && !PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                preSaleQuoteDetail.setGuideDuration(historyFee.getGuideDuration());
            } else {
                preSaleQuoteDetail.setGuideDuration(diPreSale.getGuideDuration());
            }

//            preSaleQuoteDetail.setCostFeeSum((Objects.nonNull(diPreSale.getCostFeeTotal()) ? diPreSale.getCostFeeTotal() : BigDecimal.ZERO).multiply(Objects.nonNull(diPreSale.getNum()) ? new BigDecimal(diPreSale.getNum()) : BigDecimal.ZERO));
            preSaleQuoteDetail.setCostFeeSum(diPreSaleQuoteDetail.getCostFee());
            preSaleQuoteDetail.setDeliveryQuote(diPreSaleQuoteDetail.getDeliveryQuote());
            preSaleQuoteDetail.setSaleQuote(diPreSaleQuoteDetail.getSaleQuote());
            preSaleQuoteDetail.setDeliveryQuote(diPreSaleQuoteDetail.getDeliveryQuote());
            preSaleQuoteDetail.setGrossProfit(diPreSaleQuoteDetail.getGrossProfit());
            preSaleQuoteDetail.setGrossMargin(diPreSaleQuoteDetail.getGrossMargin());
            preSaleQuoteDetail.setCountry(diPreSaleQuoteDetail.getCountry());
            preSaleQuoteDetail.setCountryCode(diPreSaleQuoteDetail.getCountryCode());
            preSaleQuoteDetail.setProvince(diPreSaleQuoteDetail.getProvince());
            preSaleQuoteDetail.setProvinceCode(diPreSaleQuoteDetail.getProvinceCode());
            preSaleQuoteDetail.setCity(diPreSaleQuoteDetail.getCity());
            preSaleQuoteDetail.setCityCode(diPreSaleQuoteDetail.getCityCode());
            preSaleQuoteDetail.setArea(diPreSaleQuoteDetail.getArea());
            preSaleQuoteDetail.setAreaCode(diPreSaleQuoteDetail.getAreaCode());
            preSaleQuoteDetail.setSaleServiceQuote(diPreSaleQuoteDetail.getSaleServiceQuote());
            preSaleQuoteDetail.setSaleServiceCycle(diPreSaleQuoteDetail.getSaleServiceCycle());
            preSaleQuoteDetail.setSystemServiceCycle(diPreSaleQuoteDetail.getSystemServiceCycle());
            preSaleQuoteDetail.setSystemServiceFee(diPreSaleQuoteDetail.getSystemServiceFee());
            //高亮 交期复核中的方案
            //1. 有复核标签，且未复核
            boolean notDoneSupply = PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(diPreSale.getDeliveryReviewSupplyFlag());
            boolean notDoneDelivery = PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(diPreSale.getDeliveryReviewDeliveryFlag());

            //只有再交期复核中显示高亮   且方案处于交期复核
            if (PreSaleQuoteStatusEnum.DELIVERY_TIME_REVIEW.getCode().equals(diPreSaleQuote.getQuoteStatus())
                    && PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(diPreSale.getPreSaleStatus())

            ) {
                preSaleQuoteDetail.setShowHighLight(true);
                preSaleQuoteDetail.setDeliveryReviewApprover(iDiPreSaleService.ApproverListByPreSaleReview(diPreSale));
            }

            DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                    .eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId())
                    .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
            if (Objects.nonNull(diPreSaleCustomized)) {
                Map<String, String> userNameMap = Maps.newHashMap();
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(com.google.common.collect.Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                if (userListResult.isSuccess()) {
                    userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                    diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                    preSaleQuoteDetail.setTechSupportOwnerCode(diPreSaleCustomized.getTechSupportOwnerCode());
                    preSaleQuoteDetail.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                }
            }
            preSaleQuoteDetailList.add(preSaleQuoteDetail);
        }
        preSaleQuoteInfoResponse.setPreSaleQuoteDetailList(preSaleQuoteDetailList);
        preSaleQuoteInfoResponse.setCostFeeTotal(costFeeTotal);


        BigDecimal calculated = preSaleQuoteService.queryCalculateDeliveryTimeUnitWeek(diPreSales);
        BigDecimal expected = preSaleQuoteService.queryExpectDeliveryTime(id);
        BigDecimal fin = preSaleQuoteService.queryAfterCheckDeliveryTimeUnitWeek(diPreSales);
        //期望交期始终跟着商机，核算交期始终从和
        preSaleQuoteInfoResponse.setExpectedDeliveryWeek(expected);

        if (null != diPreSaleQuote.getExpectationDeliveryWeek()) {
            preSaleQuoteInfoResponse.setExpectationDeliveryWeek(diPreSaleQuote.getExpectationDeliveryWeek());
        } else {
            preSaleQuoteInfoResponse.setExpectationDeliveryWeek(expected.intValue());
        }

        //如果报价单已经通过 则显示落下的值，否则从计算值中获取
        if (Objects.equals(diPreSaleQuote.getApprovalStatus(), 2)) {
            preSaleQuoteInfoResponse.setCalculateDeliveryWeek(diPreSaleQuote.getCalculateDeliveryWeek());
        } else {
            preSaleQuoteInfoResponse.setCalculateDeliveryWeek(calculated);
        }
        /**
         * 复核周交期
         */

        if ((!Objects.equals(diPreSaleQuote.getDeliveryReviewFlag(), 0) && diPreSaleQuote.getDeliveryReviewFlag() != null)) {
            //发生过交期复核 则显示
            if (Objects.equals(diPreSaleQuote.getApprovalStatus(), 2)) {
                preSaleQuoteInfoResponse.setFinallyDeliveryWeek(diPreSaleQuote.getFinallyDeliveryWeek());
            } else {
                preSaleQuoteInfoResponse.setFinallyDeliveryWeek(fin);
            }
        }


        /**
         1. 若复核后交期（周）＞期望交期（周），
         2. 报价单状态不处于已完成，交期复核时
         */
        //默认不显示
        preSaleQuoteInfoResponse.setShowDeliveryReview(false);
        if (!PreSaleQuoteStatusEnum.DELIVERY_TIME_REVIEW.getCode().equals(diPreSaleQuote.getQuoteStatus())
                && !PreSaleQuoteStatusEnum.QUOTED.getCode().equals(diPreSaleQuote.getQuoteStatus())
                && !PreSaleQuoteStatusEnum.GIVE_UP.getCode().equals(diPreSaleQuote.getQuoteStatus())
            //&& !diPreSaleQuote.getIsAdvanceExecution().equals(2) // 提交执行不走交期复核
        ) {
            BigDecimal afterWeek = fin;
            if (afterWeek == null) {
                afterWeek = calculated;
            }
            //未复核过
            if (afterWeek.compareTo(expected) > 0) {
                preSaleQuoteInfoResponse.setShowDeliveryReview(true);
            }

        }


        //账期
        List<DiPreSaleQuotePeriod> diPreSaleQuotePeriods = diPreSaleQuotePeriodMapper.selectList(Wrappers.<DiPreSaleQuotePeriod>lambdaQuery().eq(DiPreSaleQuotePeriod::getDiPreSaleQuoteId, id));
        if (CollectionUtils.isNotEmpty(diPreSaleQuotePeriods)) {

            List<PreSaleQuoteInfoResponse.PreSaleQuotePeriod> preSaleQuotePeriods = diPreSaleQuotePeriods.stream().map(diPreSaleQuotePeriod -> {
                PreSaleQuoteInfoResponse.PreSaleQuotePeriod preSaleQuotePeriod = new PreSaleQuoteInfoResponse.PreSaleQuotePeriod();
                preSaleQuotePeriod.setId(diPreSaleQuotePeriod.getId());
                preSaleQuotePeriod.setBillPeriod(diPreSaleQuotePeriod.getBillPeriod());
                preSaleQuotePeriod.setPayAmount(diPreSaleQuotePeriod.getPayAmount());
                preSaleQuotePeriod.setPayPercent(diPreSaleQuotePeriod.getPayPercent());
                preSaleQuotePeriod.setRemark(diPreSaleQuotePeriod.getRemark());
                preSaleQuotePeriod.setPlanPayDate(diPreSaleQuotePeriod.getPlanPayDate());
                preSaleQuotePeriod.setRate(diPreSaleQuotePeriod.getRate());
                preSaleQuotePeriod.setInvoiceDesc(diPreSaleQuotePeriod.getInvoiceDesc());
                preSaleQuotePeriod.setRealPayDate(diPreSaleQuotePeriod.getRealPayDate());
                return preSaleQuotePeriod;
            }).collect(Collectors.toList());
            preSaleQuoteInfoResponse.setPreSaleQuotePeriods(preSaleQuotePeriods);
        }
        //判断销售报价单状态
        if (Objects.nonNull(preSaleQuoteInfoResponse.getApprovalStatus()) && preSaleQuoteInfoResponse.getApprovalStatus().equals(1)) {
            //审批中状态查询当前审批角色和审批人
            getSaleOfferApprovalInfo(preSaleQuoteInfoResponse);
        }

        preSaleQuoteInfoResponse.setShareList(saleQuoteShareService.queryShareListDTO(preSaleQuoteInfoResponse.getId().longValue()));
        //获取GP1234明细
        List<DiPreSaleQuoteGp> preSaleQuoteGpList = diPreSaleQuoteGpService.list(new LambdaQueryWrapper<DiPreSaleQuoteGp>()
                .eq(DiPreSaleQuoteGp::getPreSaleQuoteId, diPreSaleQuote.getId())
                .eq(DiPreSaleQuoteGp::getPreSaleQuoteCode, diPreSaleQuote.getPreSaleQuoteCode())
                .eq(DiPreSaleQuoteGp::getIsDeleted, 0)
        );
        if (CollectionUtils.isNotEmpty(preSaleQuoteGpList)) {
            DiPreSaleQuoteGp projectGp = preSaleQuoteGpList.stream().filter(x -> PreSaleGpTypeEnum.PROJECT.getCode().equals(x.getGpType())).findFirst().orElse(null);
            DiPreSaleQuoteGp tradeGp = preSaleQuoteGpList.stream().filter(x -> PreSaleGpTypeEnum.TRADE.getCode().equals(x.getGpType())).findFirst().orElse(null);
            DiPreSaleQuoteGp summaryGp = preSaleQuoteGpList.stream().filter(x -> PreSaleGpTypeEnum.SUMMARY.getCode().equals(x.getGpType())).findFirst().orElse(null);

            if (CollectionUtils.isNotEmpty(tradePreSaleIds) && tradeGp == null) {
                tradeGp = summaryGp;
            } else if (CollectionUtils.isEmpty(tradePreSaleIds) && projectGp == null) {
                projectGp = summaryGp;
            }
            preSaleQuoteInfoResponse.setGpDetailResponse4Project(preSaleConverUtil.entityConvertGpDetail(projectGp));
            preSaleQuoteInfoResponse.setGpDetailResponse4Trade(preSaleConverUtil.entityConvertGpDetail(tradeGp));
            preSaleQuoteInfoResponse.setGpDetailResponse(preSaleConverUtil.entityConvertGpDetail(summaryGp));

            DiPreSaleQuoteEstimatingGp estimateGp = diPreSaleQuoteGpService.getByPreSaleQuoteId(summaryGp, diPreSaleQuote.getSaleQuoteTotal());

            preSaleQuoteInfoResponse.setGpEstimating(estimateGp);
            preSaleQuoteInfoResponse.setGpEstimating4Project(diPreSaleQuoteGpService.getByPreSaleQuoteId(projectGp, projectQuoteTotal));
            preSaleQuoteInfoResponse.setGpEstimating4Trade(diPreSaleQuoteGpService.getByPreSaleQuoteId(tradeGp, tradeQuoteTotal));
        }

        //判断状态
        if (diPreSaleQuote.getQuoteStatus().equals(2)) {
            preSaleQuoteInfoResponse.setIsAllowAbandon(false);
        } else {
            //判断是否生成了订单
            List<DiOrder> orderList = diOrderService.list(new LambdaQueryWrapper<DiOrder>().eq(DiOrder::getPreSaleQuoteNo, diPreSaleQuote.getPreSaleQuoteCode()));
            if (CollectionUtils.isNotEmpty(orderList)) {
                preSaleQuoteInfoResponse.setIsAllowAbandon(false);
            }
        }

        return preSaleQuoteInfoResponse;
    }


    /**
     * 获取销售报价单审批角色和审批人信息
     *
     * @param preSaleQuoteInfoResponse 销售报价单返回
     */
    private void getSaleOfferApprovalInfo(PreSaleQuoteInfoResponse preSaleQuoteInfoResponse) {
        if (StringUtils.isEmpty(preSaleQuoteInfoResponse.getCostApprovalProcessId())) {
            log.error("PreSaleQuoteService---getSaleOfferApprovalInfo()---销售报价单成本审批流程实例ID为空，销售报价单编码：{}", preSaleQuoteInfoResponse.getPreSaleQuoteCode());
            return;
        }
        //获取待办审批数据
        List<DiAgencyApproval> agencyApprovalList = diAgencyApprovalService.list(new LambdaQueryWrapper<DiAgencyApproval>()
                .eq(DiAgencyApproval::getBusinessKey, preSaleQuoteInfoResponse.getPreSaleQuoteCode())
                .eq(DiAgencyApproval::getIsDeleted, ContractConstants.STR_ZERO)
                .eq(DiAgencyApproval::getCamundaProcessInstanceId, preSaleQuoteInfoResponse.getCostApprovalProcessId()));
        if (CollectionUtil.isEmpty(agencyApprovalList)) {
            log.error("PreSaleQuoteService---getSaleOfferApprovalInfo()---未获取到待办审批数据，BusinessKey：{}，CamundaProcessInstanceId：{}", preSaleQuoteInfoResponse.getPreSaleQuoteCode(), preSaleQuoteInfoResponse.getCostApprovalProcessId());
            return;
        }
        //获取角色信息
        List<String> roleIdList = agencyApprovalList.stream().map(DiAgencyApproval::getLiabilityRoleId).filter(StringUtils::isNotEmpty).distinct().toList();
        if (roleIdList.size() != ContractConstants.ONE) {
            log.error("PreSaleQuoteService---getSaleOfferApprovalInfo()---角色信息不正确，角色id：{}", JSONUtil.toJsonStr(roleIdList));
        }
        SysRole sysRole = remoteRoleService.getRoleInfoById(roleIdList.get(ContractConstants.ZERO));
        if (null != sysRole) {
            preSaleQuoteInfoResponse.setApprovalRole(sysRole.getRoleName());
        } else {
            log.error("PreSaleQuoteService---getSaleOfferApprovalInfo()---获取角色信息失败，角色id：{}", roleIdList.get(ContractConstants.ZERO));
            return;
        }
        //获取员工信息
        List<String> userByList = agencyApprovalList.stream().map(DiAgencyApproval::getLiabilityBy).filter(StringUtils::isNotEmpty).distinct().toList();
        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(userByList).build());
        if (userListResult.isSuccess()) {
            String userByName = userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(","));
            preSaleQuoteInfoResponse.setApprovalBys(userByName);
        } else {
            log.error("PreSaleQuoteService---getSaleOfferApprovalInfo()---获取用户信息失败，用户工号：{}，返回结果：{}", JSONUtil.toJsonStr(userByList), JSONUtil.toJsonStr(userListResult));
        }
    }

    /**
     * 获取详情
     *
     * @param request
     * @return
     */
    public PreSaleQuoteInfoTempResponse infoTemp(PreSaleQuoteInfoTempRequest request) {

        PreSaleQuoteInfoTempResponse preSaleQuoteInfoTempResponse = new PreSaleQuoteInfoTempResponse();

        //查询商机
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(request.getNicheCode());
        if (Objects.nonNull(diMarketingNicheT)) {
            preSaleQuoteInfoTempResponse.setCompanyName(diMarketingNicheT.getCompanyName());
        }

        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(request.getPreSaleIds());
        //diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().in(DiPreSale::getPreSaleCode, request.getPreSaleCodes()));

        boolean isTrade = false;
        if (CollectionUtils.isNotEmpty(diPreSales) && diPreSales.stream().anyMatch(x -> x.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode()))) {
            isTrade = true;
        }
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
        //产品方案物料map
        Map<Long, List<DiPreSaleManifest>> manifestMap = diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));

        //物料集合
        List<DiMateriel> materielList = iDiMaterielService.list(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList())));
        Map<Long, DiMateriel> materielMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materielList)) {
            materielMap = materielList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        }

        //期望交付日期最大
        List<DiPreSale> expecteDates = diPreSales.stream().filter(diPreSale -> Objects.nonNull(diPreSale.getExpecteDate())).sorted(Comparator.comparing(DiPreSale::getExpecteDate).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expecteDates)) {
            preSaleQuoteInfoTempResponse.setExpecteDate(expecteDates.get(0).getExpecteDate());
        }
        //理论成本总计
        BigDecimal costFeeSum = new BigDecimal("0");
        BigDecimal saleServiceCycleTotalT = BigDecimal.ZERO;
        BigDecimal saleServiceQuoteTotal = BigDecimal.ZERO;
        List<PreSaleQuoteInfoTempResponse.PreSaleQuoteDetail> preSaleQuoteDetailList = new ArrayList<>();
        for (DiPreSale diPreSale : diPreSales) {


            PreSaleQuoteInfoTempResponse.PreSaleQuoteDetail preSaleQuoteDetail = new PreSaleQuoteInfoTempResponse.PreSaleQuoteDetail();
            preSaleQuoteDetail.setPreSaleId(diPreSale.getId());
            preSaleQuoteDetail.setPreSaleCode(diPreSale.getPreSaleCode());
            preSaleQuoteDetail.setNicheCode(diPreSale.getNicheCode());
            preSaleQuoteDetail.setPreSaleName(diPreSale.getPreSaleName());
            preSaleQuoteDetail.setOrderType(Integer.valueOf(diPreSale.getOrderType()));
            //取第一个方案的类型，所有的方案类型应用是一样的
            preSaleQuoteDetail.setPreSaleType(diPreSale.getPreSaleType());
            preSaleQuoteInfoTempResponse.setPreSaleType(diPreSale.getPreSaleType());
            //物料

            DiPreSaleManifest diPreSaleManifest = manifestMap.get(diPreSale.getId()).get(0);
            preSaleQuoteDetail.setMaterialCode(diPreSaleManifest.getMaterialCode());
            preSaleQuoteDetail.setProductName(diPreSaleManifest.getProductName());
            preSaleQuoteDetail.setNum(diPreSale.getNum());

            DiMateriel materiel = materielMap.get(diPreSaleManifest.getMaterialVersion());
            if (materiel != null && !PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                preSaleQuoteDetail.setShowVersionNo(materiel.getShowVersionNo());
                preSaleQuoteDetail.setShowMaterialNo(materiel.getShowMaterielNo());
                preSaleQuoteDetail.setMaterialCode(materiel.getMaterielNo());
                DiMaterialStorageVo materialStockInfo = diMaterialStockInfoService.getMaterialStockInfo(String.valueOf(materiel.getId()));
                if (Objects.nonNull(materialStockInfo)) {
                    preSaleQuoteDetail.setStockNum(String.valueOf(materialStockInfo.getMaterialStock()));
                }
            }

            if (StringUtils.isEmpty(preSaleQuoteDetail.getStockNum())) {
                preSaleQuoteDetail.setStockNum("0");
            }

            //PreSaleManifestFee fee = diPreSaleService.queryPreSaleManifestFee(diPreSaleManifest);


            preSaleQuoteDetail.setCostFee(diPreSale.getCostFeeTotal());
            preSaleQuoteDetail.setGuideDuration(diPreSale.getGuideDuration());

//            preSaleQuoteDetail.setCostFeeSum((Objects.nonNull(diPreSale.getCostFeeTotal()) ? diPreSale.getCostFeeTotal() : BigDecimal.ZERO).multiply(Objects.nonNull(diPreSale.getNum()) ? new BigDecimal(diPreSale.getNum()) : BigDecimal.ZERO));
            if (isTrade) {
                preSaleQuoteDetail.setCostFeeSum(diPreSale.getCostFeeTotal());
            } else {
                preSaleQuoteDetail.setCostFeeSum(BigDecimal.ZERO);//没有填地址 初始值 设置为o
            }
            preSaleQuoteDetail.setProvince(diPreSale.getProvince());
            preSaleQuoteDetail.setProvinceCode(diPreSale.getProvinceCode());
            preSaleQuoteDetail.setCityCode(diPreSale.getCityCode());
            preSaleQuoteDetail.setCity(diPreSale.getCity());
            preSaleQuoteDetail.setArea(diPreSale.getArea());
            preSaleQuoteDetail.setAreaCode(diPreSale.getAreaCode());
            preSaleQuoteDetail.setCountry(diPreSale.getCountry());
            preSaleQuoteDetail.setCountryCode(diPreSale.getCountryCode());

            //产品方案物料
            MaterielFeeQueryDTO materielFeeQueryDTOT = new MaterielFeeQueryDTO();
            materielFeeQueryDTOT.setProvince(diPreSale.getProvinceCode());
            materielFeeQueryDTOT.setCity(diPreSale.getCityCode());
            materielFeeQueryDTOT.setArea(diPreSale.getAreaCode());
            materielFeeQueryDTOT.setMaterielId(diPreSaleManifest.getMaterialVersion());
            //物料费用
            MaterielFeeDTO materielFeeDTOT = iDiMaterielService.queryMaterielFee(materielFeeQueryDTOT);
            BigDecimal installServiceDay = null;
            if (Objects.nonNull(materielFeeDTOT)) {
                //单套服务理论周期--天数
                installServiceDay = materielFeeDTOT.getInstallServiceDay();
            }


            //单套理论费用
            BigDecimal systemServiceFee = null;
            if (Objects.nonNull(materielFeeDTOT)) {
                if (Objects.nonNull(materielFeeDTOT.getInstallServicePrice()) && Objects.nonNull(installServiceDay)) {
                    //安装调试人天费用
                    systemServiceFee = materielFeeDTOT.getInstallServicePrice().multiply(installServiceDay);
                }
            }


            if (Objects.nonNull(systemServiceFee)) {
                preSaleQuoteDetail.setSaleServiceQuote(systemServiceFee.multiply(new BigDecimal(diPreSale.getNum())));
            }
            //  5. 新增销售服务周期报价（人/天）字段，文本框，限制数字，默认带入单套服务理论周期（天）*需求套数。
            if (Objects.nonNull(installServiceDay)) {
                preSaleQuoteDetail.setSaleServiceCycle(installServiceDay.multiply(new BigDecimal(diPreSale.getNum())));
            }
            //更新报价单销售服务周期报价成本合计

            String industry = quotationCalcService.toToolsIndustry(diPreSale.getIndustryCategories());

            ToolsDesignCost conf = quotationCalcService.getDesignConf(industry, diPreSale.getDifficultyLevel(),
                    diPreSale.getMechanicalDifficulty(),
                    diPreSale.getElectricalDifficulty(),
                    diPreSale.getProductionDifficulty(),
                    diPreSale.getAfterSalesDifficulty());

            //单套理论服务周期

            if (conf != null) {
                preSaleQuoteDetail.setSystemServiceFee(conf.getAfterSaleRate().multiply(new BigDecimal("1000")));
                preSaleQuoteDetail.setSaleServiceQuote(preSaleQuoteDetail.getSystemServiceFee().multiply(new BigDecimal(diPreSale.getNum())));
                saleServiceQuoteTotal = saleServiceQuoteTotal.add(preSaleQuoteDetail.getSaleServiceQuote());

                preSaleQuoteDetail.setSystemServiceCycle(conf.getAfterSaleRate().setScale(0, RoundingMode.CEILING));
                preSaleQuoteDetail.setSaleServiceCycle(preSaleQuoteDetail.getSystemServiceCycle().multiply(new BigDecimal(diPreSale.getNum())));
                saleServiceCycleTotalT = saleServiceCycleTotalT.add(preSaleQuoteDetail.getSaleServiceCycle());
            }

            DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                    .eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId())
                    .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
            if (Objects.nonNull(diPreSaleCustomized)) {
                Map<String, String> userNameMap = Maps.newHashMap();
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(com.google.common.collect.Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                if (userListResult.isSuccess()) {
                    userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                    diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                    preSaleQuoteDetail.setTechSupportOwnerCode(diPreSaleCustomized.getTechSupportOwnerCode());
                    preSaleQuoteDetail.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                }
            }

            preSaleQuoteDetailList.add(preSaleQuoteDetail);
            costFeeSum = costFeeSum.add((Objects.nonNull(diPreSale.getNum()) ? new BigDecimal(diPreSale.getNum()) : BigDecimal.ZERO)
                    .multiply(Objects.nonNull(diPreSale.getCostFeeTotal()) ? diPreSale.getCostFeeTotal() : BigDecimal.ZERO));
        }

        preSaleQuoteInfoTempResponse.setSaleServiceCycleTotal(saleServiceCycleTotalT);
        preSaleQuoteInfoTempResponse.setSaleServiceQuoteTotal(saleServiceQuoteTotal);
        preSaleQuoteInfoTempResponse.setPreSaleQuoteDetailList(preSaleQuoteDetailList);
        preSaleQuoteInfoTempResponse.setCostFeeTotal(costFeeSum);
        preSaleQuoteInfoTempResponse.setCalculateDeliveryWeek(preSaleQuoteService.queryCalculateDeliveryTimeUnitWeek(diPreSales));
        DiMarketingNicheDemand demand = demandService.selectByNicheId(Long.parseLong(diMarketingNicheT.getId()));
        //从商机获取地址
        if (demand != null && demand.getDeliveryFullAddress() != null) {
            FullAddressDTO fullAddressDTO = demand.getDeliveryFullAddress();
            preSaleQuoteInfoTempResponse.setCountryCode(fullAddressDTO.getCountryCode());
            preSaleQuoteInfoTempResponse.setCountryName(fullAddressDTO.getCountryName());
            preSaleQuoteInfoTempResponse.setProvinceName(fullAddressDTO.getProvinceName());
            preSaleQuoteInfoTempResponse.setProvinceCode(fullAddressDTO.getProvinceCode());
            preSaleQuoteInfoTempResponse.setCityName(fullAddressDTO.getCityName());
            preSaleQuoteInfoTempResponse.setCityCode(fullAddressDTO.getCityCode());
            preSaleQuoteInfoTempResponse.setAreaName(fullAddressDTO.getAreaName());
            preSaleQuoteInfoTempResponse.setAreaCode(fullAddressDTO.getAreaCode());
            preSaleQuoteInfoTempResponse.setCommonDeliveryAddress(fullAddressDTO.getAddress());

        }

        preSaleQuoteInfoTempResponse.setExpectationDeliveryWeek(diMarketingNicheT.getExpectationDeliveryWeek() == null ? 1 : diMarketingNicheT.getExpectationDeliveryWeek());

        return preSaleQuoteInfoTempResponse;
    }


    /**
     * 发起报价
     *
     * @param request
     * @return
     */
    @Transactional
    public R<String> lockQuote(PreSaleQuoteLockRequest request) {

//        if (Objects.isNull(request.getExpecteDate())) {
//            throw new RuntimeException("期望交付日期不能为空");
//        }
//
//        //交期报价(天)
//        BigDecimal deliveryQuote = request.getPreSaleQuoteDetailList().stream()
//                .filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getDeliveryQuote()))
//                .sorted(Comparator.comparing(PreSaleQuoteLockRequest.PreSaleQuoteAddDetail::getDeliveryQuote).reversed())
//                .collect(Collectors.toList()).get(0).getDeliveryQuote();
//
//        if (Objects.nonNull(deliveryQuote) && request.getExpecteDate().plusDays(deliveryQuote.multiply(BigDecimal.ONE).intValue())
//                .isBefore(LocalDate.now())) {
//            throw new RuntimeException("订单交付日期早于产品交期报价");
//        }
        if (Objects.isNull(request.getId())) {

            PreSaleQuoteAddRequest preSaleQuoteAddRequest = new PreSaleQuoteAddRequest();
            preSaleQuoteAddRequest.setNicheCode(request.getNicheCode());
            preSaleQuoteAddRequest.setContactsName(request.getContactsName());
            preSaleQuoteAddRequest.setContactsPhone(request.getContactsPhone());
            preSaleQuoteAddRequest.setExpecteDate(request.getExpecteDate());
            preSaleQuoteAddRequest.setPreInvoice(request.getPreInvoice());
            List<PreSaleQuoteAddRequest.PreSaleQuoteAddDetail> addDetails = request.getPreSaleQuoteDetailList().stream().map(preSaleQuoteAddDetail -> {
                PreSaleQuoteAddRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetailT = new PreSaleQuoteAddRequest.PreSaleQuoteAddDetail();
                preSaleQuoteAddDetailT.setPreSaleId(preSaleQuoteAddDetail.getPreSaleId());
                preSaleQuoteAddDetailT.setPreSaleCode(preSaleQuoteAddDetail.getPreSaleCode());
                preSaleQuoteAddDetailT.setSaleQuote(preSaleQuoteAddDetail.getSaleQuote());
                preSaleQuoteAddDetailT.setDeliveryQuote(preSaleQuoteAddDetail.getDeliveryQuote());

                preSaleQuoteAddDetailT.setProvince(preSaleQuoteAddDetail.getProvince());
                preSaleQuoteAddDetailT.setProvinceCode(preSaleQuoteAddDetail.getProvinceCode());
                preSaleQuoteAddDetailT.setCity(preSaleQuoteAddDetail.getCity());
                preSaleQuoteAddDetailT.setCityCode(preSaleQuoteAddDetail.getCityCode());
                preSaleQuoteAddDetailT.setArea(preSaleQuoteAddDetail.getArea());
                preSaleQuoteAddDetailT.setAreaCode(preSaleQuoteAddDetail.getAreaCode());
                preSaleQuoteAddDetailT.setSaleServiceQuote(preSaleQuoteAddDetail.getSaleServiceQuote());
                preSaleQuoteAddDetailT.setSaleServiceCycle(preSaleQuoteAddDetail.getSaleServiceCycle());
                return preSaleQuoteAddDetailT;
            }).collect(Collectors.toList());
            preSaleQuoteAddRequest.setPreSaleQuoteDetailList(addDetails);

            //账期
            List<PreSaleQuoteAddRequest.PreSaleQuotePeriod> preSaleQuotePeriods = request.getPreSaleQuotePeriods();
            if (CollectionUtils.isNotEmpty(preSaleQuotePeriods)) {

                List<PreSaleQuoteAddRequest.PreSaleQuotePeriod> preSaleQuotePeriodList = preSaleQuotePeriods.stream().map(preSaleQuotePeriod -> {
                    PreSaleQuoteAddRequest.PreSaleQuotePeriod preSaleQuotePeriodAdd = new PreSaleQuoteAddRequest.PreSaleQuotePeriod();
                    preSaleQuotePeriodAdd.setBillPeriod(preSaleQuotePeriod.getBillPeriod());
                    preSaleQuotePeriodAdd.setPayAmount(preSaleQuotePeriod.getPayAmount());
                    preSaleQuotePeriodAdd.setPayPercent(preSaleQuotePeriod.getPayPercent());
                    preSaleQuotePeriodAdd.setRemark(preSaleQuotePeriod.getRemark());
                    preSaleQuotePeriodAdd.setPlanPayDate(preSaleQuotePeriod.getPlanPayDate());
                    preSaleQuotePeriodAdd.setRate(preSaleQuotePeriod.getRate());
                    preSaleQuotePeriodAdd.setInvoiceDesc(preSaleQuotePeriod.getInvoiceDesc());
                    preSaleQuotePeriodAdd.setRealPayDate(preSaleQuotePeriod.getRealPayDate());
                    return preSaleQuotePeriodAdd;
                }).collect(Collectors.toList());
                preSaleQuoteAddRequest.setPreSaleQuotePeriods(preSaleQuotePeriodList);
            }
            String code = add(preSaleQuoteAddRequest);
            DiPreSaleQuote diPreSaleQuote = diPreSaleQuoteMapper.selectOne(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getPreSaleQuoteCode, code));
            request.setId(diPreSaleQuote.getId());

/*
            PreSaleQuoteEditRequest preSaleQuoteEditRequest = new PreSaleQuoteEditRequest();
            preSaleQuoteEditRequest.setId(request.getId());
            preSaleQuoteEditRequest.setContactsName(request.getContactsName());
            preSaleQuoteEditRequest.setContactsPhone(request.getContactsPhone());

            List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, request.getId()));
            if(CollectionUtils.isNotEmpty(preSaleQuoteDetails)){
                List<PreSaleQuoteEditRequest.PreSaleQuoteEditDetail> preSaleQuoteEditDetails = preSaleQuoteDetails.stream().map(preSaleQuoteAddDetail -> {
                    PreSaleQuoteEditRequest.PreSaleQuoteEditDetail preSaleQuoteEditDetail = new PreSaleQuoteEditRequest.PreSaleQuoteEditDetail();
                    preSaleQuoteEditDetail.setId(preSaleQuoteAddDetail.getId());
                    preSaleQuoteEditDetail.setSaleQuote(preSaleQuoteAddDetail.getSaleQuote());
                    preSaleQuoteEditDetail.setDeliveryQuote(preSaleQuoteAddDetail.getDeliveryQuote());
                    preSaleQuoteEditDetail.setProvince(preSaleQuoteAddDetail.getProvince());
                    preSaleQuoteEditDetail.setProvinceCode(preSaleQuoteAddDetail.getProvinceCode());
                    preSaleQuoteEditDetail.setCity(preSaleQuoteAddDetail.getCity());
                    preSaleQuoteEditDetail.setCityCode(preSaleQuoteAddDetail.getCityCode());
                    preSaleQuoteEditDetail.setArea(preSaleQuoteAddDetail.getArea());
                    preSaleQuoteEditDetail.setAreaCode(preSaleQuoteAddDetail.getAreaCode());
                    preSaleQuoteEditDetail.setSaleServiceQuote(preSaleQuoteAddDetail.getSaleServiceQuote());
                    preSaleQuoteEditDetail.setSaleServiceCycle(preSaleQuoteAddDetail.getSaleServiceCycle());
                    return preSaleQuoteEditDetail;
                }).collect(Collectors.toList());
                preSaleQuoteEditRequest.setPreSaleQuoteDetailList(preSaleQuoteEditDetails);
            }

            edit( preSaleQuoteEditRequest);*/
            DiPreSaleQuote diPreSaleQuoteT = new DiPreSaleQuote();
            diPreSaleQuoteT.setId(request.getId());
            diPreSaleQuoteT.setQuoteStatus(1);
            diPreSaleQuoteMapper.updateById(diPreSaleQuoteT);
            return R.ok(code);
        } else {
            PreSaleQuoteEditRequest preSaleQuoteEditRequest = new PreSaleQuoteEditRequest();
            preSaleQuoteEditRequest.setId(request.getId());
            preSaleQuoteEditRequest.setContactsName(request.getContactsName());
            preSaleQuoteEditRequest.setContactsPhone(request.getContactsPhone());
            preSaleQuoteEditRequest.setPreInvoice(request.getPreInvoice());
            if (CollectionUtils.isNotEmpty(request.getPreSaleQuoteDetailList())) {
                List<PreSaleQuoteEditRequest.PreSaleQuoteEditDetail> preSaleQuoteEditDetails = request.getPreSaleQuoteDetailList().stream().map(preSaleQuoteAddDetail -> {
                    PreSaleQuoteEditRequest.PreSaleQuoteEditDetail preSaleQuoteEditDetail = new PreSaleQuoteEditRequest.PreSaleQuoteEditDetail();
                    preSaleQuoteEditDetail.setId(preSaleQuoteAddDetail.getId());
                    preSaleQuoteEditDetail.setSaleQuote(preSaleQuoteAddDetail.getSaleQuote());
                    preSaleQuoteEditDetail.setDeliveryQuote(preSaleQuoteAddDetail.getDeliveryQuote());
                    preSaleQuoteEditDetail.setProvince(preSaleQuoteAddDetail.getProvince());
                    preSaleQuoteEditDetail.setProvinceCode(preSaleQuoteAddDetail.getProvinceCode());
                    preSaleQuoteEditDetail.setCity(preSaleQuoteAddDetail.getCity());
                    preSaleQuoteEditDetail.setCityCode(preSaleQuoteAddDetail.getCityCode());
                    preSaleQuoteEditDetail.setArea(preSaleQuoteAddDetail.getArea());
                    preSaleQuoteEditDetail.setAreaCode(preSaleQuoteAddDetail.getAreaCode());
                    preSaleQuoteEditDetail.setSaleServiceQuote(preSaleQuoteAddDetail.getSaleServiceQuote());
                    preSaleQuoteEditDetail.setSaleServiceCycle(preSaleQuoteAddDetail.getSaleServiceCycle());

                    return preSaleQuoteEditDetail;
                }).collect(Collectors.toList());
                preSaleQuoteEditRequest.setPreSaleQuoteDetailList(preSaleQuoteEditDetails);
            }

            //账期
            List<PreSaleQuoteAddRequest.PreSaleQuotePeriod> preSaleQuotePeriods = request.getPreSaleQuotePeriods();
            if (CollectionUtils.isNotEmpty(preSaleQuotePeriods)) {

                List<PreSaleQuoteEditRequest.PreSaleQuotePeriod> preSaleQuotePeriodList = preSaleQuotePeriods.stream().map(preSaleQuotePeriod -> {
                    PreSaleQuoteEditRequest.PreSaleQuotePeriod preSaleQuotePeriodEdit = new PreSaleQuoteEditRequest.PreSaleQuotePeriod();
                    preSaleQuotePeriodEdit.setBillPeriod(preSaleQuotePeriod.getBillPeriod());
                    preSaleQuotePeriodEdit.setPayAmount(preSaleQuotePeriod.getPayAmount());
                    preSaleQuotePeriodEdit.setPayPercent(preSaleQuotePeriod.getPayPercent());
                    preSaleQuotePeriodEdit.setRemark(preSaleQuotePeriod.getRemark());
                    preSaleQuotePeriodEdit.setPlanPayDate(preSaleQuotePeriod.getPlanPayDate());
                    preSaleQuotePeriodEdit.setRate(preSaleQuotePeriod.getRate());
                    preSaleQuotePeriodEdit.setInvoiceDesc(preSaleQuotePeriod.getInvoiceDesc());
                    preSaleQuotePeriodEdit.setRealPayDate(preSaleQuotePeriod.getRealPayDate());
                    return preSaleQuotePeriodEdit;
                }).collect(Collectors.toList());
                preSaleQuoteEditRequest.setPreSaleQuotePeriods(preSaleQuotePeriodList);
            }

            edit(preSaleQuoteEditRequest);
            DiPreSaleQuote diPreSaleQuote = new DiPreSaleQuote();
            diPreSaleQuote.setId(request.getId());
            diPreSaleQuote.setQuoteStatus(1);
            diPreSaleQuoteMapper.updateById(diPreSaleQuote);
        }

        return R.ok();
    }
//
//    private R updateLockQuote(PreSaleQuoteLockRequest request) {
//        List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, request.getId()));
//        if (CollectionUtils.isEmpty(preSaleQuoteDetails)) {
//            return R.fail("无效id");
//        }
//
//        for (DiPreSaleQuoteDetail diPreSaleQuoteDetail : preSaleQuoteDetails) {
//            if (Objects.isNull(diPreSaleQuoteDetail.getSaleQuote()) || Objects.isNull(diPreSaleQuoteDetail.getDeliveryQuote())) {
//                return R.fail("您尚有方案未报价，请确认后再试");
//            }
//        }
//
//        DiPreSaleQuote diPreSaleQuoteT = diPreSaleQuoteMapper.selectById(request.getId());
//
//        DiPreSaleQuote diPreSaleQuote = new DiPreSaleQuote();
//        diPreSaleQuote.setQuoteStatus(1);
//        diPreSaleQuote.setId(request.getId());
//        diPreSaleQuote.setContactsName(request.getContactsName());
//        diPreSaleQuote.setContactsPhone(request.getContactsPhone());
//        //产品方案
//        diPreSaleQuote.setSaleQuoteTotal(request.getPreSaleQuoteDetailList().stream().filter(preSaleQuoteAddDetail -> Objects.nonNull(preSaleQuoteAddDetail.getSaleQuote())).map(PreSaleQuoteLockRequest.PreSaleQuoteAddDetail::getSaleQuote).reduce(BigDecimal.ZERO, BigDecimal::add));
//        diPreSaleQuote.setGrossProfitTotal(diPreSaleQuote.getSaleQuoteTotal().subtract(diPreSaleQuoteT.getCostFeeTotal()));
//        if (Objects.nonNull(diPreSaleQuote.getGrossProfitTotal()) && Objects.nonNull(diPreSaleQuoteT.getCostFeeTotal()) && BigDecimal.ZERO.compareTo(diPreSaleQuoteT.getCostFeeTotal()) != 0) {
//            diPreSaleQuote.setGrossMarginTotal(diPreSaleQuote.getGrossProfitTotal().divide(diPreSaleQuoteT.getCostFeeTotal(), 2, RoundingMode.HALF_UP));
//        }
//        diPreSaleQuoteMapper.updateById(diPreSaleQuote);
//
//        if (CollectionUtils.isNotEmpty(request.getPreSaleQuoteDetailList())) {
//            for (PreSaleQuoteLockRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetail : request.getPreSaleQuoteDetailList()) {
//                DiPreSaleQuoteDetail diPreSaleQuoteDetail = new DiPreSaleQuoteDetail();
//                diPreSaleQuoteDetail.setId(preSaleQuoteAddDetail.getId());
//                diPreSaleQuoteDetail.setSaleQuote(preSaleQuoteAddDetail.getSaleQuote());
//                diPreSaleQuoteDetail.setDeliveryQuote(preSaleQuoteAddDetail.getDeliveryQuote());
//                diPreSaleQuoteDetailMapper.updateById(diPreSaleQuoteDetail);
//            }
//        }
//        return R.ok();
//    }

    /**
     * 根据销售报价单ID获取报价单对应的产品物料相关费用(合同生成时使用)
     *
     * @param preSaleQuoteNo 销售报价单编号
     * @return 费用信息
     */
    public PreSaleQuoteProductCostInfoVO getQuoteProductCostInfoByNo(String preSaleQuoteNo, Integer... notNeedFee) {
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, preSaleQuoteNo));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        //查询销售报价单明细数据
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, preSaleQuote.getId()));
        if (CollectionUtils.isEmpty(preSaleQuoteDetailList)) {
            throw new ServiceException("销售报价单数据不正确");
        }
        //查询报价单明细绑定的产品方案数据
        List<Long> preSaleIdsList = preSaleQuoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).distinct().toList();
        List<DiPreSale> preSaleList = diPreSaleService.queryDiPreSaleByIds(preSaleIdsList);
        //diPreSaleMapper.selectList(new LambdaQueryWrapper<DiPreSale>().in(DiPreSale::getPreSaleCode, preSaleCodeList));
        if (CollectionUtils.isEmpty(preSaleList) || preSaleIdsList.size() != preSaleList.size()) {
            throw new ServiceException("销售报价单对应的产品方案数据不正确");
        }
        //根据产品方案获取方案清单数据
        List<Long> preSaleIdList = preSaleList.stream().map(DiPreSale::getId).toList();
        List<DiPreSaleManifest> preSaleManifestList = diPreSaleManifestMapper.selectList(new LambdaQueryWrapper<DiPreSaleManifest>().in(DiPreSaleManifest::getDiPreSaleId, preSaleIdList));
        if (CollectionUtils.isEmpty(preSaleManifestList)) {
            throw new ServiceException("产品清单不存在");
        }
        //获取物料信息
        List<Long> materielIds = preSaleManifestList.stream().map(DiPreSaleManifest::getMaterialVersion).toList();
        List<DiMateriel> materielList = iDiMaterielService.queryMaterielByIds(materielIds);
        if (CollectionUtils.isEmpty(materielList)) {
            throw new ServiceException("物料信息不正确");
        }
        //获取商机信息
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(preSaleQuote.getNicheCode());
        if (null == diMarketingNicheT) {
            throw new ServiceException("商机不存在");
        }
        DiMarketingCustomer marketingCustomer = null;
        if (StringUtils.isNotEmpty(diMarketingNicheT.getCustomerNo())) {
            //获取客户信息
            marketingCustomer = diMarketingCustomerService.getOne(new LambdaQueryWrapper<DiMarketingCustomer>().eq(DiMarketingCustomer::getCustomerNo, diMarketingNicheT.getCustomerNo()));
        }
        List<Long> materielIdList = materielList.stream().map(DiMateriel::getId).toList();
        //获取物料指导工期数据
        List<DiMaterielPrice> materielPriceList = diMaterielPriceService.list(new LambdaQueryWrapper<DiMaterielPrice>().in(DiMaterielPrice::getMaterielId, materielIdList));
        Map<Long, DiMaterielPrice> priceMap;
        if (CollectionUtils.isNotEmpty(materielPriceList)) {
            priceMap = materielPriceList.stream().collect(Collectors.toMap(DiMaterielPrice::getMaterielId, Function.identity()));
        } else {
            priceMap = null;
        }
        //根据产品方案获取图纸信息
        List<DiPreSaleUrl> preSaleUrlList = diPreSaleUrlMapper.selectList(new LambdaQueryWrapper<DiPreSaleUrl>()
                        .in(DiPreSaleUrl::getDiPreSaleId, preSaleIdList)
//                .eq(DiPreSaleUrl::getIsPic, 1)
        );
        Map<Long, List<DiPreSaleUrl>> preSaleFileMap = preSaleUrlList.stream().collect(Collectors.groupingBy(DiPreSaleUrl::getDiPreSaleId));
        Map<Long, List<DiPreSaleUrl>> preSaleUrlMap;
        if (CollectionUtil.isNotEmpty(preSaleUrlList)) {
            preSaleUrlMap = preSaleUrlList.stream()
//                    .filter(p -> p != null && null != p.getIsPic() && p.getIsPic() == 1)
                    .collect(Collectors.groupingBy(DiPreSaleUrl::getDiPreSaleId));
        } else {
            preSaleUrlMap = null;
        }
        //根据方案清单数据获取参数配置信息
        List<Integer> preSaleManifestIdList = preSaleManifestList.stream().map(DiPreSaleManifest::getId).toList();
        List<DiPreSaleLabel> preSaleLabelList = diPreSaleLabelMapper.selectList(new LambdaQueryWrapper<DiPreSaleLabel>().in(DiPreSaleLabel::getPreSaleManifestId, preSaleManifestIdList));
        Map<Integer, List<DiPreSaleLabel>> preSaleLabelMap;
        if (CollectionUtils.isNotEmpty(preSaleLabelList)) {
            preSaleLabelMap = preSaleLabelList.stream().collect(Collectors.groupingBy(DiPreSaleLabel::getPreSaleManifestId));
        } else {
            preSaleLabelMap = null;
        }
        //获取物料库存
        Map<Long, DiMaterialStockInfo> stockInfoMap = commonService.getMaterialStockMapByIds(materielIdList);
        //产品方案数据转为Map
        Map<String, DiPreSale> preSaleMap = preSaleList.stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));
        //方案清单数据转为Map
        Map<Long, List<DiPreSaleManifest>> preSaleManifestMap = preSaleManifestList.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));
        //将物料数据转为Map
        Map<Long, DiMateriel> materielMap = materielList.stream().collect(Collectors.toMap(DiMateriel::getId, Function.identity()));
        //返回VO
        PreSaleQuoteProductCostInfoVO infoVo = new PreSaleQuoteProductCostInfoVO();
        //构建客户信息
        if (null != marketingCustomer) {
            if (StringUtils.isNotEmpty(marketingCustomer.getCompanyName())) {
                infoVo.setFirstPartyName(marketingCustomer.getCompanyName());
            }
            if (StringUtils.isNotEmpty(marketingCustomer.getCompanyTaxNumber())) {
                infoVo.setFirstPartyTaxNumber(marketingCustomer.getCompanyTaxNumber());
            }
        }
        //构建报价信息
        infoVo.setPreSaleQuoteId(preSaleQuote.getId().longValue());
        infoVo.setPreSaleQuoteNo(preSaleQuote.getPreSaleQuoteCode());
        //2024-09-24,产品方案那里修改了，这里同步修改(方法来源：PreSaleQuoteService---info())
//        infoVo.setCostFeeTotal(preSaleQuote.getCostFeeTotal());
        infoVo.setSaleQuoteTotal(preSaleQuote.getSaleQuoteTotal());
        infoVo.setGrossProfitTotal(preSaleQuote.getGrossProfitTotal());
        infoVo.setGrossMarginTotal(preSaleQuote.getGrossMarginTotal());
        infoVo.setPreInvoice(preSaleQuote.getPreInvoice());
        if (null != preSaleQuote.getFinallyDeliveryWeek()) {
            infoVo.setContractDeliveryCycleWeek(preSaleQuote.getFinallyDeliveryWeek());
        } else {
            infoVo.setContractDeliveryCycleWeek(new BigDecimal("0"));
        }
        if (null != preSaleQuote.getSaleServiceCycleTotal()) {
            infoVo.setSaleServiceCycleTotal(preSaleQuote.getSaleServiceCycleTotal());
        } else {
            infoVo.setSaleServiceCycleTotal(new BigDecimal(0));
        }
        if (null != preSaleQuote.getSaleServiceQuoteTotal()) {
            infoVo.setSaleServiceQuoteTotal(preSaleQuote.getSaleServiceQuoteTotal());
        } else {
            infoVo.setSaleServiceQuoteTotal(new BigDecimal(0));
        }
        infoVo.setExpecteDate(preSaleQuote.getExpecteDate());
        if (StringUtils.isNotEmpty(diMarketingNicheT.getOrderType())) {
            infoVo.setOrderType(diMarketingNicheT.getOrderType());
        }
        Set<Long> tradePreSaleIdList = preSaleList.stream().filter(p -> p.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode()))
                .map(x -> x.getId()).collect(Collectors.toSet());
        //将GP1234返回
        DiPreSaleQuoteGpGroup preSaleQuoteGpList = diPreSaleQuoteGpService.queryByQuoteId(Long.valueOf(preSaleQuote.getId()), CollectionUtils.isNotEmpty(tradePreSaleIdList));

        if (preSaleQuoteGpList != null) {
            infoVo.setGpEstimating(preSaleQuoteGpList.getSummaryEstimating());
            infoVo.setGpDetailResponse(preSaleConverUtil.entityConvertGpDetail(preSaleQuoteGpList.getSummary()));
            infoVo.setGpEstimating4Project(preSaleQuoteGpList.getProjectEstimating());
            infoVo.setGpDetailResponse4Project(preSaleConverUtil.entityConvertGpDetail(preSaleQuoteGpList.getProject()));
            infoVo.setGpEstimating4Trade(preSaleQuoteGpList.getTradeEstimating());
            infoVo.setGpDetailResponse4Trade(preSaleConverUtil.entityConvertGpDetail(preSaleQuoteGpList.getTrade()));

        }
        //计算产品理论成本合计
        BigDecimal costFeeTotal = new BigDecimal(0L);
        //构建产品方案数据
        List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> detailedList = new ArrayList<>();

        //贸易类 简单处理移除 多个物料相关的信息
        List<DiPreSaleQuoteDetail> tradePresaleQuoteDetailList = preSaleQuoteDetailList.stream().filter(x -> tradePreSaleIdList.contains(x.getPreSaleId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tradePresaleQuoteDetailList)) {
            tradePresaleQuoteProductCostInfoVO(tradePresaleQuoteDetailList, preSaleMap,
                    detailedList, preSaleUrlMap, preSaleManifestMap, notNeedFee
            );
            costFeeTotal.add(detailedList.stream().map(x -> x.getCostFeeSum()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        List<DiPreSaleQuoteDetail> projectPresaleQuoteDetailList = preSaleQuoteDetailList.stream().filter(x -> !tradePreSaleIdList.contains(x.getPreSaleId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(projectPresaleQuoteDetailList)) {
            for (DiPreSaleQuoteDetail preSaleQuoteDetail : projectPresaleQuoteDetailList) {
                PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed detailed = new PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed();
                //获取产品方案数据并构建需要返回的产品数据
                DiPreSale preSale = preSaleMap.get(preSaleQuoteDetail.getPreSaleCode());
                detailed.setId(preSale.getId());
                detailed.setNicheId(preSale.getNicheId().longValue());
                detailed.setNicheNo(preSale.getNicheCode());
                detailed.setPreSaleId(preSale.getId());
                detailed.setPreSaleCode(preSale.getPreSaleCode());
                detailed.setPreSaleName(preSale.getPreSaleName());
                detailed.setOrderType(preSale.getOrderType());
                detailed.setPreSaleType(preSale.getPreSaleType());
                if (null != preSale.getNum()) {
                    detailed.setDemandNum(preSale.getNum());
                } else {
                    detailed.setDemandNum(1);
                }
                if (null != preSale.getExpecteDate()) {
                    detailed.setExpecteDate(DateUtils.format(preSale.getExpecteDate(), "yyyy-MM-dd"));
                }
                detailed.setPackageType(new BigDecimal(preSale.getPackageType()));
                if (null != preSale.getInstallationFee()) {
                    detailed.setInstallationFee(preSale.getInstallationFee());
                } else {
                    detailed.setInstallationFee(new BigDecimal(0));
                }
                //获取产品清单数据(由于目前是一个方案对应一个物料并且物料没有做版本，所以现在方案跟清单的数据是一对一的，直接取就行了)并构建需要返回的产品数据
                DiPreSaleManifestWrapper preSaleManifest = new DiPreSaleManifestWrapper(preSaleManifestMap.get(preSale.getId()).get(0), PreSaleTypeEnum.of(preSale.getPreSaleType()));
                PreSaleDetailResponse.Fee hostoryFee = iDiPreSaleService.queryHistoryFeeFirst(preSale.getId(), preSale.getVersion());
                //复制产品方案那里的方法，循环调数据库(方法来源：PreSaleQuoteService---info())
                if (CollectionUtils.isEmpty(Arrays.asList(notNeedFee))) {
                    //PreSaleManifestFee fee = diPreSaleService.queryPreSaleManifestFee(preSaleManifest);
                    costFeeTotal = costFeeTotal.add(preSale.getCostFeeTotal().multiply(new BigDecimal(preSale.getNum())));
                    //这里是从亚杰那边复制过来的---2024-09-24，产品方案修改了，与产品方案保持一致

                    detailed.setCostFee(preSale.getCostFeeTotal());
                    detailed.setCostFeeSum(preSale.getCostFeeTotal().multiply(new BigDecimal(preSale.getNum())));

                    //获取单套理论交期---2024-09-24,产品方案改了，保持一致
                    if (preSale.getGuideDuration() != null) {
                        detailed.setGuideDuration(String.valueOf(preSale.getGuideDuration()));
                    } else {
                        detailed.setGuideDuration("0");
                    }

                    if (hostoryFee != null) {
                        detailed.setGuideDuration(String.valueOf(hostoryFee.getGuideDuration()));
                    }
                }

//            DiMaterielVersion materielVersion = diMaterielVersionService.getById(preSaleManifest.getMaterialVersion());
//            Optional.ofNullable(materielVersion).ifPresent(v -> {
//                if (Objects.nonNull(v.getBomStatus())) {
//                    detailed.setShowVersionNo(getShowVersionNo(v.getBomStatus(),v.getVersionNo()));
//                }
//            });

                detailed.setMechanicalDesignDuration(preSaleManifest.getMechanicalDesignDuration());
                detailed.setElectricalDesignPeriod(preSaleManifest.getElectricalDesignPeriod());
                detailed.setTechSupportDay(preSaleManifest.getTechSupportDay());
                detailed.setFeeTotal(preSaleManifest.getFeeTotal());
                //detailed.setRdDay(preSaleManifest.getRdDay());
                MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
                materielFeeQueryDTO.setMaterielId(preSaleManifest.getMaterialVersion());

                detailed.setSupplyDay(preSaleManifest.getSupplyDay());
                detailed.setProduceDay(preSaleManifest.getProduceDay());

                //detailed.setImplementDay(preSaleManifest.getImplementDay());
//            detailed.setCostFeeSum(detailed.getCostFee().multiply(new BigDecimal(detailed.getDemandNum())));
                //获取物料信息
                DiMateriel materiel = materielMap.get(preSaleManifest.getMaterialVersion());
                detailed.setMaterialNo(materiel.getMaterielNo());
                detailed.setMaterialName(materiel.getMaterielName());
                detailed.setVersionNo(materiel.getVersionNo());
                detailed.setShowVersionNo(getShowVersionNo(materiel.getVersionStatus(), materiel.getVersionNo()));
                //获取物料库存
                if (CollectionUtils.isNotEmpty(stockInfoMap) && stockInfoMap.containsKey(materiel.getId())) {
                    detailed.setMaterialStock(stockInfoMap.get(materiel.getId()).getMaterialStock());
                } else {
                    detailed.setMaterialStock(0);
                }

                //获取物料标准成本和合计成本
                if (CollectionUtils.isNotEmpty(priceMap) && priceMap.containsKey(materiel.getId())) {
                    if (null != priceMap.get(materiel.getId()).getGuideSumCosts()) {
                        detailed.setGuideSumCosts(priceMap.get(materiel.getId()).getGuideSumCosts());
                    } else {
                        detailed.setGuideSumCosts(new BigDecimal(0));
                    }
                    if (null != priceMap.get(materiel.getId()).getCostSumCosts()) {
                        detailed.setCostSumCosts(priceMap.get(materiel.getId()).getCostSumCosts());
                    } else {
                        detailed.setCostSumCosts(new BigDecimal(0));
                    }
                } else {
                    detailed.setGuideSumCosts(new BigDecimal(0));
                    detailed.setCostSumCosts(new BigDecimal(0));
                }
                //构建报价明细返回数据
                detailed.setSaleQuote(preSaleQuoteDetail.getSaleQuote());
                detailed.setDeliveryQuote(preSaleQuoteDetail.getDeliveryQuote());
                detailed.setGrossProfit(preSaleQuoteDetail.getGrossProfit());
                if (Objects.isNull(preSaleQuoteDetail.getGrossMargin())) {
                    detailed.setGrossMargin(new BigDecimal(0));
                } else {
                    detailed.setGrossMargin(preSaleQuoteDetail.getGrossMargin());
                }
                detailed.setCountryCode(preSaleQuoteDetail.getCountryCode());
                detailed.setCountry(preSaleQuoteDetail.getCountry());
                detailed.setProvinceCode(preSaleQuoteDetail.getProvinceCode());
                detailed.setProvince(preSaleQuoteDetail.getProvince());
                detailed.setCityCode(preSaleQuoteDetail.getCityCode());
                detailed.setCity(preSaleQuoteDetail.getCity());
                detailed.setAreaCode(preSaleQuoteDetail.getAreaCode());
                detailed.setArea(preSaleQuoteDetail.getArea());
                if (null != preSaleQuoteDetail.getSystemServiceCycle()) {
                    detailed.setSystemServiceCycle(preSaleQuoteDetail.getSystemServiceCycle());
                } else {
                    detailed.setSystemServiceCycle(new BigDecimal("0"));
                }
                if (null != preSaleQuoteDetail.getSystemServiceFee()) {
                    detailed.setSystemServiceFee(preSaleQuoteDetail.getSystemServiceFee());
                } else {
                    detailed.setSystemServiceFee(new BigDecimal("0"));
                }
                if (null != preSaleQuoteDetail.getSaleServiceQuote()) {
                    detailed.setSaleServiceQuote(preSaleQuoteDetail.getSaleServiceQuote());
                } else {
                    detailed.setSaleServiceQuote(new BigDecimal("0"));
                }
                if (null != preSaleQuoteDetail.getSaleServiceCycle()) {
                    detailed.setSaleServiceCycle(preSaleQuoteDetail.getSaleServiceCycle());
                } else {
                    detailed.setSaleServiceCycle(new BigDecimal("0"));
                }
                //构建产品方案图纸数据
                if (CollectionUtil.isNotEmpty(preSaleUrlMap) && preSaleUrlMap.containsKey(preSale.getId())) {
                    List<DiPreSaleUrl> list = preSaleUrlMap.get(preSale.getId());
                    //根据文件类型分组并获取文件Key
                    Map<String, List<String>> drawingFileKeyMap = list.stream().collect(Collectors.groupingBy(DiPreSaleUrl::getType, Collectors.mapping(DiPreSaleUrl::getFileKey, Collectors.toList())));
                    detailed.setDrawingFileKeyMap(drawingFileKeyMap);
                }
                //返回产品方案参数配置信息
                if (CollectionUtils.isNotEmpty(preSaleLabelMap) && preSaleLabelMap.containsKey(preSaleManifest.getId())) {
                    detailed.setLabelList(preSaleLabelMap.get(preSaleManifest.getId()));
                }
                Optional.ofNullable(preSaleFileMap.get(preSale.getId())).ifPresent(diPreSaleFile -> {
                    detailed.setDrawingFileTypeList(com.google.common.collect.Lists.newArrayList(diPreSaleFile.stream().map(DiPreSaleUrl::getType).collect(Collectors.toSet())));
                });

                DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                        .eq(DiPreSaleCustomized::getPreSaleId, preSale.getId())
                        .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
                if (Objects.nonNull(diPreSaleCustomized)) {
                    Map<String, String> userNameMap = Maps.newHashMap();
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(com.google.common.collect.Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                    if (userListResult.isSuccess()) {
                        userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                        diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                        detailed.setTechSupportOwnerCode(diPreSaleCustomized.getTechSupportOwnerCode());
                        detailed.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                    }
                }
                detailed.setDifficultyLevel(preSale.getDifficultyLevel());
                detailed.setProductionDifficulty(preSale.getProductionDifficulty());
                detailed.setElectricalDifficulty(preSale.getElectricalDifficulty());
                detailed.setAfterSalesDifficulty(preSale.getAfterSalesDifficulty());
                detailed.setMechanicalDifficulty(preSale.getMechanicalDifficulty());
                detailedList.add(detailed);
            }
        }
        infoVo.setCostFeeTotal(costFeeTotal);
        infoVo.setDetailedList(detailedList);
        //构建账期数据
        List<DiPreSaleQuotePeriod> diPreSaleQuotePeriods = diPreSaleQuotePeriodMapper.selectList(Wrappers.<DiPreSaleQuotePeriod>lambdaQuery().eq(DiPreSaleQuotePeriod::getDiPreSaleQuoteId, preSaleQuote.getId()));
        if (CollectionUtils.isNotEmpty(diPreSaleQuotePeriods)) {
            List<PreSaleQuoteProductCostInfoVO.PreSaleQuotePeriod> preSaleQuotePeriods = diPreSaleQuotePeriods.stream().map(diPreSaleQuotePeriod -> {
                PreSaleQuoteProductCostInfoVO.PreSaleQuotePeriod preSaleQuotePeriod = new PreSaleQuoteProductCostInfoVO.PreSaleQuotePeriod();
                preSaleQuotePeriod.setId(diPreSaleQuotePeriod.getId());
                preSaleQuotePeriod.setBillPeriod(diPreSaleQuotePeriod.getBillPeriod());
                preSaleQuotePeriod.setPayAmount(diPreSaleQuotePeriod.getPayAmount());
                preSaleQuotePeriod.setRemark(diPreSaleQuotePeriod.getRemark());
                preSaleQuotePeriod.setPayPercent(diPreSaleQuotePeriod.getPayPercent());
                preSaleQuotePeriod.setPlanPayDate(diPreSaleQuotePeriod.getPlanPayDate());
                preSaleQuotePeriod.setRate(diPreSaleQuotePeriod.getRate());
                preSaleQuotePeriod.setInvoiceDesc(diPreSaleQuotePeriod.getInvoiceDesc());
                preSaleQuotePeriod.setRealPayDate(diPreSaleQuotePeriod.getRealPayDate());
                return preSaleQuotePeriod;
            }).collect(Collectors.toList());
            infoVo.setPeriodList(preSaleQuotePeriods);
        }
        return infoVo;
    }

    private void tradePresaleQuoteProductCostInfoVO(List<DiPreSaleQuoteDetail> preSaleQuoteDetailList,
                                                    Map<String, DiPreSale> preSaleMap,
                                                    List<PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed> detailedList,
                                                    Map<Long, List<DiPreSaleUrl>> preSaleUrlMap,
                                                    Map<Long, List<DiPreSaleManifest>> preSaleManifestMap,
                                                    Integer... notNeedFee

    ) {
        for (DiPreSaleQuoteDetail preSaleQuoteDetail : preSaleQuoteDetailList) {
            PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed detailed = new PreSaleQuoteProductCostInfoVO.ProductMaterialDetailed();
            //获取产品方案数据并构建需要返回的产品数据
            DiPreSale preSale = preSaleMap.get(preSaleQuoteDetail.getPreSaleCode());
            detailed.setId(preSale.getId());
            detailed.setNicheId(preSale.getNicheId().longValue());
            detailed.setNicheNo(preSale.getNicheCode());
            detailed.setPreSaleId(preSale.getId());
            detailed.setPreSaleCode(preSale.getPreSaleCode());
            detailed.setPreSaleName(preSale.getPreSaleName());
            detailed.setOrderType(preSale.getOrderType());
            detailed.setPreSaleType(preSale.getPreSaleType());
            if (null != preSale.getNum()) {
                detailed.setDemandNum(preSale.getNum());
            } else {
                detailed.setDemandNum(1);
            }
            if (null != preSale.getExpecteDate()) {
                detailed.setExpecteDate(DateUtils.format(preSale.getExpecteDate(), "yyyy-MM-dd"));
            }
            detailed.setPackageType(new BigDecimal(preSale.getPackageType()));
            if (null != preSale.getInstallationFee()) {
                detailed.setInstallationFee(preSale.getInstallationFee());
            } else {
                detailed.setInstallationFee(new BigDecimal(0));
            }
            //获取产品清单数据(由于目前是一个方案对应一个物料并且物料没有做版本，所以现在方案跟清单的数据是一对一的，直接取就行了)并构建需要返回的产品数据
            BigDecimal costFeeTotalTemp = BigDecimal.ZERO;
            BigDecimal costFeeTemp = BigDecimal.ZERO;
            //BigDecimal costGuideDurationTemp = BigDecimal.ZERO;
            for (DiPreSaleManifest preSaleManifest : preSaleManifestMap.get(preSale.getId())) {
                //复制产品方案那里的方法，循环调数据库(方法来源：PreSaleQuoteService---info())
                if (CollectionUtils.isEmpty(Arrays.asList(notNeedFee))) {
                    FeeItem fee = feedService.getFeeItem(preSale, preSaleManifest);
                    //costFeeTotal = costFeeTotal.add(fee.getTotalCostFee());
                    //这里是从亚杰那边复制过来的---2024-09-24，产品方案修改了，与产品方案保持一致
                    //detailed.setCostFee(fee.getFeeTotal());
                    costFeeTemp = MoneyUtils.sum(costFeeTemp, fee.getFeeTotal());
                    //detailed.setCostFeeSum(fee.getTotalCostFee());
                    costFeeTotalTemp = MoneyUtils.sum(costFeeTotalTemp, fee.getFeeTotal().multiply(BigDecimal.valueOf(preSale.getNum())));
                    //获取单套理论交期---2024-09-24,产品方案改了，保持一致
//                    if (null != fee.getGuideDuration()) {
//                        //detailed.setGuideDuration(fee.getGuideDuration().toString());
//                        costGuideDurationTemp = MoneyUtils.sum(costGuideDurationTemp, fee.getGuideDuration());
//                    } else {
//                        detailed.setGuideDuration("0");
//                    }
                }
            }
            detailed.setCostFee(costFeeTemp);
            detailed.setCostFeeSum(costFeeTotalTemp);
            detailed.setGuideDuration(preSale.getGuideDuration().toString());


//            DiMaterielVersion materielVersion = diMaterielVersionService.getById(preSaleManifest.getMaterialVersion());
//            Optional.ofNullable(materielVersion).ifPresent(v -> {
//                if (Objects.nonNull(v.getBomStatus())) {
//                    detailed.setShowVersionNo(getShowVersionNo(v.getBomStatus(),v.getVersionNo()));
//                }
//            });
//            detailed.setMechanicalDesignDuration(preSaleManifest.getMechanicalDesignDuration());
//            detailed.setElectricalDesignPeriod(preSaleManifest.getElectricalDesignPeriod());
//            detailed.setFeeTotal(preSaleManifest.getFeeTotal());
//            detailed.setRdDay(preSaleManifest.getRdDay());
            //MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
            //materielFeeQueryDTO.setMaterielId(preSaleManifest.getMaterialVersion());
//            MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);
//            if (Objects.nonNull(materielFeeDTO)) {
//                if (Objects.nonNull(materielFeeDTO.getGuideMaterielDuration())) {
//                    detailed.setSupplyDay(materielFeeDTO.getGuideMaterielDuration().intValue());
//                }
//                if (Objects.nonNull(materielFeeDTO.getGuideProduceDuration())) {
//                    detailed.setProduceDay(materielFeeDTO.getGuideProduceDuration().intValue());
//                }
//            }
//            detailed.setImplementDay(preSaleManifest.getImplementDay());
//            detailed.setCostFeeSum(detailed.getCostFee().multiply(new BigDecimal(detailed.getDemandNum())));
            //获取物料信息
//            DiMateriel materiel = materielMap.get(preSaleManifest.getMaterialVersion());
//            detailed.setMaterialNo(materiel.getMaterielNo());
//            detailed.setMaterialName(materiel.getMaterielName());
//            detailed.setVersionNo(materiel.getVersionNo());
//            detailed.setShowVersionNo(getShowVersionNo(materiel.getVersionStatus(), materiel.getVersionNo()));
            //获取物料库存
//            if (CollectionUtils.isNotEmpty(stockInfoMap) && stockInfoMap.containsKey(materiel.getId())) {
//                detailed.setMaterialStock(stockInfoMap.get(materiel.getId()).getMaterialStock());
//            } else {
//                detailed.setMaterialStock(0);
//            }

            //获取物料标准成本和合计成本
//            if (CollectionUtils.isNotEmpty(priceMap) && priceMap.containsKey(materiel.getId())) {
//                if (null != priceMap.get(materiel.getId()).getGuideSumCosts()) {
//                    detailed.setGuideSumCosts(priceMap.get(materiel.getId()).getGuideSumCosts());
//                } else {
//                    detailed.setGuideSumCosts(new BigDecimal(0));
//                }
//                if (null != priceMap.get(materiel.getId()).getCostSumCosts()) {
//                    detailed.setCostSumCosts(priceMap.get(materiel.getId()).getCostSumCosts());
//                } else {
//                    detailed.setCostSumCosts(new BigDecimal(0));
//                }
//            } else {
//                detailed.setGuideSumCosts(new BigDecimal(0));
//                detailed.setCostSumCosts(new BigDecimal(0));
//            }
            //构建报价明细返回数据
            detailed.setSaleQuote(preSaleQuoteDetail.getSaleQuote());
            detailed.setDeliveryQuote(preSaleQuoteDetail.getDeliveryQuote());
            detailed.setGrossProfit(preSaleQuoteDetail.getGrossProfit());
            if (Objects.isNull(preSaleQuoteDetail.getGrossMargin())) {
                detailed.setGrossMargin(new BigDecimal(0));
            } else {
                detailed.setGrossMargin(preSaleQuoteDetail.getGrossMargin());
            }
            detailed.setCountryCode(preSaleQuoteDetail.getCountryCode());
            detailed.setCountry(preSaleQuoteDetail.getCountry());
            detailed.setProvinceCode(preSaleQuoteDetail.getProvinceCode());
            detailed.setProvince(preSaleQuoteDetail.getProvince());
            detailed.setCityCode(preSaleQuoteDetail.getCityCode());
            detailed.setCity(preSaleQuoteDetail.getCity());
            detailed.setAreaCode(preSaleQuoteDetail.getAreaCode());
            detailed.setArea(preSaleQuoteDetail.getArea());
            if (null != preSaleQuoteDetail.getSystemServiceCycle()) {
                detailed.setSystemServiceCycle(preSaleQuoteDetail.getSystemServiceCycle());
            } else {
                detailed.setSystemServiceCycle(new BigDecimal("0"));
            }
            if (null != preSaleQuoteDetail.getSystemServiceFee()) {
                detailed.setSystemServiceFee(preSaleQuoteDetail.getSystemServiceFee());
            } else {
                detailed.setSystemServiceFee(new BigDecimal("0"));
            }
            if (null != preSaleQuoteDetail.getSaleServiceQuote()) {
                detailed.setSaleServiceQuote(preSaleQuoteDetail.getSaleServiceQuote());
            } else {
                detailed.setSaleServiceQuote(new BigDecimal("0"));
            }
            if (null != preSaleQuoteDetail.getSaleServiceCycle()) {
                detailed.setSaleServiceCycle(preSaleQuoteDetail.getSaleServiceCycle());
            } else {
                detailed.setSaleServiceCycle(new BigDecimal("0"));
            }
            //构建产品方案图纸数据
            if (CollectionUtil.isNotEmpty(preSaleUrlMap) && preSaleUrlMap.containsKey(preSale.getId())) {
                List<DiPreSaleUrl> list = preSaleUrlMap.get(preSale.getId());
                //根据文件类型分组并获取文件Key
                Map<String, List<String>> drawingFileKeyMap = list.stream().collect(Collectors.groupingBy(DiPreSaleUrl::getType, Collectors.mapping(DiPreSaleUrl::getFileKey, Collectors.toList())));
                detailed.setDrawingFileKeyMap(drawingFileKeyMap);
            }
            //返回产品方案参数配置信息
//            if (CollectionUtils.isNotEmpty(preSaleLabelMap) && preSaleLabelMap.containsKey(preSaleManifest.getId())) {
//                detailed.setLabelList(preSaleLabelMap.get(preSaleManifest.getId()));
//            }
//            Optional.ofNullable(preSaleFileMap.get(preSale.getId())).ifPresent(diPreSaleFile -> {
//                detailed.setDrawingFileTypeList(com.google.common.collect.Lists.newArrayList(diPreSaleFile.stream().map(DiPreSaleUrl::getType).collect(Collectors.toSet())));
//            });

            DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                    .eq(DiPreSaleCustomized::getPreSaleId, preSale.getId())
                    .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
            if (Objects.nonNull(diPreSaleCustomized)) {
                Map<String, String> userNameMap = Maps.newHashMap();
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(com.google.common.collect.Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                if (userListResult.isSuccess()) {
                    userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                    diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                    detailed.setTechSupportOwnerCode(diPreSaleCustomized.getTechSupportOwnerCode());
                    detailed.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                }
            }
            //detailed.setCostFee();
            detailedList.add(detailed);
        }
    }

    /**
     * 更新销售报价单绑定的产品方案状态
     *
     * @param preSaleQuoteNo 销售报价单编号
     * @param status         状态
     */
    public void updateQuotePlanStatus(String preSaleQuoteNo, String status) {
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, preSaleQuoteNo));
        if (null == preSaleQuote) {
            log.error("PreSaleQuoteService---updateQuotePlanStatus()---销售报价单不存在，报价单编号：{}", preSaleQuoteNo);
            return;
        }
        //查询销售报价单明细数据
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, preSaleQuote.getId()));
        if (CollectionUtils.isEmpty(preSaleQuoteDetailList)) {
            log.error("PreSaleQuoteService---updateQuotePlanStatus()---销售报价单明细不存在，报价单ID：{}", preSaleQuote.getId());
            return;
        }
        //查询报价单明细绑定的产品方案数据
        List<Long> preSaleIdsList = preSaleQuoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).distinct().toList();
        List<DiPreSale> preSaleList = diPreSaleService.queryDiPreSaleByIds(preSaleIdsList);
        //diPreSaleMapper.selectList(new LambdaQueryWrapper<DiPreSale>().in(DiPreSale::getPreSaleCode, preSaleCodeList));
        if (CollectionUtils.isEmpty(preSaleList)) {
            log.error("PreSaleQuoteService---updateQuotePlanStatus()---产品方案不存在，产品方案编码：{}", JSONUtil.toJsonStr(preSaleList));
            return;
        }
        //更新方案状态
//        LambdaUpdateChainWrapper<DiPreSale> updateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleMapper);
//        updateWrapper.set(DiPreSale::getPreSaleStatus, status);
//        updateWrapper.in(DiPreSale::getId, preSaleIdList);
//        updateWrapper.update();
        preSaleList.forEach(item -> {
            item.setPreSaleStatus(status);
            item.setPreSaleDoneTime(LocalDateTime.now());

            DiPreSale curPreSale = diPreSaleService.queryDiPreSaleById(item.getId());
            List<DiPreSale> curPreSaleVersions = diPreSaleService.lambdaQuery().eq(DiPreSale::getPreSaleCode, curPreSale.getPreSaleCode()).list();
            curPreSaleVersions.stream().forEach(preSale -> {
                if (PreSaleTypeEnum.STANDARD.getCode().equals(curPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(curPreSale.getPreSaleType())) {
                    item.setOrderPreSaleStatus(OrderPreSaleStatusEnum.INIT.getCode());
                } else {
                    item.setOrderPreSaleStatus(OrderPreSaleStatusEnum.TWO.getCode());
                    item.setTechnicalReviewFlag(true);
                }
//                Long materialId = Long.valueOf(historyInfo.getFees().get(historyInfo.getFees().size() - 1).getMaterialVersion());
//                List<MaterielChecklistNewDTO> materielChecklistNewDTOS = iDiMaterielService.queryMaterielPlanCheckListByVersion(materialId);
//                diPreSaleHistory.setPreSaleMaterialHistoryContent(JSON.toJSONString(materielChecklistNewDTOS));
//                MaterielBomVersionVo materialVo = new MaterielBomVersionVo();
//                materialVo.setMaterielVersionId(materialId);
//                MaterielVersionBomDTO materialBom = diMaterielBomService.bomByVersion(materialVo);
//                diPreSaleHistory.setPreSaleMaterialBomHistoryContent(JSON.toJSONString(materialBom));
//                diPreSaleHistory.setPreSaleId(preSale.getId());
//                historyInfo.setPreSaleStatus(status);
//                historyInfo.setPreSaleDoneTime(LocalDateTime.now());
//                diPreSaleHistory.setPreSaleHistoryContent(JSON.toJSONString(historyInfo));
//
//                preSaleHistoryService.saveDiPreSaleHistory(diPreSaleHistory);
            });
            //注意，这里要发送技术支持复核待办和钉钉
            tscSendMessageAndSaveAgency(item);
        });
        diPreSaleService.updateBatchById(preSaleList);

    }

    private void tscSendMessageAndSaveAgency(DiPreSale diPreSale) {
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
        if (null == diPreSaleCustomized) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---未获取到非标方案表数据，产品方案ID：{}", diPreSale.getId());
            return;
        }
        if (StringUtils.isBlank(diPreSaleCustomized.getTechSupportOwnerCode())) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---没有技术支持负责人，非标方案表数据：{}", JSONUtil.toJsonStr(diPreSaleCustomized));
            return;
        }
        if (StringUtils.isBlank(diPreSale.getNicheCode())) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---当前方案没有关联商机，产品方案信息：{}", JSONUtil.toJsonStr(diPreSale));
            return;
        }
        //根据商机获取订单信息
        List<DiOrder> diOrderList = diOrderService.list(new LambdaQueryWrapper<DiOrder>().eq(DiOrder::getNicheNo, diPreSale.getNicheCode()));
        if (CollectionUtils.isEmpty(diOrderList)) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---没有获取到订单数据，商机ID：{}", diPreSale.getNicheCode());
            return;
        }
        if (diOrderList.size() > 1) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---方案关联的订单数据大于1条，商机ID：{}", diPreSale.getNicheCode());
            return;
        }
        sendMessageAndSaveAgency(diPreSale.getPreSaleCode(), diPreSale.getPreSaleName(), diPreSaleCustomized.getTechSupportOwnerCode(), diPreSale.getPreSaleStatus(), diOrderList.get(0));
    }


    public void sendMessageAndSaveAgency(String preSaleCode, String preSaleName, String sendUser, String preSaleStatus, DiOrder diOrder) {
        //发送钉钉消息
        if (StringUtils.isBlank(preSaleName)) {
            DiPreSale diPreSale = diPreSaleService.queryDiPreSaleByCode(preSaleCode);
            preSaleName = diPreSale.getPreSaleName();
            preSaleStatus = diPreSale.getPreSaleStatus();
        }
        String title = StrUtil.format(DingTalkEnum.TECHNICAL_SUPPORT_NOTICE.getTitle(), preSaleCode);
        String content = StrUtil.format(DingTalkEnum.TECHNICAL_SUPPORT_NOTICE.getMessage(),
                StringUtils.isEmpty(diOrder.getProjectNo()) ? "" : diOrder.getProjectNo(),
                diOrder.getOrderNo(), preSaleCode);
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("产品方案");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        diMessageList.setSendingUser(sendUser);
        diMessageListService.insertDiMessageList(diMessageList);
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(preSaleCode);
        agencyTaskInfoDto.setJumpKey(diOrder.getOrderNo());
        agencyTaskInfoDto.setProjectNo(diOrder.getProjectNo());
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.TECHNICAL_SUPPORT_CHECK);
        agencyTaskInfoDto.setTaskName(preSaleName);
        //获取状态
        Map<String, String> statusMap = remoteDictDataService.queryAsMap("pre_sale_status");
        String taskStateDesc = "";
        if (CollectionUtils.isNotEmpty(statusMap) && statusMap.containsKey(preSaleStatus)) {
            taskStateDesc = statusMap.get(preSaleStatus);
        }
        agencyTaskInfoDto.setTaskStateDesc(taskStateDesc);
        agencyTaskInfoDto.setLiabilityByList(Collections.singletonList(sendUser));
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        //rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        TaskChangeResult taskResult = diAgencyTaskService.setTask(preSaleCode, AgencyTaskTypeEnum.TECHNICAL_SUPPORT_CHECK, Arrays.asList(sendUser),
                TaskArgs.builder()
                        .projectNo(diOrder.getProjectNo())
                        .jumpKey(diOrder.getOrderNo())
                        .taskName(preSaleName)
                        .taskStateDesc(taskStateDesc)
                        .expectDeliverDate(null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null)
                        .build()
        );

    }


    public PreSaleQuotationFeeCalcResponse preSaleQuotationFeeCalc(PreSaleQuotationFeeCalcRequest req) {
        PreSaleQuotationFeeCalcResponse totalResp = new PreSaleQuotationFeeCalcResponse();

        DiPreSaleQuote diPreSaleQuote = diPreSaleQuoteMapper.selectOne(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getPreSaleQuoteCode, req.getPreSaleQuoteCode()));

        if (Objects.isNull(diPreSaleQuote)) {
            return totalResp;
        }

        Integer id = diPreSaleQuote.getId();

        //查询商机
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(diPreSaleQuote.getNicheCode());

        //查询报价单明细
        List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, id));
        //清洗销售报价单数据，将GP1234落库，数据非空校验(可能会有垃圾数据)
        if (CollectionUtil.isEmpty(preSaleQuoteDetails)) {
            return totalResp;
        }

        Map<Long, BigDecimal> preSaleQuoteMap = preSaleQuoteDetails.stream().collect(Collectors.toMap(DiPreSaleQuoteDetail::getPreSaleId, DiPreSaleQuoteDetail::getSaleQuote));

        //清洗销售报价单数据，将GP1234落库，数据非空校验(可能会有垃圾数据)
        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleQuoteDetails.stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(diPreSales)) {
            return totalResp;
        }
        //产品方案map
        //Map<String, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));

        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
        //产品方案物料map
        Map<Long, List<DiPreSaleManifest>> manifestMap = diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));

        //物料集合
        List<DiMateriel> materielList = iDiMaterielService.list(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList())));
        Map<Long, DiMateriel> materielMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materielList)) {
            materielMap = materielList.stream().collect(Collectors.toMap(DiMateriel::getId, x -> x));
        }

        totalResp = this.calcByQuote(
                diPreSaleQuote.getCountryCode(), diPreSaleQuote.getProvinceCode(), diPreSaleQuote.getCityCode(), diPreSaleQuote.getAreaCode(),
                diPreSaleQuote.getBusinessCost(),
                diMarketingNicheT,
                diPreSales,
                manifestMap,
                materielMap, preSaleQuoteMap);

        return totalResp;

    }


    private BigDecimal addDecimal(BigDecimal... args) {
        BigDecimal start = BigDecimal.ZERO;
        boolean allIsNull = true;
        for (BigDecimal arg : args) {
            if (arg != null) {
                start = start.add(arg);
                allIsNull = false;
            }
        }
        if (allIsNull) {
            return null;
        }
        return start;
    }

    private BigDecimal increment(BigDecimal base, BigDecimal time, BigDecimal old) {

        if (base == null || time == null) {
            return old;
        }
        if (old == null) {
            return base.multiply(time);
        }
        return base.multiply(time).add(old);
    }

    private String convertDict(String type, String value, boolean name2Value) {
        String result = null;
        if (StringUtils.isBlank(value)) {
            return null;
        }
        QueryDict qry = new QueryDict();
        qry.setDictType(type);
        R<List<SysDictData>> r = remoteSysService.dictType(qry);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(r.getData())) {
            if (name2Value) {
                result = r.getData().stream().filter(s -> s.getDictLabel().equals(value)).findFirst().map(s -> s.getDictValue()).orElse(null);
            } else {
                result = r.getData().stream().filter(s -> s.getDictValue().equals(value)).findFirst().map(s -> s.getDictLabel()).orElse(null);
            }
        }
        log.info("convertDict type:{},value={},result={}", type, value, result);
        return result;
    }


    public PreSaleQuotationFeeCalcResponse preSaleQuotationFeeCalcTemp(PreSaleQuotationFeeCalcTempRequest req) {
        StopWatch watch = new StopWatch("preSaleQuotationFeeCalcTemp");
        watch.start("准备数据");
        //PreSaleQuotationFeeCalcResponse totalResp = new PreSaleQuotationFeeCalcResponse();
        String nicheCode = req.getNicheCode();

        List<Long> preSaleIds =
                req.getPreSaleInfoList().stream().map(preSaleInfo -> preSaleInfo.getPreSaleId()).collect(Collectors.toList());

        List<DiPreSale> diPreSales = diPreSaleService.queryDiPreSaleByIds(preSaleIds);
        if (StringUtils.isBlank(nicheCode) && CollectionUtils.isNotEmpty(diPreSales)) {
            nicheCode = diPreSales.get(0).getNicheCode();
        }

        if (CollectionUtils.isEmpty(req.getPreSaleInfoList())) {
            req.setPreSaleInfoList(diPreSales.stream().map(preSale -> {
                PreSaleQuotationFeeCalcTempRequest.preSaleInfo preSaleInfo = new PreSaleQuotationFeeCalcTempRequest.preSaleInfo();
                preSaleInfo.setPreSaleId(preSale.getId());
                preSaleInfo.setQuantity(BigDecimal.valueOf(preSale.getNum()));
                return preSaleInfo;
            }).collect(Collectors.toList()));
        }
        //修改方案的用量
        diPreSales.forEach(x -> {
            var input = req.getPreSaleInfoList().stream().filter(y -> y.getPreSaleId().equals(x.getId())).findFirst().orElse(null);
            if (input == null) {
                return;
            }
            if (input.getQuantity() != null) {
                x.setNum(input.getQuantity().intValue());
            }
        });

        Map<Long, BigDecimal> preSaleQuoteMap = req.getPreSaleInfoList().stream().collect(Collectors.toMap(x -> x.getPreSaleId(), x -> x.getPreSaleQuote()));
        //查询商机
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(nicheCode);

        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
        //产品方案物料map
        Map<Long, List<DiPreSaleManifest>> manifestMap = diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));

        //物料集合
        List<DiMateriel> materielList = iDiMaterielService.list(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList())));
        Map<Long, DiMateriel> materielMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materielList)) {
            materielMap = materielList.stream().collect(Collectors.toMap(DiMateriel::getId, x -> x));
        }
        watch.stop();
        //watch.start("计算费用");

        return calcByQuote(req.getCountryCode(), req.getProvinceCode(), req.getCityCode(), req.getAreaCode(), req.getBusinessCost(), diMarketingNicheT, diPreSales, manifestMap, materielMap, preSaleQuoteMap);

    }

    private PreSaleQuotationFeeCalcResponse calcByQuote(String countryCode, String provinceCode, String cityCode, String districtCode, BigDecimal businessCost,
                                                        DiMarketingNiche diMarketingNicheT,
                                                        List<DiPreSale> diPreSales,
                                                        Map<Long, List<DiPreSaleManifest>> manifestMap,
                                                        Map<Long, DiMateriel> materielMap, Map<Long, BigDecimal> preSaleQuoteMap) {

        String countryCodeNew = this.convertDict("tools_country2normal", countryCode, true);


        List<DiPreSale> tradePreSale = diPreSales.stream().filter(x -> PreSaleTypeEnum.TRADE.getCode().equals(x.getPreSaleType())).collect(Collectors.toList());
        List<DiPreSale> ProjectPreSale = diPreSales.stream().filter(x -> !PreSaleTypeEnum.TRADE.getCode().equals(x.getPreSaleType())).collect(Collectors.toList());

        BigDecimal tradeQuote = tradePreSale.stream().map(x -> preSaleQuoteMap.get(x.getId())).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(null);
        BigDecimal projectQuote = ProjectPreSale.stream().map(x -> preSaleQuoteMap.get(x.getId())).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(null);

        QuoteFeeResp tradeResp = this.calcByQuoteByPreSaleGroup(countryCodeNew, provinceCode, cityCode, districtCode, businessCost, diMarketingNicheT, tradePreSale, manifestMap, materielMap);
        tradeResp.setQuoteTotal(tradeQuote);
        QuoteFeeResp projectResp = this.calcByQuoteByPreSaleGroup(countryCodeNew, provinceCode, cityCode, districtCode, businessCost, diMarketingNicheT, ProjectPreSale, manifestMap, materielMap);
        projectResp.setQuoteTotal(projectQuote);
        QuoteFeeResp summaryResp = new QuoteFeeResp();
        summaryResp.setMatchCity(0);
        if (Objects.equals(1, tradeResp.getMatchCity()) || Objects.equals(1, projectResp.getMatchCity())) {
            summaryResp.setMatchCity(1);
        }
        summaryResp.setPreSaleFeeMap(new HashMap<>());
        boolean hasBom = false;//方案不存在这个提示
        FeeResp temp = quotationCalcService.sumPreSaleFeeByType(Arrays.asList(tradeResp, projectResp), businessCost, hasBom);
        BeanUtils.copyProperties(temp, summaryResp);
        summaryResp.getPreSaleFeeMap().putAll(tradeResp.getPreSaleFeeMap());
        summaryResp.getPreSaleFeeMap().putAll(projectResp.getPreSaleFeeMap());
        summaryResp.setBusinessCost(businessCost);
        summaryResp.setTrace(tradeResp.getTrace() + "\n" + projectResp.getTrace());
        summaryResp.setQuoteTotal(MoneyUtils.sum(tradeResp.getQuoteTotal(), projectResp.getQuoteTotal()));

        PreSaleQuotationFeeCalcResponse totalResp = new PreSaleQuotationFeeCalcResponse();
        totalResp.setQuoteSummary(summaryResp);
        totalResp.setQuote4Project(projectResp);
        totalResp.setQuote4Trade(tradeResp);

        DiPreSaleQuoteGp summaryGp = feeResponse2QuoteGp(null, summaryResp, PreSaleGpTypeEnum.SUMMARY, summaryResp.getMatchCity());
        DiPreSaleQuoteGp projectGp = feeResponse2QuoteGp(null, projectResp, PreSaleGpTypeEnum.PROJECT, summaryResp.getMatchCity());
        DiPreSaleQuoteGp tradeGp = feeResponse2QuoteGp(null, tradeResp, PreSaleGpTypeEnum.TRADE, summaryResp.getMatchCity());


        totalResp.setGpDetailResponse(preSaleConverUtil.entityConvertGpDetail(summaryGp));
        totalResp.setGpDetailResponse4Project(preSaleConverUtil.entityConvertGpDetail(projectGp));
        totalResp.setGpDetailResponse4Trade(preSaleConverUtil.entityConvertGpDetail(tradeGp));

        totalResp.setGpEstimating4Project(diPreSaleQuoteGpService.getByPreSaleQuoteId(projectGp, projectQuote));
        totalResp.setGpEstimating4Trade(diPreSaleQuoteGpService.getByPreSaleQuoteId(tradeGp, tradeQuote));
        totalResp.setGpEstimating(diPreSaleQuoteGpService.getByPreSaleQuoteId(summaryGp, MoneyUtils.sum(tradeQuote, projectQuote)));

        return totalResp;
    }

    private QuoteFeeResp calcByQuoteByPreSaleGroup(String countryCode, String provinceCode, String cityCode, String districtCode, BigDecimal businessCost,
                                                   DiMarketingNiche diMarketingNicheT,
                                                   List<DiPreSale> diPreSales,
                                                   Map<Long, List<DiPreSaleManifest>> manifestMap,
                                                   Map<Long, DiMateriel> materielMap) {
        StopWatch watch = new StopWatch("preSaleQuotationFeeCalcTemp");

        MultiFeeCalcQry feeCalcQry = new MultiFeeCalcQry();

        //feeCalcQry.setBusinessCost(null);
        feeCalcQry.setCountryCode(countryCode);
        feeCalcQry.setCity(cityCode);
        feeCalcQry.setProvince(provinceCode);
        feeCalcQry.setDistrict(districtCode);

        feeCalcQry.setBusinessCost(businessCost);

        feeCalcQry.setExport(false);

        feeCalcQry.setBu(this.convertDict("tools_bu2niche", diMarketingNicheT.getNicheOwnerDept(), true));

        if (diMarketingNicheT.getNeedPostSaleInstall() != null && !diMarketingNicheT.getNeedPostSaleInstall()) {
            log.info("安装复选框未选中,售后费用未0");
            //当需要售安装复选框未选中时，方案内-bomtab 中的”费用和周期清单“ 表单中的 安装调试费用和安装调试周期（天）字段计为0，且隐藏，销售报价单、合同管理处的GP3 售后费用计为0.
            feeCalcQry.setNeedPostSaleInstall(false);
        } else {
            feeCalcQry.setNeedPostSaleInstall(true);
        }
        if (CollectionUtils.isEmpty(diPreSales)) {
            return new QuoteFeeResp();
        }

        for (DiPreSale diPreSale : diPreSales) {
            BigDecimal MaterielTotalFee = null;
            // 循环方案
            MultiFeeCalcQry.PreSale feePreSale = new MultiFeeCalcQry.PreSale();
            feePreSale.setPackageType(diPreSale.getPackageTypeConfig());
            feePreSale.setNum(BigDecimal.valueOf(diPreSale.getNum()));
            String industry = StringUtils.isBlank(diPreSale.getIndustryCategories()) ? diMarketingNicheT.getIndustryCategories() : diPreSale.getIndustryCategories();
            String difficultyLevel = StringUtils.isBlank(diPreSale.getDifficultyLevel()) ? String.valueOf(diMarketingNicheT.getDifficultyLevel()) : diPreSale.getDifficultyLevel();
            feePreSale.setIndustry(quotationCalcService.toToolsIndustry(industry));

            feePreSale.setTechnicalDifficulty(difficultyLevel);
            feePreSale.setMechanicalDifficulty(diPreSale.getMechanicalDifficulty());
            feePreSale.setElectricalDifficulty(diPreSale.getElectricalDifficulty());
            feePreSale.setProductionDifficulty(diPreSale.getProductionDifficulty());
            feePreSale.setAfterSalesDifficulty(diPreSale.getAfterSalesDifficulty());

            feePreSale.setPreSaleCode(diPreSale.getPreSaleCode());
            feePreSale.setPreSaleType(PreSaleTypeEnum.of(diPreSale.getPreSaleType()));
            List<DiPreSaleManifest> manifests = manifestMap.get(diPreSale.getId());

            for (DiPreSaleManifest manifest : manifests) {
                // 循环物料
                DiMateriel materiel = materielMap.get(manifest.getMaterialVersion());
                MultiFeeCalcQry.MaterielItem item = new MultiFeeCalcQry.MaterielItem();
                item.setMaterielId(materiel.getId());
                item.setMaterielLength(materiel.getMaterielLength());
                item.setMaterielWidth(materiel.getMaterielWidth());
                item.setMaterielHeight(materiel.getMaterielHigh());
                item.setMaterielWeight(materiel.getMaterielWeight());
                item.setQuantity(manifest.getUseNum() == null ? BigDecimal.ONE : BigDecimal.valueOf(manifest.getUseNum()));
                item.setHasBom(0);// 无需显示维护提示 固定写死
                watch.start("diPreSaleService.getFee");
                FeeItem fee = feedService.getFeeItem(diPreSale, manifest);
                watch.stop();
                if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
                    item.setGuideMaterialCosts(this.addDecimal(BigDecimal.ZERO, manifest.getRealTemporaryMaterialCosts(), manifest.getRealMaterialFee()));
                    item.setGuideProductionCosts(this.addDecimal(BigDecimal.ZERO, manifest.getRealTemporaryProductionCosts(), manifest.getRealGuideProductionCosts(), manifest.getRealProductFee()));
                    feePreSale.setPackageCost(this.addDecimal(BigDecimal.ZERO, manifest.getRealPackageFee()));

                    feePreSale.setElectricalDesignCost(this.addDecimal(BigDecimal.ZERO, manifest.getRealElectricDesignFee()));
                    feePreSale.setMechanicalDesignCost(this.addDecimal(BigDecimal.ZERO, manifest.getRealMachineDesignFee()));
                    feePreSale.setTechnicalSupportCost(this.addDecimal(BigDecimal.ZERO, manifest.getRealTechSupportFee()));
                    feePreSale.setAfterSaleCost(this.addDecimal(BigDecimal.ZERO, manifest.getRealDeliveryDebugFee()));

                    feePreSale.setAfterSaleRate(this.addDecimal(BigDecimal.ZERO, manifest.getRealDeliveryDebugDay()));

                } else {
                    //feePreSale.setAfterSaleRate(MoneyUtils.toDecimal(manifest.getImplementDay()));

                    item.setGuideMaterialCosts(this.addDecimal(fee.getTemporaryMaterialCosts(), MoneyUtils.divide(fee.getMaterialFee(), manifest.getUseNum() == null ? BigDecimal.ONE : BigDecimal.valueOf(manifest.getUseNum()))));
                    item.setGuideProductionCosts(this.addDecimal(fee.getTemporaryProductionCosts(),
                            MoneyUtils.divide(fee.getGuideProductionCosts(), manifest.getUseNum() == null ? BigDecimal.ONE : BigDecimal.valueOf(manifest.getUseNum()))
                    ));
                }

                if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.TRADE) {
                    MaterielTotalFee = MoneyUtils.sum(MaterielTotalFee, manifest.getFeeTotal());
                }
                feePreSale.getMaterielItems().add(item);
            }
            feePreSale.setTradeMaterialCosts(MaterielTotalFee);
            feeCalcQry.getPreSaleList().add(feePreSale);
        }
        QuoteFeeResp totalResp = quotationCalcService.calcV2(feeCalcQry);

        //watch.stop();
        log.info("计算费用耗时:{}", watch.prettyPrint());
        return totalResp;
    }

    /**
     * 查询共享人
     *
     * @param preSaleQuoteCode
     * @return
     */
    public List<PreSaleQuotaShareResponse> queryShareByCode(String preSaleQuoteCode) {
        List<DiPreSaleQuote> preSaleQuoteList = this.diPreSaleQuoteMapper.selectList(
                new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, preSaleQuoteCode));
        if (CollectionUtils.isEmpty(preSaleQuoteList)) {
            return new ArrayList<>();
        }
        return saleQuoteShareService.queryShareListDTO(Long.valueOf(preSaleQuoteList.get(0).getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void repairPreSaleQuoteGp() {
        //获取所有报价单
        List<DiPreSaleQuote> preSaleQuoteList = diPreSaleQuoteMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuote>());
        if (CollectionUtil.isEmpty(preSaleQuoteList)) {
            return;
        }
        List<String> repairCodeList = new ArrayList<>();
        //获取所有报价单GP1234
        List<DiPreSaleQuoteGp> preSaleQuoteGpList = diPreSaleQuoteGpService.list();
        if (CollectionUtil.isNotEmpty(preSaleQuoteGpList)) {
            List<String> codeList = preSaleQuoteList.stream().map(DiPreSaleQuote::getPreSaleQuoteCode).distinct().toList();
            List<String> noRepairCode = preSaleQuoteGpList.stream().map(DiPreSaleQuoteGp::getPreSaleQuoteCode).distinct().toList();
            repairCodeList.addAll(codeList.stream().filter(code -> !noRepairCode.contains(code)).toList());
        } else {
            repairCodeList.addAll(preSaleQuoteList.stream().map(DiPreSaleQuote::getPreSaleQuoteCode).distinct().toList());
        }
        if (CollectionUtil.isEmpty(repairCodeList)) {
            return;
        }
        Map<String, DiPreSaleQuote> preSaleQuoteMap = preSaleQuoteList.stream().collect(Collectors.toMap(DiPreSaleQuote::getPreSaleQuoteCode, x -> x));

        List<DiPreSaleQuoteGp> saveList = new ArrayList<>();
        for (String repairCode : repairCodeList) {
            if (!preSaleQuoteMap.containsKey(repairCode)) {
                continue;
            }
            List<DiPreSaleQuoteGp> preSaleQuoteGp = calcFee(preSaleQuoteMap.get(repairCode));
            if (preSaleQuoteGp == null) continue;
            saveList.addAll(preSaleQuoteGp);
        }
        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }
        diPreSaleQuoteGpService.upset(saveList);
    }


    public List<DiPreSaleQuoteGp> calcFee(DiPreSaleQuote quote) {
        PreSaleQuotationFeeCalcRequest request = new PreSaleQuotationFeeCalcRequest();
        request.setPreSaleQuoteCode(quote.getPreSaleQuoteCode());
        PreSaleQuotationFeeCalcResponse response = preSaleQuotationFeeCalc(request);
        if (null == response) {
            return null;
        }
        return feeCalcResult2QuoteGp(quote, response);
    }

    @NotNull
    public static List<DiPreSaleQuoteGp> feeCalcResult2QuoteGp(DiPreSaleQuote quote, PreSaleQuotationFeeCalcResponse response) {
        List<DiPreSaleQuoteGp> result = new ArrayList<>();
        if (response.getGpDetailResponse() != null) {
            result.add(feeResponse2QuoteGp(quote, response.getQuoteSummary(), PreSaleGpTypeEnum.SUMMARY, response.getGpDetailResponse().getMatchCity()));
        }
        if (response.getGpDetailResponse4Project() != null) {
            result.add(feeResponse2QuoteGp(quote, response.getQuote4Project(), PreSaleGpTypeEnum.PROJECT, response.getGpDetailResponse4Project().getMatchCity()));
        }
        if (response.getGpDetailResponse4Trade() != null) {
            result.add(feeResponse2QuoteGp(quote, response.getQuote4Trade(), PreSaleGpTypeEnum.TRADE, response.getGpDetailResponse4Trade().getMatchCity()));
        }
        return result;
    }

    @NotNull
    public static <T extends FeeResp> DiPreSaleQuoteGp feeResponse2QuoteGp(DiPreSaleQuote quote, T feeResp, PreSaleGpTypeEnum type, Integer matchCity) {
        DiPreSaleQuoteGp preSaleQuoteGp = new DiPreSaleQuoteGp();
        preSaleQuoteGp.setGpType(type.getCode());
        if (quote != null) {
            preSaleQuoteGp.setPreSaleQuoteId(Long.valueOf(quote.getId()));
            preSaleQuoteGp.setPreSaleQuoteCode(quote.getPreSaleQuoteCode());
        }
        preSaleQuoteGp.setMatchCity(matchCity);
        if (null != feeResp.getMaterialCost()) {
            preSaleQuoteGp.setProdCostMaterial(feeResp.getMaterialCost());
        }
        if (null != feeResp.getProcessCost()) {
            preSaleQuoteGp.setProdCostMachining(feeResp.getProcessCost());
        }
        if (null != feeResp.getPackageCost()) {
            preSaleQuoteGp.setProdCostPacking(feeResp.getPackageCost());
        }
        if (null != feeResp.getGp1()) {
            preSaleQuoteGp.setProdCostSubtotal(feeResp.getGp1());
        }
        if (null != feeResp.getBusinessCost()) {
            preSaleQuoteGp.setSceneInstallIncidentalFees(feeResp.getBusinessCost());
            preSaleQuoteGp.setSceneInstallIncidentalSubtotal(feeResp.getBusinessCost());
        }
        if (null != feeResp.getDesignCost()) {
            preSaleQuoteGp.setDirectCostDr(feeResp.getDesignCost());
        }
        if (null != feeResp.getAfterSaleCost()) {
            preSaleQuoteGp.setDirectCostAs(feeResp.getAfterSaleCost());
        }
        if (null != feeResp.getLogisticsCost()) {
            preSaleQuoteGp.setDirectCostLogistics(feeResp.getLogisticsCost());
        }
        if (null != feeResp.getSalesCost()) {
            preSaleQuoteGp.setDirectCostSale(feeResp.getSalesCost());
        }
        if (null != feeResp.getGp3()) {
            preSaleQuoteGp.setDirectCostSubtotal(feeResp.getGp3());
        }
        if (null != feeResp.getBusinessOperationCost()) {
            preSaleQuoteGp.setOperateCostBusiness(feeResp.getBusinessOperationCost());
        }
        if (null != feeResp.getManagementOperationCost()) {
            preSaleQuoteGp.setOperateCostManage(feeResp.getManagementOperationCost());
        }
        if (null != feeResp.getGp4()) {
            preSaleQuoteGp.setOperateCostSubtotal(feeResp.getGp4());
        }
        preSaleQuoteGp.setQuoteTotal(feeResp.getQuoteTotal());
        return preSaleQuoteGp;
    }

    public List<Long> queryPreSaleIdListByQuoteNo(String preSaleQuoteNo) {

        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, preSaleQuoteNo));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        //查询销售报价单明细数据
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, preSaleQuote.getId()));
        if (CollectionUtils.isEmpty(preSaleQuoteDetailList)) {
            throw new ServiceException("销售报价单数据不正确");
        }
        return preSaleQuoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).distinct().toList();
    }


    /**
     * 退回方案
     *
     * @param request
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void returnQuote(ReturnQuoteRequest request) {
        List<DiPreSaleQuote> diPreSaleQuotes = diPreSaleQuoteMapper.selectList(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getNicheCode, request.getNicheCode()));
        if (CollectionUtils.isNotEmpty(diPreSaleQuotes)) {
            //更新所有报价单状态为已放弃
            DiPreSaleQuote diPreSaleQuote = new DiPreSaleQuote();
            diPreSaleQuote.setQuoteStatus(2);
            diPreSaleQuoteMapper.update(diPreSaleQuote, Wrappers.<DiPreSaleQuote>lambdaUpdate().in(DiPreSaleQuote::getId, diPreSaleQuotes.stream().map(DiPreSaleQuote::getId).collect(Collectors.toList())));

            //更新商机为方案报价阶段
            iDiMarketingNicheService.updateNicheStatus(request.getNicheCode(), "8");
        }
    }

    /**
     * 完成报价
     *
     * @param request
     * @return
     */
    public Boolean finishQuote(FinishQuoteRequest request) {
        List<DiPreSaleQuote> diPreSaleQuotes = diPreSaleQuoteMapper.selectList(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getNicheCode, request.getNicheCode())
                .ne(DiPreSaleQuote::getQuoteStatus, PreSaleQuoteStatusEnum.GIVE_UP.getCode()));
        if (CollectionUtils.isNotEmpty(diPreSaleQuotes)) {
            DiPreSaleQuote diPreSaleQuote = diPreSaleQuotes.get(0);
            //报价单审批通过或提前执行
            if (AdvanceExecutionStatusEnum.YES.getCode().equals(diPreSaleQuote.getIsAdvanceExecution())) {
                iDiMarketingNicheService.updateNicheStatus(request.getNicheCode(), NicheStatusEnum.ORDER_FORM_EARLY_EXECUTION.getCode());
            } else if (diPreSaleQuote.getApprovalStatus() == 2) {
                //商机状态 变更为 合同阶段。
                iDiMarketingNicheService.updateNicheStatus(request.getNicheCode(), "2");
            } else {
                return false;
            }
            //删除代办
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setType("2");
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE_QUOTE);
            agencyTaskInfoDto.setBusinessKey(diPreSaleQuote.getNicheCode());
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void abandonQuote(PreSaleQuoteEditRequest request, String nicheStatus) {
        if (Objects.isNull(request.getId())) {
            throw new ServiceException("必填项缺失");
        }
        //获取销售报价单
        DiPreSaleQuote diPreSaleQuote = diPreSaleQuoteMapper.selectById(request.getId());
        if (Objects.isNull(diPreSaleQuote)) {
            throw new ServiceException("销售报价单不存在");
        }
        //判断是否生成订单
        List<DiOrder> orderList = diOrderService.list(new LambdaQueryWrapper<DiOrder>().eq(DiOrder::getPreSaleQuoteNo, diPreSaleQuote.getPreSaleQuoteCode()));
        if (CollectionUtils.isNotEmpty(orderList)) {
            throw new ServiceException("销售报价单已生成订单，不允许放弃");
        }
        //获取销售报价单下产品方案
        List<DiPreSaleQuoteDetail> quoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, diPreSaleQuote.getId()));
        if (CollectionUtils.isEmpty(quoteDetailList)) {
            throw new ServiceException("销售报价单下无产品方案");
        }
        //商机状态变更为报价阶段
        iDiMarketingNicheService.updateNicheStatus(diPreSaleQuote.getNicheCode(), nicheStatus);
        //获取合同
        List<DiContract> contractList = diContractMapper.selectList(new LambdaQueryWrapper<DiContract>()
                .eq(DiContract::getPreSaleQuoteNo, diPreSaleQuote.getPreSaleQuoteCode())
                .ne(DiContract::getContractState, '4'));
        if (CollectionUtils.isNotEmpty(contractList)) {
            contractList.forEach(contract -> {
                //合同变更为已放弃
                LambdaUpdateChainWrapper<DiContract> contractUpdateWrapper = new LambdaUpdateChainWrapper<>(diContractMapper);
                contractUpdateWrapper.eq(DiContract::getId, contract.getId());
                contractUpdateWrapper.set(DiContract::getContractState, ContractConstants.STR_FOUR);
                contractUpdateWrapper.set(DiContract::getUpdateBy, SecurityUtils.getUsername());
                contractUpdateWrapper.update();
            });
        }
        //将销售报价单放弃
        LambdaUpdateChainWrapper<DiPreSaleQuote> quoteUpdateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleQuoteMapper);
        quoteUpdateWrapper.eq(DiPreSaleQuote::getId, diPreSaleQuote.getId());
        quoteUpdateWrapper.set(DiPreSaleQuote::getQuoteStatus, ContractConstants.TWO);
        quoteUpdateWrapper.set(DiPreSaleQuote::getUpdateBy, SecurityUtils.getUsername());
        quoteUpdateWrapper.update();
        //将代办任务或代办审批删除
        if (CollectionUtils.isNotEmpty(contractList)) {
            //代办审批
            contractList.forEach(contract -> {
                AgencyApprovalInfoDTO approvalInfoDto = new AgencyApprovalInfoDTO();
                approvalInfoDto.setApprovalTypeEnum(ApprovalTypeEnum.CONTRACT_RISK_APPROVAL);
                approvalInfoDto.setBusinessKey(contract.getContractNo());
                approvalInfoDto.setType("2");
                String topic = AgencyConstants.AGENCY_APPROVAL_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_APPROVAL_TAG;
                rocketMQTemplate.syncSend(topic, approvalInfoDto);
            });
        }
        //代办任务
        quoteDetailList.forEach(quoteDetail -> {
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setType("2");
            //删除交期复核代办
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK);
            agencyTaskInfoDto.setBusinessKey(quoteDetail.getPreSaleId().toString());
            agencyTaskInfoDto.setJumpKey(quoteDetail.getPreSaleId().toString());
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            //删除采购复核待办任务
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_PURCHASE);
            agencyTaskInfoDto.setBusinessKey(quoteDetail.getPreSaleId().toString());
            agencyTaskInfoDto.setJumpKey(quoteDetail.getPreSaleId().toString());
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            //删除交付复核待办任务
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_DELIVER);
            agencyTaskInfoDto.setBusinessKey(quoteDetail.getPreSaleId().toString());
            agencyTaskInfoDto.setJumpKey(quoteDetail.getPreSaleId().toString());
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            //将候补销售报价单代办任务删除
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.SUPPLY_PRE_SALE_QUOTE);
            agencyTaskInfoDto.setBusinessKey(diPreSaleQuote.getId().toString());
            agencyTaskInfoDto.setJumpKey(diPreSaleQuote.getPreSaleQuoteCode());
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        });
        //代办审批
        AgencyApprovalInfoDTO approvalInfoDto = new AgencyApprovalInfoDTO();
        approvalInfoDto.setApprovalTypeEnum(ApprovalTypeEnum.SALE_OFFER_COST_APPROVAL);
        approvalInfoDto.setBusinessKey(diPreSaleQuote.getPreSaleQuoteCode());
        approvalInfoDto.setType("2");
        String topic = AgencyConstants.AGENCY_APPROVAL_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_APPROVAL_TAG;
        rocketMQTemplate.syncSend(topic, approvalInfoDto);
    }

}
