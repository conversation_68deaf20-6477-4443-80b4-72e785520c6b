package com.dyd.di.matrix.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.matrix.domain.*;
import com.dyd.di.matrix.entity.DiMatrixTerritoryMeta;
import com.dyd.di.matrix.enums.MetaTypeEnum;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryMapper;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryMetaMapper;
import com.dyd.di.matrix.service.DiMatrixTerritoryMetaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【di_matrix_territory_meta(版图元数据)】的数据库操作Service实现
* @createDate 2025-03-19 14:13:43
*/
@Service
public class DiMatrixTerritoryMetaServiceImpl extends ServiceImpl<DiMatrixTerritoryMetaMapper, DiMatrixTerritoryMeta>
implements DiMatrixTerritoryMetaService {

    @Autowired
    private DiMatrixTerritoryMetaMapper diMatrixTerritoryMetaMapper;

    @Autowired
    private DiMatrixTerritoryMapper diMatrixTerritoryMapper;

    /**
     * 修改和编辑版图
     * @param request
     */
    @Transactional
    @Override
    public List<DiMatrixConflictListResponse> saveAndEditMatrixTerritoryMeta(MatrixTerritoryMetaSaveRequest request) {

       /* List<DiMatrixConflictListResponse> diMatrixConflictListResponses = checkConflict(request);
        if(CollectionUtils.isNotEmpty(diMatrixConflictListResponses)){
            return diMatrixConflictListResponses;
        }*/
        //根据类型分类
        Map<Integer, List<MatrixTerritoryMetaSaveRequest.Meta>> mateaMap = request.getMatrixMetas().stream().collect(Collectors.groupingBy(MatrixTerritoryMetaSaveRequest.Meta::getMetaType));
        //升序排，先保存配件
        List<Integer> keys = mateaMap.keySet().stream().sorted().collect(Collectors.toList());

        for(Integer metaType:keys){

            //查询类型现有数据
            //List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).eq(DiMatrixTerritoryMeta::getMetaType, metaType));

            List<MatrixTerritoryMetaSaveRequest.Meta> metas = mateaMap.get(metaType);
            Set<String> metaNames = metas.stream().map(MatrixTerritoryMetaSaveRequest.Meta::getTerritoryName).collect(Collectors.toSet());
            /*//如果名称去重size小于原有size，说明
            if(metaNames.size() < metas.size()){
                throw new RuntimeException(MetaTypeEnum.getDesc(metaType)+"中名称有重复");
            }*/
            //id为空的保存
            List<MatrixTerritoryMetaSaveRequest.Meta> addMetas = metas.stream().filter(meta -> meta.getId() == null).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(addMetas)){
                addMetas.stream().forEach(meta -> {
                    DiMatrixTerritoryMeta diMatrixTerritoryMeta = new DiMatrixTerritoryMeta();
                    diMatrixTerritoryMeta.setMetaType(metaType);
                    diMatrixTerritoryMeta.setTerritoryName(meta.getTerritoryName());
                    diMatrixTerritoryMeta.setCreateBy(SecurityUtils.getUsername());
                    diMatrixTerritoryMeta.setCreateTime(LocalDateTime.now());
                    diMatrixTerritoryMeta.setPartsParentId(meta.getPartsParentId());
                    //配件品类
                    if(metaType == 7 && Objects.isNull(meta.getPartsParentId())){
                        List<DiMatrixTerritoryMeta> diMatrixTerritoryMetasT = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).eq(DiMatrixTerritoryMeta::getMetaType, 6).eq(DiMatrixTerritoryMeta::getTerritoryName, meta.getPartsParentName()));
                        if(CollectionUtils.isEmpty(diMatrixTerritoryMetasT)){
                            throw new RuntimeException("归属功能不能为空");
                        }
                        diMatrixTerritoryMeta.setPartsParentId(diMatrixTerritoryMetasT.get(0).getId());
                    }

                    diMatrixTerritoryMetaMapper.insert(diMatrixTerritoryMeta);
                });
            }
            //更新数据
            List<MatrixTerritoryMetaSaveRequest.Meta> updateMetas = metas.stream().filter(meta -> meta.getId() != null).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(updateMetas)){
                updateMetas.stream().forEach(meta -> {
                    DiMatrixTerritoryMeta diMatrixTerritoryMeta = new DiMatrixTerritoryMeta();
                    diMatrixTerritoryMeta.setId(meta.getId());
                    diMatrixTerritoryMeta.setTerritoryName(meta.getTerritoryName());
                    diMatrixTerritoryMeta.setPartsParentId(meta.getPartsParentId());
                    diMatrixTerritoryMeta.setUpdateBy(SecurityUtils.getUsername());
                    diMatrixTerritoryMeta.setUpdateTime(LocalDateTime.now());
                    diMatrixTerritoryMeta.setDelFlag(0);
                    if(Objects.nonNull(meta.getIsDelete()) && meta.getIsDelete()){
                        diMatrixTerritoryMeta.setDelFlag(1);
                    }else {
                        //配件品类
                        if (metaType == 7 && Objects.isNull(meta.getPartsParentId())) {
                            List<DiMatrixTerritoryMeta> diMatrixTerritoryMetasT = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).eq(DiMatrixTerritoryMeta::getMetaType, 6).eq(DiMatrixTerritoryMeta::getTerritoryName, meta.getPartsParentName()));
                            if (CollectionUtils.isEmpty(diMatrixTerritoryMetasT)) {
                                throw new RuntimeException("归属功能不能为空");
                            }
                            diMatrixTerritoryMeta.setPartsParentId(diMatrixTerritoryMetasT.get(0).getId());
                        }
                    }
                    diMatrixTerritoryMetaMapper.updateById(diMatrixTerritoryMeta);
                });
            }

            /*if(CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)) {
                List<Long> updateMetaIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(updateMetas)) {
                    updateMetaIds = updateMetas.stream().map(MatrixTerritoryMetaSaveRequest.Meta::getId).collect(Collectors.toList());
                }
                //需要删除的数据
                List<Long> finalUpdateMetaIds = updateMetaIds;
                List<DiMatrixTerritoryMeta> deleteMetas = diMatrixTerritoryMetas.stream().filter(diMatrixTerritoryMeta -> !finalUpdateMetaIds.contains(diMatrixTerritoryMeta.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteMetas)) {
                    deleteMetas.stream().forEach(diMatrixTerritoryMeta -> {
                        DiMatrixTerritoryMeta diMatrixTerritoryMetaDelete = new DiMatrixTerritoryMeta();
                        diMatrixTerritoryMetaDelete.setId(diMatrixTerritoryMeta.getId());
                        diMatrixTerritoryMetaDelete.setDelFlag(1);
                        diMatrixTerritoryMetaDelete.setUpdateBy(SecurityUtils.getUsername());
                        diMatrixTerritoryMetaDelete.setUpdateTime(LocalDateTime.now());
                        diMatrixTerritoryMetaMapper.updateById(diMatrixTerritoryMetaDelete);
                    });
                }
            }*/
        }

        //处理一个类型全部删除，没有任何传入
        /*List<Integer> deleByType = Arrays.asList(1, 2, 3, 4, 5,6,7).stream().filter(key -> !mateaMap.keySet().contains(key)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(deleByType)){
            DiMatrixTerritoryMeta diMatrixTerritoryMetaDelete = new DiMatrixTerritoryMeta();
            diMatrixTerritoryMetaDelete.setDelFlag(1);
            diMatrixTerritoryMetaDelete.setUpdateBy(SecurityUtils.getUsername());
            diMatrixTerritoryMetaDelete.setUpdateTime(LocalDateTime.now());
            diMatrixTerritoryMetaMapper.update(diMatrixTerritoryMetaDelete,Wrappers.<DiMatrixTerritoryMeta>lambdaUpdate().in(DiMatrixTerritoryMeta::getMetaType,deleByType));
        }*/

        /*if(version != null) {
            DiMatrixTerritoryMeta diMatrixTerritoryMeta = new DiMatrixTerritoryMeta();
            diMatrixTerritoryMeta.setVersion(version+1);
            diMatrixTerritoryMetaMapper.update(diMatrixTerritoryMeta,null);
        }*/
        return null;
    }

    /**
     * 校验冲突
     * @param request
     */
    /*public List<DiMatrixConflictListResponse> checkConflict(MatrixTerritoryMetaSaveRequest request){
        List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0));

        Map<Long, DiMatrixTerritoryMeta> diMatrixTerritoryMetaMap = diMatrixTerritoryMetas.stream().collect(Collectors.toMap(DiMatrixTerritoryMeta::getId, Function.identity()));
        List<DiMatrixConflictListResponse> diMatrixConflictListResponseList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)){
            List<MatrixTerritoryMetaSaveRequest.Meta> matrixMetas = request.getMatrixMetas();
            for(MatrixTerritoryMetaSaveRequest.Meta meta:matrixMetas){
                DiMatrixConflictListResponse diMatrixConflictListResponse = new DiMatrixConflictListResponse();
                diMatrixConflictListResponse.setMetaType(meta.getMetaType());
                diMatrixConflictListResponse.setTerritoryName(meta.getTerritoryName());
                diMatrixConflictListResponse.setPartsParentId(meta.getPartsParentId());
                diMatrixConflictListResponse.setPartsParentName(meta.getPartsParentName());
                if(meta.getId() == null){
                    Long count = diMatrixTerritoryMetaMapper.selectCount(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).eq(DiMatrixTerritoryMeta::getMetaType, meta.getMetaType()).eq(DiMatrixTerritoryMeta::getTerritoryName, meta.getTerritoryName()));
                    if(count > 0){
                        diMatrixConflictListResponse.setFlag(true);
                    }
                }else{

                    diMatrixConflictListResponse.setId(meta.getId());
                    DiMatrixTerritoryMeta diMatrixTerritoryMeta = diMatrixTerritoryMetaMapper.selectById(meta.getId());
                    //名称不一样或已删除为冲突
                    if((!diMatrixTerritoryMeta.getTerritoryName().equals(meta.getTerritoryName()) && diMatrixTerritoryMeta.getVersion().longValue() > meta.getVersion()) || diMatrixTerritoryMeta.getDelFlag() == 1){
                        diMatrixConflictListResponse.setFlag(true);
                    }else {
                        Long count = diMatrixTerritoryMetaMapper.selectCount(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).eq(DiMatrixTerritoryMeta::getMetaType, meta.getMetaType()).eq(DiMatrixTerritoryMeta::getTerritoryName, meta.getTerritoryName()).ne(DiMatrixTerritoryMeta::getId, meta.getId()));
                        if (count > 0) {
                            diMatrixConflictListResponse.setFlag(true);
                        }
                    }
                }
                diMatrixConflictListResponseList.add(diMatrixConflictListResponse);
            }
        }

        List<DiMatrixConflictListResponse> diMatrixConflictListResponses = diMatrixConflictListResponseList.stream().filter(diMatrixConflictListResponse -> diMatrixConflictListResponse.getFlag()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(diMatrixConflictListResponses)){
            List<Long> conflictIds = diMatrixConflictListResponseList.stream().filter(diMatrixConflictListResponse -> Objects.nonNull(diMatrixConflictListResponse.getId())).map(DiMatrixConflictListResponse::getId).collect(Collectors.toList());
            diMatrixTerritoryMetas = diMatrixTerritoryMetas.stream().filter(diMatrixTerritoryMeta -> !conflictIds.contains(diMatrixTerritoryMeta.getId())).collect(Collectors.toList());
            for(DiMatrixTerritoryMeta diMatrixTerritoryMeta:diMatrixTerritoryMetas){
                DiMatrixConflictListResponse diMatrixConflictListResponse = new DiMatrixConflictListResponse();
                diMatrixConflictListResponse.setMetaType(diMatrixTerritoryMeta.getMetaType());
                diMatrixConflictListResponse.setTerritoryName(diMatrixTerritoryMeta.getTerritoryName());
                diMatrixConflictListResponse.setVersion(diMatrixTerritoryMeta.getVersion());
                diMatrixConflictListResponse.setId(diMatrixTerritoryMeta.getId());
                if(Objects.nonNull(diMatrixTerritoryMeta.getPartsParentId())) {
                    diMatrixConflictListResponse.setPartsParentId(diMatrixTerritoryMeta.getPartsParentId());
                    DiMatrixTerritoryMeta diMatrixTerritoryMetaT = diMatrixTerritoryMetaMapper.selectById(diMatrixTerritoryMeta.getPartsParentId());

                    if(Objects.nonNull(diMatrixTerritoryMetaT)) {
                        diMatrixConflictListResponse.setPartsParentName(diMatrixTerritoryMetaT.getTerritoryName());
                    }
                }
                diMatrixConflictListResponse.setFlag(false);
                diMatrixConflictListResponse.setId(diMatrixTerritoryMeta.getId());
                diMatrixConflictListResponseList.add(diMatrixConflictListResponse);
            }
            return diMatrixConflictListResponseList;
        }
        return null;
    }*/

    /**
     * 查询版图列表
     * @return
     */
    @Override
    public List<MatrixTerritoryMetasResponse> selectMatrixTerritoryMetas(Integer metaType,Long territoryPartT,Long territoryPartCategoryT) {

        //列表
        List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = new ArrayList<>();

        if(Objects.nonNull(territoryPartCategoryT)){
            DiMatrixTerritoryMeta diMatrixTerritoryMeta = diMatrixTerritoryMetaMapper.selectById(territoryPartCategoryT);
            diMatrixTerritoryMetas.add(diMatrixTerritoryMetaMapper.selectById(diMatrixTerritoryMeta.getPartsParentId()));
        }else if(Objects.nonNull(territoryPartT)){
            diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery()
                    .eq(Objects.nonNull(territoryPartT),DiMatrixTerritoryMeta::getPartsParentId,territoryPartT).eq(DiMatrixTerritoryMeta::getDelFlag,0));
        }else{
            diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0)
                    .eq(Objects.nonNull(metaType), DiMatrixTerritoryMeta::getMetaType, metaType)
                    .orderByDesc(DiMatrixTerritoryMeta::getCreateTime));
        }
        //业务版图
        List<DiMatrixTerritory> diMatrixTerritories = diMatrixTerritoryMapper.selectList(Wrappers.<DiMatrixTerritory>lambdaQuery().eq(DiMatrixTerritory::getDelFlag, 0));
        if (CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)) {

            return diMatrixTerritoryMetas.stream().map(diMatrixTerritoryMeta -> {
                MatrixTerritoryMetasResponse meta = new MatrixTerritoryMetasResponse();
                meta.setMetaType(diMatrixTerritoryMeta.getMetaType());
                meta.setTerritoryName(diMatrixTerritoryMeta.getTerritoryName());
                meta.setId(diMatrixTerritoryMeta.getId());
                //领域
                if (1 == diMatrixTerritoryMeta.getMetaType()) {
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryDomains = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryId = diMatrixTerritory.getTerritoryDomain();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryId != null && id != null && territoryId == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryDomains)) {
                            meta.setLinkCount(territoryDomains.size());
                        }

                    }
                } else if (2 == diMatrixTerritoryMeta.getMetaType()) {//行业
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryIndustrys = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryId = diMatrixTerritory.getTerritoryIndustry();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryId != null && id != null && territoryId == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryIndustrys)) {
                            meta.setLinkCount(territoryIndustrys.size());
                        }

                    }
                } else if (3 == diMatrixTerritoryMeta.getMetaType()) {//应用
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryApplications = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryId = diMatrixTerritory.getTerritoryApplication();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryId != null && id != null && territoryId == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryApplications)) {
                            meta.setLinkCount(territoryApplications.size());
                        }

                    }
                } else if (4 == diMatrixTerritoryMeta.getMetaType()) {//对象
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryObjects = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryId = diMatrixTerritory.getTerritoryObject();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryId != null && id != null && territoryId == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryObjects)) {
                            meta.setLinkCount(territoryObjects.size());
                        }

                    }
                } else if (5 == diMatrixTerritoryMeta.getMetaType()) {//工艺
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryProcess = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryId = diMatrixTerritory.getTerritoryProcess();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryId != null && id != null && territoryId == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryProcess)) {
                            meta.setLinkCount(territoryProcess.size());
                        }
                    }
                }else if (6 == diMatrixTerritoryMeta.getMetaType()) {//配件
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {

                        List<DiMatrixTerritory> territoryParts = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryPart = diMatrixTerritory.getTerritoryPart();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryPart != null && id != null && territoryPart == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryParts)) {
                            meta.setLinkCount(territoryParts.size());
                        }
                    }
                    /*if(Objects.nonNull(territoryPartCategoryT)){
                        DiMatrixTerritoryMeta diMatrixTerritoryMetaT = diMatrixTerritoryMetaMapper.selectById(diMatrixTerritoryMeta.getPartsParentId());
                        if(Objects.nonNull(diMatrixTerritoryMetaT)){
                            meta.setId(diMatrixTerritoryMetaT.getId());
                            meta.setTerritoryName(diMatrixTerritoryMetaT.getTerritoryName());
                        }
                    }*/
                }else if (7 == diMatrixTerritoryMeta.getMetaType()) {//配件品类
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryPartsCategory = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryPartCategory = diMatrixTerritory.getTerritoryPartCategory();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryPartCategory != null && id != null && territoryPartCategory == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryPartsCategory)) {
                            meta.setLinkCount(territoryPartsCategory.size());
                        }
                        meta.setPartsParentId(diMatrixTerritoryMeta.getPartsParentId());
                        DiMatrixTerritoryMeta diMatrixTerritoryMetaT = diMatrixTerritoryMetaMapper.selectById(diMatrixTerritoryMeta.getPartsParentId());
                        if(Objects.nonNull(diMatrixTerritoryMetaT)) {
                            meta.setPartsParentName(diMatrixTerritoryMetaT.getTerritoryName());
                        }
                    }
                }else if (8 == diMatrixTerritoryMeta.getMetaType()) {//配件品类
                    if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
                        List<DiMatrixTerritory> territoryComponents = diMatrixTerritories.stream().filter(diMatrixTerritory -> {
                            Long territoryComponent = diMatrixTerritory.getTerritoryComponent();
                            Long id = diMatrixTerritoryMeta.getId();
                            return territoryComponent != null && id != null && territoryComponent == id.longValue();
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(territoryComponents)) {
                            meta.setLinkCount(territoryComponents.size());
                        }

                    }
                }
                return meta;
            }).collect(Collectors.toList());

        }
        return null;
    }

    /**
     * 版图设置下拉
     * @param
     * @return
     */
    @Override
    public List<MatrixTerritoryMetasResponse> selectMetas(MatrixTerritoryMetasXLRequest request) {

        //类型
        Integer metaType = request.getMetaType();
        //查询业务版图
        List<DiMatrixTerritory> diMatrixTerritories = diMatrixTerritoryMapper.selectList(Wrappers.<DiMatrixTerritory>lambdaQuery()
                .eq(DiMatrixTerritory::getDelFlag, 0)
        .eq(Objects.nonNull(request.getTerritoryDomain()),DiMatrixTerritory::getTerritoryDomain,request.getTerritoryDomain())
        .eq(Objects.nonNull(request.getTerritoryIndustry()),DiMatrixTerritory::getTerritoryIndustry,request.getTerritoryIndustry())
        .eq(Objects.nonNull(request.getTerritoryApplication()),DiMatrixTerritory::getTerritoryApplication,request.getTerritoryApplication())
        .eq(Objects.nonNull(request.getTerritoryObject()),DiMatrixTerritory::getTerritoryObject,request.getTerritoryObject())
        .eq(Objects.nonNull(request.getTerritoryProcess()),DiMatrixTerritory::getTerritoryProcess,request.getTerritoryProcess())
        .eq(StringUtils.isNotEmpty(request.getBizName()),DiMatrixTerritory::getBizName,request.getBizName())
        .eq(Objects.nonNull(request.getTerritoryPart()),DiMatrixTerritory::getTerritoryPart,request.getTerritoryPart())
        .eq(Objects.nonNull(request.getTerritoryPartCategory()),DiMatrixTerritory::getTerritoryPartCategory,request.getTerritoryPartCategory()));

        if(CollectionUtils.isNotEmpty(diMatrixTerritories)) {
            List<Long> ids = null;

            //领域
            if (1 == metaType) {
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryDomain).collect(Collectors.toList());
            } else if (2 == metaType) {//行业
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryIndustry).collect(Collectors.toList());
            } else if (3 == metaType) {//应用
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryApplication).collect(Collectors.toList());
            } else if (4 == metaType) {//对象
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryObject).collect(Collectors.toList());
            } else if (5 == metaType) {//工艺
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryProcess).collect(Collectors.toList());
            }else if(6 == metaType){//配件
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryPart).collect(Collectors.toList());
            }else if(7 == metaType){//配件品类
                ids = diMatrixTerritories.stream().map(DiMatrixTerritory::getTerritoryPartCategory).collect(Collectors.toList());
            }



            if(CollectionUtils.isNotEmpty(ids)){
                List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0).in(DiMatrixTerritoryMeta::getId, ids));
                if(CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)){
                    return diMatrixTerritoryMetas.stream().map(diMatrixTerritoryMeta -> {
                        MatrixTerritoryMetasResponse matrixTerritoryMetasResponse = new MatrixTerritoryMetasResponse();
                        matrixTerritoryMetasResponse.setId(diMatrixTerritoryMeta.getId());
                        matrixTerritoryMetasResponse.setTerritoryName(diMatrixTerritoryMeta.getTerritoryName());
                        return matrixTerritoryMetasResponse;
                    }).collect(Collectors.toList());
                }
            }
        }
        return null;
    }

    @Override
    public List<DiMatrixConflictListResponse> checkMetaConflict(MatrixTerritoryMetaSaveRequest request) {
        List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0));

        if(CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)) {
            List<Long> requestIds = request.getMatrixMetas().stream().filter(meta -> Objects.nonNull(meta.getId())).map(MatrixTerritoryMetaSaveRequest.Meta::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(requestIds)) {
                if (requestIds.size() > diMatrixTerritoryMetas.size()) {

                    List<Long> ids = diMatrixTerritoryMetas.stream().map(DiMatrixTerritoryMeta::getId).collect(Collectors.toList());
                    List<MatrixTerritoryMetaSaveRequest.Meta> metas = request.getMatrixMetas().stream().filter(meta -> Objects.nonNull(meta.getId()) && !ids.contains(meta.getId())).collect(Collectors.toList());
                    return metas.stream().map(meta -> {
                        DiMatrixConflictListResponse diMatrixConflictListResponse = new DiMatrixConflictListResponse();
                        diMatrixConflictListResponse.setId(meta.getId());
                        diMatrixConflictListResponse.setMetaType(meta.getMetaType());
                        diMatrixConflictListResponse.setTerritoryName(meta.getTerritoryName());
                        diMatrixConflictListResponse.setPartsParentId(meta.getPartsParentId());
                        diMatrixConflictListResponse.setPartsParentName(meta.getPartsParentName());
                        diMatrixConflictListResponse.setFlag(1);
                        return diMatrixConflictListResponse;
                    }).collect(Collectors.toList());
                } else if (requestIds.size() < diMatrixTerritoryMetas.size()) {
                    diMatrixTerritoryMetas.stream().filter(diMatrixTerritoryMeta -> !requestIds.contains(diMatrixTerritoryMeta.getId())).collect(Collectors.toList()).stream().map(diMatrixTerritoryMeta -> {
                        DiMatrixConflictListResponse diMatrixConflictListResponse = new DiMatrixConflictListResponse();
                        diMatrixConflictListResponse.setId(diMatrixTerritoryMeta.getId());
                        diMatrixConflictListResponse.setMetaType(diMatrixTerritoryMeta.getMetaType());
                        diMatrixConflictListResponse.setTerritoryName(diMatrixTerritoryMeta.getTerritoryName());
                        diMatrixConflictListResponse.setFlag(0);
                        return diMatrixConflictListResponse;
                    }).collect(Collectors.toList());
                }
            }

        }

        return null;
    }
}
