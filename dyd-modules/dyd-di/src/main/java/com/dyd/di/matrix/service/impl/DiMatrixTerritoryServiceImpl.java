package com.dyd.di.matrix.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.label.pojo.LabelWarehouseDetailDTO;
import com.dyd.di.label.domain.DiLabelClassification;
import com.dyd.di.label.domain.DiLabelWarehouse;
import com.dyd.di.label.service.IDiLabelClassificationService;
import com.dyd.di.label.service.IDiLabelWarehouseService;
import com.dyd.di.marketing.domain.DiTerritoryMapping;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.service.MarketingTerritoryMappingService;
import com.dyd.di.matrix.convert.MatrixConvert;
import com.dyd.di.matrix.domain.*;
import com.dyd.di.matrix.dto.AddAndEditTerritoryResponse;
import com.dyd.di.matrix.entity.DiMatrixTerritoryMeta;
import com.dyd.di.matrix.enums.LabelClassEnum;
import com.dyd.di.matrix.enums.RelationTypeEnum;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryCompetitorAnalysisMapper;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryMapper;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryMarketingStrategyMapper;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryMetaMapper;
import com.dyd.di.matrix.service.*;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.domain.SysDictData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【di_matrix_territory(业务版图)】的数据库操作Service实现
 * @createDate 2025-03-19 16:00:28
 */
@Service
public class DiMatrixTerritoryServiceImpl extends ServiceImpl<DiMatrixTerritoryMapper, DiMatrixTerritory>
        implements DiMatrixTerritoryService {

    @Autowired
    private DiMatrixTerritoryMapper diMatrixTerritoryMapper;

    @Autowired
    private DiMatrixTerritoryMetaMapper diMatrixTerritoryMetaMapper;

    @Autowired
    private DiMatrixLabelService diMatrixLabelService;

    @Autowired
    private IDiLabelWarehouseService diLabelWarehouseService;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private IDiLabelClassificationService diLabelClassificationService;

    @Autowired
    private DiMatrixFileTypeService diMatrixFileTypeService;

    @Autowired
    private DiMatrixFileService diMatrixFileService;

    @Autowired
    private DiMatrixFileExtendService diMatrixFileExtendService;


    @Autowired
    private DiTerritoryMappingMapper territoryMappingMapper;

    @Autowired
    private DiMatrixTerritoryCompetitorAnalysisMapper diMatrixTerritoryCompetitorAnalysisMapper;

    @Autowired
    private DiMatrixTerritoryMarketingStrategyMapper diMatrixTerritoryMarketingStrategyMapper;

    /**
     * 保存，编辑业务版图
     *
     * @param request
     */
    @Transactional
    @Override
    public AddAndEditTerritoryResponse addAndEditTerritory(MatrixTerritorySaveAndEditRequest request) {

        DiMatrixTerritory diMatrixTerritory = new DiMatrixTerritory();
        diMatrixTerritory.setCateId(request.getCateId());
        diMatrixTerritory.setOwner(request.getOwner());
        diMatrixTerritory.setDept(request.getDept());
        diMatrixTerritory.setIndustryDesc(request.getIndustryDesc());
        diMatrixTerritory.setBizName(request.getBizName());
        diMatrixTerritory.setTerritoryLevel(request.getTerritoryLevel());
        if (request.getCateId() != 3) {
            diMatrixTerritory.setTerritoryDomain(request.getTerritoryDomain());
            diMatrixTerritory.setTerritoryIndustry(request.getTerritoryIndustry());
            diMatrixTerritory.setTerritoryApplication(request.getTerritoryApplication());
            diMatrixTerritory.setTerritoryObject(request.getTerritoryObject());
            diMatrixTerritory.setTerritoryProcess(request.getTerritoryProcess());
            diMatrixTerritory.setTerritoryComponent(request.getTerritoryComponent());
            diMatrixTerritory.setDomainTwoLevel(request.getDomainTwoLevel());
        } else {
            diMatrixTerritory.setTerritoryPart(request.getTerritoryPart());
            diMatrixTerritory.setTerritoryPartCategory(request.getTerritoryPartCategory());
        }
        AddAndEditTerritoryResponse addAndEditTerritoryResponse = new AddAndEditTerritoryResponse();

        //id为空，新增
        if (Objects.isNull(request.getId())) {

            Long count = diMatrixTerritoryMapper.selectCount(Wrappers.<DiMatrixTerritory>lambdaQuery().eq(DiMatrixTerritory::getTerritoryDomain, diMatrixTerritory.getTerritoryDomain()).eq(DiMatrixTerritory::getTerritoryIndustry, diMatrixTerritory.getTerritoryIndustry())
                    .eq(DiMatrixTerritory::getTerritoryApplication, diMatrixTerritory.getTerritoryApplication()).eq(DiMatrixTerritory::getTerritoryObject, diMatrixTerritory.getTerritoryObject())
                    .eq(DiMatrixTerritory::getTerritoryProcess, diMatrixTerritory.getTerritoryProcess()));
            if (count > 0) {
                throw new RuntimeException("该组合业务版图已存在");
            }

            Long countT = diMatrixTerritoryMapper.selectCount(Wrappers.<DiMatrixTerritory>lambdaQuery().eq(DiMatrixTerritory::getBizName, diMatrixTerritory.getBizName()));
            if (countT > 0) {
                throw new RuntimeException("该业务名称已存在");
            }

            diMatrixTerritoryMapper.insert(diMatrixTerritory);
            addAndEditTerritoryResponse.setId(diMatrixTerritory.getId());
            if (CollectionUtils.isNotEmpty(request.getDiMatrixLabels())) {
                request.getDiMatrixLabels().forEach(x -> x.setRelationId(diMatrixTerritory.getId()));
            }

            List<DiMatrixFileType> fileTypes = new ArrayList<>();
            fileTypes.addAll(request.getIntroductionFileTypes());
            fileTypes.addAll(request.getPhotosFileTypes());
            fileTypes.addAll(request.getMouthFileTypes());
            addFile(fileTypes,diMatrixTerritory.getId());

        } else {

            addAndEditTerritoryResponse.setId(request.getId());
            List<DiMatrixTerritory> diMatrixTerritoryT = diMatrixTerritoryMapper.selectList(Wrappers.<DiMatrixTerritory>lambdaQuery().eq(DiMatrixTerritory::getTerritoryDomain, diMatrixTerritory.getTerritoryDomain()).eq(DiMatrixTerritory::getTerritoryIndustry, diMatrixTerritory.getTerritoryIndustry())
                    .eq(DiMatrixTerritory::getTerritoryApplication, diMatrixTerritory.getTerritoryApplication()).eq(DiMatrixTerritory::getTerritoryObject, diMatrixTerritory.getTerritoryObject())
                    .eq(DiMatrixTerritory::getTerritoryProcess, diMatrixTerritory.getTerritoryProcess())
                    .ne(DiMatrixTerritory::getId, request.getId())
            );

            if (CollectionUtils.isNotEmpty(diMatrixTerritoryT) ) {
                throw new RuntimeException("该组合业务版图已存在");
            }

            List<DiMatrixTerritory> diMatrixTerritoryTT = diMatrixTerritoryMapper.selectList(Wrappers.<DiMatrixTerritory>lambdaQuery().eq(DiMatrixTerritory::getBizName, diMatrixTerritory.getBizName())
                    .ne(DiMatrixTerritory::getId, request.getId()));
            if (CollectionUtils.isNotEmpty(diMatrixTerritoryTT)) {
                throw new RuntimeException("该业务名称已存在");
            }

            diMatrixTerritory.setId(request.getId());
            diMatrixTerritoryMapper.updateById(diMatrixTerritory);

           //修改文件扩展字段
           /*for (DiMatrixFileExtend file : request.getFileVo()){
               if(Objects.isNull(file.getId())){
                   if(StringUtils.isNotBlank(file.getThumbnailFileKey())){
                       diMatrixFileExtendService.save(file);
                   }
               }else{
                   diMatrixFileExtendService.updateById(file);
               }
           }*/

        }

        //图镇关联
        List<DiMatrixLabel> diMatrixLabels = request.getDiMatrixLabels();
        boolean hasDuplicates = hasDuplicates(diMatrixLabels, diMatrixLabel -> diMatrixLabel.getLabelId() + "-" + diMatrixLabel.getRelationId());
        if (hasDuplicates) {
            throw new RuntimeException("关联标签不允许重复");
        }
        for (DiMatrixLabel diMatrixLabel : diMatrixLabels) {
            diMatrixLabel.setRelationId(diMatrixTerritory.getId());
            diMatrixLabel.setRelationType(RelationTypeEnum.TERRITORY.getType());
            diMatrixLabel.setCreateBy(SecurityUtils.getUsername());
            diMatrixLabel.setCreateTime(DateUtils.getNowDate());
            if (Objects.isNull(diMatrixLabel.getId())) {
                diMatrixLabelService.save(diMatrixLabel);
            } else {
                diMatrixLabelService.updateById(diMatrixLabel);
            }
        }

        //竞品分析
        if(Objects.nonNull(request.getMatrixTerritoryCompetitorAnalysis())){

            if(Objects.isNull(request.getMatrixTerritoryCompetitorAnalysis().getMarketSize()) && Objects.isNull(request.getMatrixTerritoryCompetitorAnalysis().getIndustryTrends())
            && Objects.isNull(request.getMatrixTerritoryCompetitorAnalysis().getGp2ProfitMargin()) && StringUtils.isEmpty(request.getMatrixTerritoryCompetitorAnalysis().getCustomerAnalysis())){
                if(Objects.nonNull(request.getMatrixTerritoryCompetitorAnalysis().getId())){
                    diMatrixTerritoryCompetitorAnalysisMapper.deleteById(request.getMatrixTerritoryCompetitorAnalysis().getId());
                }
            }else {

                DiMatrixTerritoryCompetitorAnalysis diMatrixTerritoryCompetitorAnalysis = new DiMatrixTerritoryCompetitorAnalysis();
                diMatrixTerritoryCompetitorAnalysis.setTerritoryId(diMatrixTerritory.getId());
                diMatrixTerritoryCompetitorAnalysis.setMarketSize(request.getMatrixTerritoryCompetitorAnalysis().getMarketSize());
                diMatrixTerritoryCompetitorAnalysis.setIndustryTrends(request.getMatrixTerritoryCompetitorAnalysis().getIndustryTrends());
                diMatrixTerritoryCompetitorAnalysis.setGp2ProfitMargin(request.getMatrixTerritoryCompetitorAnalysis().getGp2ProfitMargin());
                diMatrixTerritoryCompetitorAnalysis.setCustomerAnalysis(request.getMatrixTerritoryCompetitorAnalysis().getCustomerAnalysis());
                if (Objects.nonNull(request.getMatrixTerritoryCompetitorAnalysis().getId())) {
                    diMatrixTerritoryCompetitorAnalysis.setId(request.getMatrixTerritoryCompetitorAnalysis().getId());
                    diMatrixTerritoryCompetitorAnalysisMapper.updateById(diMatrixTerritoryCompetitorAnalysis);
                } else {
                    diMatrixTerritoryCompetitorAnalysisMapper.insert(diMatrixTerritoryCompetitorAnalysis);
                }
            }

        }

        //市场策略
        if(Objects.nonNull(request.getMatrixTerritoryMarketingStrategy())){

            if(Objects.isNull(request.getMatrixTerritoryMarketingStrategy().getDistributionChannel()) && Objects.isNull(request.getMatrixTerritoryMarketingStrategy().getExecutionCycle())
             && StringUtils.isEmpty(request.getMatrixTerritoryMarketingStrategy().getOwner()) && StringUtils.isEmpty(request.getMatrixTerritoryMarketingStrategy().getSalesStrategy())
             && StringUtils.isEmpty(request.getMatrixTerritoryMarketingStrategy().getPriceStrategy()) && StringUtils.isEmpty(request.getMatrixTerritoryMarketingStrategy().getResourceMatchingStrategy())
             && StringUtils.isEmpty(request.getMatrixTerritoryMarketingStrategy().getProductStrategy())){
                if(Objects.nonNull(request.getMatrixTerritoryMarketingStrategy().getId())){
                    diMatrixTerritoryMarketingStrategyMapper.deleteById(request.getMatrixTerritoryMarketingStrategy().getId());
                }
            }else {

                DiMatrixTerritoryMarketingStrategy diMatrixTerritoryMarketingStrategy = new DiMatrixTerritoryMarketingStrategy();
                diMatrixTerritoryMarketingStrategy.setTerritoryId(diMatrixTerritory.getId());
                diMatrixTerritoryMarketingStrategy.setDistributionChannel(request.getMatrixTerritoryMarketingStrategy().getDistributionChannel());
                diMatrixTerritoryMarketingStrategy.setExecutionCycle(request.getMatrixTerritoryMarketingStrategy().getExecutionCycle());
                diMatrixTerritoryMarketingStrategy.setOwner(request.getMatrixTerritoryMarketingStrategy().getOwner());
                diMatrixTerritoryMarketingStrategy.setSalesStrategy(request.getMatrixTerritoryMarketingStrategy().getSalesStrategy());
                diMatrixTerritoryMarketingStrategy.setPriceStrategy(request.getMatrixTerritoryMarketingStrategy().getPriceStrategy());
                diMatrixTerritoryMarketingStrategy.setResourceMatchingStrategy(request.getMatrixTerritoryMarketingStrategy().getResourceMatchingStrategy());
                diMatrixTerritoryMarketingStrategy.setProductStrategy(request.getMatrixTerritoryMarketingStrategy().getProductStrategy());
                if (Objects.nonNull(request.getMatrixTerritoryMarketingStrategy().getId())) {
                    diMatrixTerritoryMarketingStrategy.setId(request.getMatrixTerritoryMarketingStrategy().getId());
                    diMatrixTerritoryMarketingStrategyMapper.updateById(diMatrixTerritoryMarketingStrategy);
                } else {
                    diMatrixTerritoryMarketingStrategyMapper.insert(diMatrixTerritoryMarketingStrategy);
                }
            }
        }

        return addAndEditTerritoryResponse;
    }

    public void addFile(List<DiMatrixFileType> diMatrixFileTypes,Long belongingId){
        //判断是否有重复文件分类
        boolean typeName = diMatrixFileTypes.stream()
                .collect(Collectors.groupingBy(type -> type.getTypeName() + "_" + type.getBelonging()))
                .values().stream().anyMatch(group -> group.size() > 1);
        if (typeName) {
            throw new RuntimeException("分类名称不允许重复");
        }

        for (DiMatrixFileType diMatrixFileType : diMatrixFileTypes) {
            diMatrixFileType.setBelongingId(belongingId);
            diMatrixFileType.setCreateBy(SecurityUtils.getUsername());
            diMatrixFileType.setCreateTime(DateUtils.getNowDate());
            diMatrixFileTypeService.save(diMatrixFileType);
            if (CollectionUtil.isNotEmpty(diMatrixFileType.getFileVo())) {
                for (DiMatrixFileExtend fileVo : diMatrixFileType.getFileVo()) {
                    DiMatrixFile diMatrixFile = new DiMatrixFile();
                    diMatrixFile.setBelonging("0");
                    diMatrixFile.setBelongingId(diMatrixFileType.getId());
                    diMatrixFile.setFileKey(fileVo.getFileKey());
                    diMatrixFile.setCreateBy(SecurityUtils.getUsername());
                    diMatrixFile.setCreateTime(DateUtils.getNowDate());
                    diMatrixFileService.save(diMatrixFile);
                    if(StringUtils.isNotBlank(fileVo.getThumbnailFileKey())){
                        diMatrixFileExtendService.save(fileVo);
                    }
                }
            }
        }
    }
    public static <T> boolean hasDuplicates(List<T> list, Function<T, ?> keyExtractor) {
        return list.stream()
                .collect(Collectors.groupingBy(keyExtractor))
                .values()
                .stream()
                .anyMatch(group -> group.size() > 1);
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public MatrixTerritoryInfoResponse getMatrixTerritoryInfo(Long id) {
        DiMatrixTerritory diMatrixTerritory = diMatrixTerritoryMapper.selectById(id);
        if (Objects.isNull(diMatrixTerritory)) {
            throw new RuntimeException("id无效");
        }
        MatrixTerritoryInfoResponse matrixTerritoryInfoResponse = MatrixConvert.INSTANCE.territoryConvertToInfo(diMatrixTerritory);

        Map<Long, DiMatrixTerritoryMeta> diMatrixTerritoryMetaMap = diMatrixTerritoryMetaMapper.selectList(null).stream().collect(Collectors.toMap(DiMatrixTerritoryMeta::getId, Function.identity()));
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryDomain()))) {
            matrixTerritoryInfoResponse.setTerritoryDomainName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryDomain()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryApplication()))) {
            matrixTerritoryInfoResponse.setTerritoryApplicationName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryApplication()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryIndustry()))) {
            matrixTerritoryInfoResponse.setTerritoryIndustryName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryIndustry()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryObject()))) {
            matrixTerritoryInfoResponse.setTerritoryObjectName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryObject()).getTerritoryName());
        }

        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryProcess()))) {
            matrixTerritoryInfoResponse.setTerritoryProcessName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryProcess()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryPart()))) {
            matrixTerritoryInfoResponse.setTerritoryPartName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryPart()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryPartCategory()))) {
            matrixTerritoryInfoResponse.setTerritoryPartCategoryName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryPartCategory()).getTerritoryName());
        }
        if (Objects.nonNull(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryComponent()))) {
            matrixTerritoryInfoResponse.setTerritoryComponentName(diMatrixTerritoryMetaMap.get(diMatrixTerritory.getTerritoryComponent()).getTerritoryName());
        }



        matrixTerritoryInfoResponse.setDomainTwoLevel(diMatrixTerritory.getDomainTwoLevel());
        matrixTerritoryInfoResponse.setIndustryDesc(diMatrixTerritory.getIndustryDesc());
        matrixTerritoryInfoResponse.setCreateTime(diMatrixTerritory.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        R<List<SysDictData>> territoryLevelR = remoteDictDataService.dictTypeNew("territory_level");
        if (territoryLevelR.isSuccess() && CollectionUtils.isNotEmpty(territoryLevelR.getData())) {
            Map<String, SysDictData> stringSysDictDataMap = territoryLevelR.getData().stream().collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));
            if (Objects.nonNull(stringSysDictDataMap.get(diMatrixTerritory.getTerritoryLevel()))) {
                matrixTerritoryInfoResponse.setTerritoryLevelName(stringSysDictDataMap.get(diMatrixTerritory.getTerritoryLevel()).getDictLabel());
            }
        }

        R<List<SysDictData>> cateIdR = remoteDictDataService.dictTypeNew("cate_id");
        if (cateIdR.isSuccess() && CollectionUtils.isNotEmpty(cateIdR.getData())) {
            Map<String, SysDictData> stringSysDictDataMap = cateIdR.getData().stream().collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));
            if (Objects.nonNull(stringSysDictDataMap.get(diMatrixTerritory.getCateId()))) {
                matrixTerritoryInfoResponse.setCateIdName(stringSysDictDataMap.get(diMatrixTerritory.getCateId()).getDictLabel());
            }
        }

        QueryWrapper<DiMatrixLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", "0");
        queryWrapper.eq("relation_id", id);
        queryWrapper.eq("relation_type", 1);
        queryWrapper.orderByDesc("id");
        List<DiMatrixLabel> diMatrixLabels = diMatrixLabelService.list(queryWrapper);

        //基本资料业务版图
        List<DiMatrixLabel> diMatrixLabelList = new ArrayList<>();

        //技术范围--全量
        List<DiMatrixLabel> diMatrixLabelTs = new ArrayList<>();
        for (DiMatrixLabel diMatrixLabel : diMatrixLabels) {
            LabelWarehouseDetailDTO info = diLabelWarehouseService.getInfo(diMatrixLabel.getLabelId().toString());
            diMatrixLabel.setLabelWarehouseDetailDTO(info);
            diMatrixLabel.setClassificationName(diMatrixLabel.getLabelWarehouseDetailDTO().getClassificationName());
            diMatrixLabel.setLabelName(diMatrixLabel.getLabelWarehouseDetailDTO().getLabelName());
            diMatrixLabelTs.add(diMatrixLabel);
            if(Objects.isNull(info.getIsShowTerritory()) || info.getIsShowTerritory() == 1) {
                diMatrixLabelList.add(diMatrixLabel);
            }

        }


        matrixTerritoryInfoResponse.setDiMatrixLabels(diMatrixLabelList.stream()
                .sorted(Comparator.comparing(DiMatrixLabel::getClassificationName,Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(DiMatrixLabel::getLabelName,Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList()));

        matrixTerritoryInfoResponse.setDiMatrixLabelTs(diMatrixLabelTs.stream()
                .sorted(Comparator.comparing(DiMatrixLabel::getClassificationName,Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(DiMatrixLabel::getLabelName,Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList()));

        //竞品分析
        DiMatrixTerritoryCompetitorAnalysis diMatrixTerritoryCompetitorAnalyses = diMatrixTerritoryCompetitorAnalysisMapper.selectOne(Wrappers.<DiMatrixTerritoryCompetitorAnalysis>lambdaQuery().eq(DiMatrixTerritoryCompetitorAnalysis::getTerritoryId, id));
        if(Objects.nonNull(diMatrixTerritoryCompetitorAnalyses)){
            MatrixTerritoryInfoResponse.MatrixTerritoryCompetitorAnalysis matrixTerritoryCompetitorAnalysis = MatrixConvert.INSTANCE.analysisConver(diMatrixTerritoryCompetitorAnalyses);
            matrixTerritoryInfoResponse.setMatrixTerritoryCompetitorAnalysis(matrixTerritoryCompetitorAnalysis);
        }

        //销售策略
        DiMatrixTerritoryMarketingStrategy diMatrixTerritoryMarketingStrategy = diMatrixTerritoryMarketingStrategyMapper.selectOne(Wrappers.<DiMatrixTerritoryMarketingStrategy>lambdaQuery().eq(DiMatrixTerritoryMarketingStrategy::getTerritoryId, id));
        if(Objects.nonNull(diMatrixTerritoryMarketingStrategy)){
            MatrixTerritoryInfoResponse.MatrixTerritoryMarketingStrategy matrixTerritoryMarketingStrategy = MatrixConvert.INSTANCE.marketStrategyConver(diMatrixTerritoryMarketingStrategy);
            matrixTerritoryInfoResponse.setMatrixTerritoryMarketingStrategy(matrixTerritoryMarketingStrategy);
        }
        return matrixTerritoryInfoResponse;
    }


    @Override
    public List<MatrixTerritoryListResponse> getMatrixTerritoryList(MatrixTerritoryListRequest request) {
        LambdaQueryWrapper<DiMatrixTerritory> lambdaQuery = Wrappers.lambdaQuery();

        //根据线索或者商机ID查询对应的业务版图
        if (StringUtils.hasText(request.getType()) && StringUtils.hasText(request.getRefId())) {
            List<DiTerritoryMapping> mappingList = territoryMappingMapper.selectList(
                    new LambdaQueryWrapper<DiTerritoryMapping>()
                            .eq(DiTerritoryMapping::getRefId, request.getRefId()))
                    .stream()
                    .toList();
            return mappingList.stream()
                    .map(marketingTerritoryMapping -> JSON.parseObject(marketingTerritoryMapping.getTerritoryJson(), MatrixTerritoryListResponse.class))
                    .filter(matrixTerritoryListResponse -> {
                        if (Objects.isNull(matrixTerritoryListResponse)) {
                            return false;
                        }
                        if (Objects.nonNull(request.getTerritoryDomain())) {
                            if (!Objects.equals(request.getTerritoryDomain(), matrixTerritoryListResponse.getTerritoryDomain())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryIndustry())) {
                            if (!Objects.equals(request.getTerritoryIndustry(), matrixTerritoryListResponse.getTerritoryIndustry())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryApplication())) {
                            if (!Objects.equals(request.getTerritoryApplication(), matrixTerritoryListResponse.getTerritoryApplication())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryObject())) {
                            if (!Objects.equals(request.getTerritoryObject(), matrixTerritoryListResponse.getTerritoryObject())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryProcess())) {
                            if (!Objects.equals(request.getTerritoryProcess(), matrixTerritoryListResponse.getTerritoryProcess())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryComponent())) {
                            if (!Objects.equals(request.getTerritoryComponent(), matrixTerritoryListResponse.getTerritoryComponent())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getCateId())) {
                            if (!Objects.equals(request.getCateId(), matrixTerritoryListResponse.getCateId())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryPart())) {
                            if (!Objects.equals(request.getTerritoryPart(), matrixTerritoryListResponse.getTerritoryPart())) {
                                return false;
                            }
                        }
                        if (Objects.nonNull(request.getTerritoryPartCategory())) {
                            if (!Objects.equals(request.getTerritoryPartCategory(), matrixTerritoryListResponse.getCateId())) {
                                return false;
                            }
                        }
                        if (StringUtils.isNotEmpty(request.getBizName())) {
                            if (!matrixTerritoryListResponse.getBizName().contains(request.getBizName())) {
                                return false;
                            }
                        }
                        return true;
                    }).sorted((o1, o2) -> o2.getId().compareTo(o1.getId()))
                    .toList();
        }

        //查询业务版图
        List<DiMatrixTerritory> diMatrixTerritories = diMatrixTerritoryMapper.selectList(lambdaQuery
                .eq(Objects.nonNull(request.getTerritoryDomain()), DiMatrixTerritory::getTerritoryDomain, request.getTerritoryDomain())
                .eq(Objects.nonNull(request.getTerritoryIndustry()), DiMatrixTerritory::getTerritoryIndustry, request.getTerritoryIndustry())
                .eq(Objects.nonNull(request.getTerritoryApplication()), DiMatrixTerritory::getTerritoryApplication, request.getTerritoryApplication())
                .eq(Objects.nonNull(request.getTerritoryObject()), DiMatrixTerritory::getTerritoryObject, request.getTerritoryObject())
                .eq(Objects.nonNull(request.getTerritoryProcess()), DiMatrixTerritory::getTerritoryProcess, request.getTerritoryProcess())
                .eq(Objects.nonNull(request.getTerritoryComponent()),DiMatrixTerritory::getTerritoryComponent,request.getTerritoryComponent())
                .eq(Objects.nonNull(request.getCateId()), DiMatrixTerritory::getCateId, request.getCateId())
                .eq(Objects.nonNull(request.getTerritoryPart()), DiMatrixTerritory::getTerritoryPart, request.getTerritoryPart())
                .eq(Objects.nonNull(request.getTerritoryPartCategory()), DiMatrixTerritory::getTerritoryPartCategory, request.getTerritoryPartCategory())
                .like(StringUtils.isNotEmpty(request.getBizName()), DiMatrixTerritory::getBizName, request.getBizName())
                .orderByDesc(DiMatrixTerritory::getId)
        );
        if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
            //查询版图设置
            List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0));
            Map<Long, DiMatrixTerritoryMeta> metaMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)) {

                metaMap = diMatrixTerritoryMetas.stream().collect(Collectors.toMap(DiMatrixTerritoryMeta::getId, Function.identity()));
            }
            Map<Long, String> brandMap = this.queryTerritoryBrand(diMatrixTerritories);
            Map<Long, DiMatrixTerritoryMeta> finalMetaMap = metaMap;
            return diMatrixTerritories.stream().map(diMatrixTerritory -> {
                MatrixTerritoryListResponse matrixTerritoryListResponse = new MatrixTerritoryListResponse();
                matrixTerritoryListResponse.setTerritoryDomain(diMatrixTerritory.getTerritoryDomain());
                matrixTerritoryListResponse.setTerritoryDomainName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryDomain()));
                matrixTerritoryListResponse.setTerritoryIndustry(diMatrixTerritory.getTerritoryIndustry());
                matrixTerritoryListResponse.setTerritoryIndustryName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryIndustry()));
                matrixTerritoryListResponse.setTerritoryApplication(diMatrixTerritory.getTerritoryApplication());
                matrixTerritoryListResponse.setTerritoryApplicationName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryApplication()));
                matrixTerritoryListResponse.setTerritoryObject(diMatrixTerritory.getTerritoryObject());
                matrixTerritoryListResponse.setTerritoryObjectName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryObject()));
                matrixTerritoryListResponse.setTerritoryProcess(diMatrixTerritory.getTerritoryProcess());
                matrixTerritoryListResponse.setTerritoryProcessName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryProcess()));
                matrixTerritoryListResponse.setTerritoryPart(diMatrixTerritory.getTerritoryPart());
                matrixTerritoryListResponse.setTerritoryPartName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryPart()));
                matrixTerritoryListResponse.setTerritoryPartCategory(diMatrixTerritory.getTerritoryPartCategory());
                matrixTerritoryListResponse.setTerritoryPartCategoryName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryPartCategory()));
                matrixTerritoryListResponse.setTerritoryComponent(diMatrixTerritory.getTerritoryComponent());
                matrixTerritoryListResponse.setTerritoryComponentName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryComponent()));
                matrixTerritoryListResponse.setCateId(diMatrixTerritory.getCateId());
                matrixTerritoryListResponse.setTerritoryLevel(diMatrixTerritory.getTerritoryLevel());
                matrixTerritoryListResponse.setBizName(diMatrixTerritory.getBizName());
                matrixTerritoryListResponse.setDept(diMatrixTerritory.getDept());
                matrixTerritoryListResponse.setDeptName(diMatrixTerritory.getDeptName());
                matrixTerritoryListResponse.setOwnerName(diMatrixTerritory.getOwnerName());
                matrixTerritoryListResponse.setOwner(diMatrixTerritory.getOwner());
                matrixTerritoryListResponse.setCreateBy(diMatrixTerritory.getCreateBy());
                matrixTerritoryListResponse.setUpdateBy(diMatrixTerritory.getUpdateBy());
                matrixTerritoryListResponse.setCreateTime(diMatrixTerritory.getCreateTime());
                matrixTerritoryListResponse.setUpdateTime(diMatrixTerritory.getUpdateTime());
                matrixTerritoryListResponse.setId(diMatrixTerritory.getId());
                matrixTerritoryListResponse.setBrandName(brandMap.get(diMatrixTerritory.getId()));
                return matrixTerritoryListResponse;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public MatrixTerritoryListResponse getResponse(Long territoryId) {
        DiMatrixTerritory diMatrixTerritory = diMatrixTerritoryMapper.selectById(territoryId);

        List<DiMatrixTerritoryMeta> diMatrixTerritoryMetas = diMatrixTerritoryMetaMapper.selectList(Wrappers.<DiMatrixTerritoryMeta>lambdaQuery().eq(DiMatrixTerritoryMeta::getDelFlag, 0));
        Map<Long, DiMatrixTerritoryMeta> metaMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(diMatrixTerritoryMetas)) {
            metaMap = diMatrixTerritoryMetas.stream().collect(Collectors.toMap(DiMatrixTerritoryMeta::getId, Function.identity()));
        }
        Map<Long, String> brandMap = this.queryTerritoryBrand(Lists.newArrayList(diMatrixTerritory));
        Map<Long, DiMatrixTerritoryMeta> finalMetaMap = metaMap;

        MatrixTerritoryListResponse matrixTerritoryListResponse = new MatrixTerritoryListResponse();
        matrixTerritoryListResponse.setTerritoryDomain(diMatrixTerritory.getTerritoryDomain());
        matrixTerritoryListResponse.setTerritoryDomainName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryDomain()));
        matrixTerritoryListResponse.setTerritoryIndustry(diMatrixTerritory.getTerritoryIndustry());
        matrixTerritoryListResponse.setTerritoryIndustryName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryIndustry()));
        matrixTerritoryListResponse.setTerritoryApplication(diMatrixTerritory.getTerritoryApplication());
        matrixTerritoryListResponse.setTerritoryApplicationName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryApplication()));
        matrixTerritoryListResponse.setTerritoryObject(diMatrixTerritory.getTerritoryObject());
        matrixTerritoryListResponse.setTerritoryObjectName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryObject()));
        matrixTerritoryListResponse.setTerritoryProcess(diMatrixTerritory.getTerritoryProcess());
        matrixTerritoryListResponse.setTerritoryProcessName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryProcess()));
        matrixTerritoryListResponse.setTerritoryPart(diMatrixTerritory.getTerritoryPart());
        matrixTerritoryListResponse.setTerritoryPartName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryPart()));
        matrixTerritoryListResponse.setTerritoryPartCategory(diMatrixTerritory.getTerritoryPartCategory());
        matrixTerritoryListResponse.setTerritoryPartCategoryName(this.getMetaName(finalMetaMap, diMatrixTerritory.getTerritoryPartCategory()));
        matrixTerritoryListResponse.setCateId(diMatrixTerritory.getCateId());
        matrixTerritoryListResponse.setTerritoryLevel(diMatrixTerritory.getTerritoryLevel());
        matrixTerritoryListResponse.setBizName(diMatrixTerritory.getBizName());
        matrixTerritoryListResponse.setDept(diMatrixTerritory.getDept());
        matrixTerritoryListResponse.setDeptName(diMatrixTerritory.getDeptName());
        matrixTerritoryListResponse.setOwnerName(diMatrixTerritory.getOwnerName());
        matrixTerritoryListResponse.setOwner(diMatrixTerritory.getOwner());
        matrixTerritoryListResponse.setCreateBy(diMatrixTerritory.getCreateBy());
        matrixTerritoryListResponse.setUpdateBy(diMatrixTerritory.getUpdateBy());
        matrixTerritoryListResponse.setCreateTime(diMatrixTerritory.getCreateTime());
        matrixTerritoryListResponse.setUpdateTime(diMatrixTerritory.getUpdateTime());
        matrixTerritoryListResponse.setId(diMatrixTerritory.getId());
        matrixTerritoryListResponse.setBrandName(brandMap.get(diMatrixTerritory.getId()));
        return matrixTerritoryListResponse;
    }

    private Map<Long, String> queryTerritoryBrand(List<DiMatrixTerritory> diMatrixTerritories) {

        Map<Long, String> results = new HashMap<>();

        if (CollectionUtils.isNotEmpty(diMatrixTerritories)) {
            DiLabelClassification classification = diLabelClassificationService.queryByName(LabelClassEnum.Brand.getText());
            if (classification == null) {
                log.error("未查询到品牌分类");
                return results;
            }

            List<DiLabelWarehouse> diLabelWarehouses = diLabelWarehouseService.queryByClassificationId(classification.getId());
            if (CollectionUtils.isEmpty(diLabelWarehouses)) {
                log.error("未查询到品牌标签");
                return results;
            }
            Map<Long, DiLabelWarehouse> diLabelWarehouseMap = diLabelWarehouses.stream().collect(Collectors.toMap(DiLabelWarehouse::getId, Function.identity()));

            List<Long> territoryIds = diMatrixTerritories.stream().map(DiMatrixTerritory::getId).collect(Collectors.toList());
            List<Long> labelIds = diLabelWarehouses.stream().map(DiLabelWarehouse::getId).collect(Collectors.toList());
            List<DiMatrixLabel> diMatrixLabels = diMatrixLabelService.queryByTerritoryIds(territoryIds, labelIds);
            if (CollectionUtils.isNotEmpty(diMatrixLabels)) {
                Map<Long, List<DiMatrixLabel>> matrixLabelMap = diMatrixLabels.stream().collect(Collectors.groupingBy(DiMatrixLabel::getRelationId));
                for (DiMatrixTerritory diMatrixTerritory : diMatrixTerritories) {
                    if (matrixLabelMap.containsKey(diMatrixTerritory.getId())) {
                        String brandName = matrixLabelMap.get(diMatrixTerritory.getId()).stream().map(x -> diLabelWarehouseMap.get(x.getLabelId()).getLabelName())
                                .collect(Collectors.joining(","));
                        results.put(diMatrixTerritory.getId(), brandName);
                    }
                }
            }
        }
        return results;

    }

    private String getMetaName(Map<Long, DiMatrixTerritoryMeta> finalMetaMap, Long metaId) {
        if (finalMetaMap == null || finalMetaMap.isEmpty() || Objects.isNull(metaId) || Objects.isNull(finalMetaMap.get(metaId))) {
            return null;
        }
        return finalMetaMap.get(metaId).getTerritoryName();
    }
}
