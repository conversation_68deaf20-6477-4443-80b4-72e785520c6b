package com.dyd.di.order.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.contract.entity.DiContract;
import com.dyd.di.eventbus.AbstractDydEventListener;
import com.dyd.di.marketing.domain.DiMarketingNicheDemand;
import com.dyd.di.marketing.service.DiMarketingNicheDemandService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.domain.DiOrderBillPeriod;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.event.*;
import com.dyd.di.order.service.IDiOrderBillPeriodService;
import com.dyd.di.order.service.IDiOrderService;
import com.dyd.di.order.service.OrderCommonService;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteUserService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service()
@Slf4j
@RefreshScope()
public class OrderUpdateSystemsEngineerEventListener {

    @Autowired
    private IDiOrderService orderService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private IDiProcessProjectService projectService;

    @Autowired
    private RemoteDictDataService dictDataService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DiMarketingNicheDemandService demandService;


    @Autowired
    private RemoteUserService userService;

    @Autowired
    private IDiOrderBillPeriodService periodService;

    @Autowired
    private com.dyd.di.contract.iservice.IDiContractService diContractService;


//    @EventListener
//    public void onDiOrderCreatedEventHandler(DiOrderCreatedEvent event) {
//
//        DiOrder order = orderService.selectDiOrderById(String.valueOf(event.getOrderId()));
//
//        String notifyUser = defaultUser;
//        DiMarketingNicheDemand nicheDemand = demandService.queryByNicheNo(order.getNicheNo());
//        if (nicheDemand != null) {
//            String userId = dictDataService.queryDictLabel("order_schedule_user", nicheDemand.getIndustry());
//            if (StringUtils.isNotBlank(userId)) {
//                notifyUser = userId;
//            }
//        }
//        DiOrder update = new DiOrder();
//        update.setId(order.getId());
//        update.setSystemsEngineer(notifyUser);
//        orderService.updateById(update);
//
//        OrderUpdateSystemsEngineerEvent updateEvent = new OrderUpdateSystemsEngineerEvent();
//        updateEvent.setOrderId(event.getOrderId());
//        updateEvent.setOldSystemsEngineer(null);
//        updateEvent.setNewSystemsEngineer(notifyUser);
//        handleTodo(updateEvent, order);
//    }


    @Service
    class OrderManagerUpdateEventListener extends AbstractDydEventListener<OrderManagerUpdateEvent> {
        @EventListener
        @Override
        public void onEvent(OrderManagerUpdateEvent event) {
            //if (StringUtils.isBlank(event.getOldManager()) && StringUtils.isNotBlank(event.getNewManager())) {
            checkSendNotice(event.getOrderId());
            //}
        }
    }

    @Service
    class OrderManagerUpdateEventListener2 extends AbstractDydEventListener<OrderActualPaymentEvent> {
        @EventListener
        @Override
        public void onEvent(OrderActualPaymentEvent event) {
            checkSendNotice(event.getOrderId());
        }
    }

    @Service
    class OrderAdvanceExecutionEventListener2 extends AbstractDydEventListener<OrderAdvanceExecutionEvent> {
        @EventListener
        @Override
        public void onEvent(OrderAdvanceExecutionEvent event) {
            checkSendNotice(event.getOrderId());
        }
    }


    @EventListener
    public void onOrderUpdateSystemsEngineerEventHandler(OrderUpdateSystemsEngineerEvent event) {
        DiOrder order = orderService.selectDiOrderById(String.valueOf(event.getOrderId()));
        handleTodo(event, order);
    }

    public void checkSendNotice(Long orderId) {
        DiOrder order = orderService.getById(orderId);

        List<DiOrderBillPeriod> periodList = periodService.queryByOrderNo(order.getOrderNo());
        DiContract contract = diContractService.queryByNo(order.getContractNo());
        boolean payed = periodList.stream().anyMatch(item -> Objects.nonNull(item.getRealPayDate()));
        boolean hasManager = StringUtils.isNotBlank(order.getProjectManagerUserId());
        boolean advanceExecution = "2".equals(contract.getIsAdvanceExecution()) || "2".equals(order.getIsAdvanceExecution());
        boolean done = hasManager && (payed || advanceExecution);
        if (!done) {
            log.info("订单:{} 排期条件不满足:    1. 已设定项目经理:{},    2. 已绑定预付款:{} 或提前执行:{}", orderId, hasManager, payed, advanceExecution);
            return;
        }
        if (StringUtils.isNotBlank(order.getSystemsEngineer())) {
            log.info("订单:{}，系统工程师已经设定，不在重复发送通知", orderId);
            return;
        }

        String notifyUser = orderService.getSystemEngineer(order);
        DiOrder update = new DiOrder();
        update.setId(order.getId());
        update.setSystemsEngineer(notifyUser);
        orderService.updateById(update);


        OrderUpdateSystemsEngineerEvent event = new OrderUpdateSystemsEngineerEvent();
        event.setOrderId(order.getId());
        event.setOldSystemsEngineer(null);
        event.setNewSystemsEngineer(notifyUser);
        handleTodo(event, order);
    }



    public void handleTodo(OrderUpdateSystemsEngineerEvent event, DiOrder order) {

        if (Objects.equals(event.getOldSystemsEngineer(), event.getNewSystemsEngineer())) {
            log.info("订单:{},系统工程师:{},没有变化，不处理代办", event.getOrderId(), event.getNewSystemsEngineer());
            return;
        }

        DiProcessProject project = projectService.selectByNo(order.getProjectNo());
        projectService.update(Wrappers.<DiProcessProject>lambdaUpdate()
                .set(DiProcessProject::getSystemsEngineer, event.getNewSystemsEngineer())
                .eq(DiProcessProject::getProjectNo, project.getProjectNo()));

        if (StringUtils.isNotBlank(event.getOldSystemsEngineer())) {
            //删除待办任务
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            //agencyTaskInfoDto.setLiabilityByList(Collections.singletonList(event.getOldSystemsEngineer()));
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.ORDER_SYSTEMS_ENGINEER);
            agencyTaskInfoDto.setBusinessKey(order.getOrderNo());
            agencyTaskInfoDto.setJumpKey(order.getOrderNo());
            agencyTaskInfoDto.setType("2");
            String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            //移除权限
            commonService.personnelSaveProject(order.getProjectNo(), Lists.newArrayList(event.getOldSystemsEngineer()), "2");
        }

        if (StringUtils.isNotBlank(event.getNewSystemsEngineer())) {
            //添加待办任务
            orderCommonService.saveAgencyTask(order.getOrderNo(), order.getOrderNo(), order.getProjectNo(), order.getProjectNo(), AgencyTaskTypeEnum.ORDER_SYSTEMS_ENGINEER, project.getProjectName(), dictDataService.queryDictLabel("order_stage", String.valueOf(order.getOrderStage())), Collections.singletonList(event.getNewSystemsEngineer()), null != order.getOrderDeliveryDate() ? order.getOrderDeliveryDate() : null);

            String title = "";
            String content = "";
            if (StringUtils.isNotBlank(event.getOldSystemsEngineer())) {
                //更新
                title = String.format(OrderDingTalkEnum.ORDER_SYSTEM_ENGINEER_UPDATE.getTitle(), order.getProjectNo());
                content = String.format(OrderDingTalkEnum.ORDER_SYSTEM_ENGINEER_UPDATE.getMessage(), order.getProjectNo());
            } else {
                //新增
                DiMarketingNicheDemand nicheDemand = demandService.queryByNicheNo(order.getCustomerName());
                title = String.format(OrderDingTalkEnum.ORDER_SYSTEM_ENGINEER_INIT.getTitle(), order.getProjectNo());
                content = String.format(OrderDingTalkEnum.ORDER_SYSTEM_ENGINEER_INIT.getMessage(), userService.queryUserNickName(project.getSaleUserId()), order.getCustomerName(), project.getProjectName());
            }

            orderCommonService.sendDingTalkMessage("订单", title, content, Collections.singletonList(event.getNewSystemsEngineer()), null);

            // 添加权限
            commonService.personnelSaveProject(order.getProjectNo(), Lists.newArrayList(event.getNewSystemsEngineer()), "0");

        }

    }
}
