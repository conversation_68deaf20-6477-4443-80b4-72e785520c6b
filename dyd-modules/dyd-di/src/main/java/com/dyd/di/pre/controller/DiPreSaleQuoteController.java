package com.dyd.di.pre.controller;

import com.alibaba.fastjson.JSON;
import com.dyd.common.core.annotation.AutoConvertDict;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.api.model.QuoteGpResponse;
import com.dyd.di.contract.constants.ContractConstants;
import com.dyd.di.pre.domain.request.*;
import com.dyd.di.pre.domain.response.*;
import com.dyd.di.pre.service.DiPreSaleQuoteGpService;
import com.dyd.di.pre.service.IPreSaleQuoteService;
import com.dyd.di.pre.service.impl.PreSaleQuoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售报价单
 */
@Slf4j
@RestController
@RequestMapping("/preSaleQuote")
public class DiPreSaleQuoteController {

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;

    @Autowired
    private IPreSaleQuoteService quoteService;

    @Autowired
    private DiPreSaleQuoteGpService diPreSaleQuoteGpService;

    /**
     * 新增报价单
     *
     * @param request
     * @return
     */
    @PostMapping("/add")
    public R add(@RequestBody PreSaleQuoteAddRequest request) {
        log.info("新增报价单请求参数{}", JSON.toJSONString(request));
        String result = preSaleQuoteService.add(request);
        return R.ok(result);
    }


    /**
     * 销售报价单列表
     *
     * @param request
     * @return
     */
    @GetMapping("/getQuoteListPage")
    public R<PageWrapper<List<PreSaleQuoteListResponse>>> getQuoteListPage(PreSaleQuoteListRequest request) {

        return R.ok(preSaleQuoteService.getQuoteListPage(request));
    }


    /**
     * 编辑报价单
     *
     * @param request
     * @return
     */
    @PostMapping("/edit")
    public R edit(@RequestBody PreSaleQuoteEditRequest request) {
        log.info("报价单编辑请求参数{}", JSON.toJSONString(request));
        preSaleQuoteService.edit(request);
        return R.ok();
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getInfo")
    @AutoConvertDict
    public R<PreSaleQuoteInfoResponse> info(@RequestParam(value = "id", required = false) Integer id, @RequestParam(value = "code", required = false) String code) {
        PreSaleQuoteInfoResponse info = preSaleQuoteService.info(id, code);
        return R.ok(info);
    }

    /**
     * 报价单价格计算
     *
     * @param code
     * @return
     */
    @PostMapping("/preSaleQuotationFeeCalc")
    public R<PreSaleQuotationFeeCalcResponse> preSaleQuotationFeeCalc(@RequestBody PreSaleQuotationFeeCalcRequest code) {
        return R.ok(preSaleQuoteService.preSaleQuotationFeeCalc(code));
    }

    /**
     * 临时报价单价格计算
     *
     * @param request
     * @return
     */
    @PostMapping("/preSaleQuotationFeeCalcTemp")
    public R<PreSaleQuotationFeeCalcResponse> preSaleQuotationFeeCalcTemp(@RequestBody PreSaleQuotationFeeCalcTempRequest request) {
        request.getPreSaleInfoList().forEach(x -> {
            if (x.getPreSaleQuote() == null) {
                x.setPreSaleQuote(BigDecimal.ZERO);
            }
        });
        return R.ok(preSaleQuoteService.preSaleQuotationFeeCalcTemp(request));
    }

    /**
     * 销售报价单中间页
     *
     * @param request
     * @return
     */
    @GetMapping("/getInfoTemp")
    public R<PreSaleQuoteInfoTempResponse> getInfoTemp(PreSaleQuoteInfoTempRequest request) {
        log.info("销售报价单中间页请求参数{}", JSON.toJSONString(request));
        return R.ok(preSaleQuoteService.infoTemp(request));
    }

    /**
     * 发起报价
     *
     * @param request
     * @return
     */
    @PostMapping("/lockQuote")
    public R<String> lockQuote(@RequestBody PreSaleQuoteLockRequest request) {
        log.info("发起报价请求参数{}", JSON.toJSONString(request));
        return preSaleQuoteService.lockQuote(request);
    }

    /**
     * 查询报价单 的共享销售
     *
     * @param request
     * @return
     */
    @PostMapping("/queryShareByCode")
    @AutoConvertDict
    public R<List<PreSaleQuotaShareResponse>> queryShareByCode(@RequestBody QueryShareByCodeRequest request) {
        return R.ok(preSaleQuoteService.queryShareByCode(request.getPreSaleQuoteCode()));
    }

    @GetMapping("/repairPreSaleQuoteGp")
    public void repairPreSaleQuoteGp() {
        preSaleQuoteService.repairPreSaleQuoteGp();
    }

    /**
     * 开始交期复核
     */
    @PostMapping("/startReviewDeliveryTime")
    public R startReviewDeliveryTime(@RequestBody StartReviewQuoteRequest request) {
        quoteService.startDeliveryTimeReview(request.getQuoteId());
        return R.ok(true);
    }

    @GetMapping("/selectGp")
    public R<QuoteGpResponse> selectGp(@RequestParam("nicheNo") String nicheNo){
        return R.ok(diPreSaleQuoteGpService.selectGp(nicheNo));
    }

    /**
     * 提前执行
     * @param request
     * @return
     */
    @PostMapping("/advanceExecution")
    public R advanceExecution(@RequestBody AdvanceExecutionRequest request){
        return R.ok(quoteService.advanceExecution(request.getQuoteId()));
    }


    /**
     * 退回方案
     * @Descrption 所有已发起报价单全部变更为已放弃，商机状态变更为方案阶段
     * @param request
     * @return
     */
    @PostMapping("/returnQuote")
    public R ReturnQuote(@RequestBody @Validated ReturnQuoteRequest request){
        log.info("退回方案请求参数{}",JSON.toJSONString(request));
        preSaleQuoteService.returnQuote(request);
        return R.ok();
    }


    /**
     * 商机完成报价按钮
     * @Descript 判断有效报价单是否处于审批通过或提前执行
     * 是
     * @param request
     * @return
     */
    @PostMapping("/finishQuote")
    public R<Boolean> finishQuote(@RequestBody @Validated FinishQuoteRequest request){
        log.info("完成按钮请求参数{}",JSON.toJSONString(request));
        return R.ok(preSaleQuoteService.finishQuote(request));
    }

    @PostMapping("/abandonQuote")
    public R<Void> abandonQuote(@RequestBody PreSaleQuoteEditRequest request) {
        preSaleQuoteService.abandonQuote(request, ContractConstants.STR_EIGHT);
        return R.ok();
    }

}
