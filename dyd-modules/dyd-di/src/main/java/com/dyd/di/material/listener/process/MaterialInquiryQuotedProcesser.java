package com.dyd.di.material.listener.process;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dyd.common.core.utils.MoneyUtils;
import com.dyd.common.core.utils.SimpleTraceUtil;
import com.dyd.di.material.entity.DiMaterialInquiryDetail;
import com.dyd.di.material.entity.DiMaterialInquiryForm;
import com.dyd.di.material.iservice.IDiMaterialInquiryFormService;
import com.dyd.di.materiel.domain.DiMaterielPrice;
import com.dyd.di.materiel.service.IDiMaterielPriceService;
import com.dyd.di.materiel.service.IDiMaterielSupplierService;
import com.dyd.di.pre.domain.PreSaleManifestFee;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.service.IDiPreSaleManifestService;
import com.dyd.di.pre.service.IDiPreSaleMaterielSelectionService;
import com.dyd.di.pre.service.IDiPreSaleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class MaterialInquiryQuotedProcesser {
    @Autowired
    private IDiPreSaleManifestService preSaleManifestService;

    @Autowired
    private IDiMaterielPriceService materielPriceService;

    @Autowired
    private IDiMaterielSupplierService materielSupplierService;

    @Autowired
    private IDiMaterialInquiryFormService materialInquiryFormService;

    @Autowired
    private IDiPreSaleService preSaleService;

    @Autowired
    private IDiPreSaleMaterielSelectionService preSaleMaterielSelectionService;

    public void processMqMessage(String message) {
        SimpleTraceUtil.startTrace();
        log.info("接收到物料价格变更信息" + message);
        List<DiMaterialInquiryDetail> detailList = JSONUtil.toList(message, DiMaterialInquiryDetail.class);
        if (ObjectUtils.isEmpty(detailList)) {
            log.info("物料价格列表信息为空");
            return;
        }
        DiMaterialInquiryForm materialInquiryForm = materialInquiryFormService.lambdaQuery()
                .eq(DiMaterialInquiryForm::getId, detailList.get(0).getMaterialInquiryId())
                .one();
        if (ObjectUtils.isEmpty(materialInquiryForm)) {
            log.info("物料询价单不存在,询价单编号:{}", detailList.get(0).getMaterialInquiryId());
            return;
        }
        //
        DiPreSale preSale = null;
        if (materialInquiryForm.getPreSaleId() != null) {
            preSale = preSaleService.queryDiPreSaleById(Long.valueOf(materialInquiryForm.getPreSaleId()));
        }
//        BigDecimal guideDuration = BigDecimal.ZERO;
//        BigDecimal feeTotal = BigDecimal.ZERO;
        //对应产品方案-费用及各交付周期模块中 对应的 方案版本的物料费用、物料货期两个字段
        //  2. 将对应报价单数据更新至物料基础信息的采购供应商表单中，如出现重复供应商的，则更新原供应商信息，同时将选中的供应商价格及有效期更新至对应物料基础信息的 价格&货期tab的指导物料货期和指导物料价格字段中。
        for (DiMaterialInquiryDetail detail : detailList) {
            //step1 更新方案清单中的物料费用、供应货期字段

            // step2 更新物料供应商表，如无此供应商则插入，无有供应商则更新价格信息
//            DiMaterielSupplier materielSupplier = materielSupplierService.lambdaQuery()
//                    .eq(DiMaterielSupplier::getMaterielId, detail.getMaterialId())
//                    .eq(DiMaterielSupplier::getSupplierId, detail.getSupplier())
//                    .one();
//            if (ObjectUtils.isEmpty(materielSupplier)) {
//                materielSupplier = new DiMaterielSupplier();
//                materielSupplier.setMaterielId(detail.getMaterialId());
//                materielSupplier.setMaterielNo(detail.getMaterielNo());
//                materielSupplier.setSupplierId(Long.valueOf(detail.getSupplier()));
//                materielSupplier.setMaterialCosts(detail.getQuotedPrice());
//                materielSupplier.setSupplierDeliveryDate(BigDecimal.valueOf(Long.valueOf(detail.getDeliveryTime())));
//                materielSupplierService.insertDiSupplier(materielSupplier);
//            } else {
//                materielSupplier.setMaterialCosts(detail.getQuotedPrice());
//                materielSupplier.setSupplierDeliveryDate(BigDecimal.valueOf(Long.valueOf(detail.getDeliveryTime())));
//                materielSupplierService.updateById(materielSupplier);
//            }

            //step3 更新物料 价格表中的物料货期 和 指导物料价格 字段
            if (null == detail.getSelectionId()) {
                changeMaterialPrice(detail);
            } else {
                changeSelectionPrice(detail);
            }
            if (Objects.nonNull(materialInquiryForm.getPreSaleId())) {

                DiPreSaleManifest preSaleManifest = preSaleManifestService.lambdaQuery()
                        .eq(DiPreSaleManifest::getDiPreSaleId, preSale.getId()).orderByDesc(DiPreSaleManifest::getId)
                        .eq(DiPreSaleManifest::getManifestVersion, preSale.getVersion())
                        .eq(DiPreSaleManifest::getMaterialVersion, detail.getMaterialId())
                        .eq(DiPreSaleManifest::getDelFlag, 0)
                        .orderByDesc(DiPreSaleManifest::getId).one();
                if (ObjectUtils.isNotEmpty(preSaleManifest)) {
                    BigDecimal sumMaterialCost = detailList.stream()
                            .map(DiMaterialInquiryDetail::getGuideMaterialCost)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    Optional.ofNullable(sumMaterialCost).ifPresent(price -> preSaleManifest.setFeeTotal(sumMaterialCost));
                    Integer deliveryTime = detailList.stream()
                            .map(DiMaterialInquiryDetail::getDeliveryTime)
                            .map(Integer::parseInt) // 将 String 转换为 Integer
                            .max(Integer::compare).orElse(null);
                    Optional.ofNullable(deliveryTime).ifPresent(time -> preSaleManifest.setSupplyDay(time));

                    Date validityDate = detailList.stream()
                            .map(DiMaterialInquiryDetail::getValidityDate)
                            .min(Date::compareTo).orElse(null);
                    Optional.ofNullable(validityDate).ifPresent(date -> preSaleManifest.setExpirationDateEnd(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()));
                    preSaleManifestService.updateById(preSaleManifest);
//                    PreSaleManifestFee fee = preSaleService.queryPreSaleManifestFee(preSaleManifest);
//                    BigDecimal tmpGuideDuration = Objects.nonNull(fee.getGuideDuration()) ? fee.getGuideDuration() : BigDecimal.ZERO;
//                    guideDuration = guideDuration.compareTo(tmpGuideDuration) > 0 ? guideDuration : tmpGuideDuration;
//                    feeTotal = feeTotal.add(Objects.nonNull(fee.getFeeTotal()) ? fee.getFeeTotal() : BigDecimal.ZERO);
                }
            }

        }
        if (ObjectUtils.isNotEmpty(preSale)) {
            calculateGuideDurationAndCostFeeTotal(preSale);
        }
    }


    public void calculateGuideDurationAndCostFeeTotal(DiPreSale preSale) {
        if (PreSaleTypeEnum.of(preSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {

            log.info("大非标品,询价时不更新方案的成本和获取,方案ID：{},code:{}", preSale.getId(), preSale.getPreSaleCode());
            return;
        }
        List<DiPreSaleManifest> preSaleManifestList = preSaleManifestService.lambdaQuery()
                .eq(DiPreSaleManifest::getDiPreSaleId, preSale.getId())
                .orderByDesc(DiPreSaleManifest::getId)
                .eq(DiPreSaleManifest::getManifestVersion, preSale.getVersion())
                //.eq(DiPreSaleManifest::getMaterialVersion, detail.getMaterialId())
                .eq(DiPreSaleManifest::getDelFlag, 0)
                .orderByDesc(DiPreSaleManifest::getId).list();

        BigDecimal guideDuration = BigDecimal.ZERO;
        BigDecimal feeTotal = BigDecimal.ZERO;
        for (DiPreSaleManifest preSaleManifest : preSaleManifestList) {
            PreSaleManifestFee fee = preSaleService.queryPreSaleManifestFee(preSaleManifest);
            BigDecimal tmpGuideDuration = Objects.nonNull(fee.getGuideDuration()) ? fee.getGuideDuration() : BigDecimal.ZERO;
            guideDuration = guideDuration.compareTo(tmpGuideDuration) > 0 ? guideDuration : tmpGuideDuration;
            feeTotal = feeTotal.add(Objects.nonNull(fee.getFeeTotal()) ? fee.getFeeTotal() : BigDecimal.ZERO);
        }
        log.info(" 方案：{} 更新物料指导工期 {}，理论成本 {},原工期：{}，原成本：{}", preSale.getPreSaleCode(), guideDuration, feeTotal, preSale.getGuideDuration(), preSale.getCostFeeTotal());
        preSale.setGuideDuration(guideDuration);
        preSale.setCostFeeTotal(feeTotal);

        preSaleService.updateById(preSale);
    }

    private void changeMaterialPrice(DiMaterialInquiryDetail detail) {
        DiMaterielPrice materielPrice = materielPriceService.lambdaQuery()
                .eq(DiMaterielPrice::getMaterielId, detail.getMaterialId())
                .one();
        Boolean materialPriceNewRow = false;
        if (Objects.isNull(materielPrice)) {
            materielPrice = new DiMaterielPrice();
            materialPriceNewRow = true;
        }
        DiMaterielPrice finalMaterielPrice = materielPrice;
        finalMaterielPrice.setMaterielId(detail.getMaterialId());
        finalMaterielPrice.setMaterielNo(detail.getMaterielNo());
        finalMaterielPrice.setMaterielType(0L);

        Optional.ofNullable(detail.getDeliveryTime()).ifPresent(deliveryTime ->
        {
            finalMaterielPrice.setGuideMaterielDuration(BigDecimal.valueOf(Long.valueOf(deliveryTime)));
            //指导工期=指导货期+生产工期
            finalMaterielPrice.setGuideDuration(MoneyUtils.sum(BigDecimal.valueOf(Long.valueOf(deliveryTime)), finalMaterielPrice.getGuideProduceDuration()));
        });
//        Optional.ofNullable(detail.getGuideMaterialCost()).ifPresent(quotedPrice -> {
//            materielPrice.setGuideMaterialCosts(quotedPrice);
//            materielPrice.setGuideSumCosts(quotedPrice);
//        });
        Optional.ofNullable(detail.getGuideMaterialCost()).ifPresent(materialCost -> finalMaterielPrice.setGuideMaterialCosts(materialCost));
        Optional.ofNullable(detail.getGuideProductionCosts()).ifPresent(productionCost -> finalMaterielPrice.setGuideProductionCosts(productionCost));
        if (null != detail.getGuideMaterialCost() && null != detail.getGuideProductionCosts()) {
            BigDecimal guideSumCosts = detail.getGuideMaterialCost().add(detail.getGuideProductionCosts());
            finalMaterielPrice.setGuideSumCosts(guideSumCosts);
        }
        Optional.ofNullable(detail.getValidityDate()).ifPresent(validityDate -> finalMaterielPrice.setValidTime(validityDate));
        boolean found;
        if (materialPriceNewRow) {
            found = materielPriceService.save(finalMaterielPrice);
        } else {
            found = materielPriceService.updateById(finalMaterielPrice);
        }
        log.info("物料联动变更结束,物料号:{},物料代码：{} ,found:{} 物料价格:{}", detail.getMaterialId(), detail.getMaterielNo(), found, JSON.toJSONString(finalMaterielPrice));
    }

    private void changeSelectionPrice(DiMaterialInquiryDetail detail) {
//        DiPreSaleMaterielSelection materielSelection = preSaleMaterielSelectionService.lambdaQuery()
//                .eq(DiPreSaleMaterielSelection::getId, detail.getSelectionId())
//                .orderByDesc(DiPreSaleMaterielSelection::getId).last("limit 1").one();
//        materielSelection.setQuotedPrice(detail.getGuideMaterialCost());
//        materielSelection.setGuideCosts(detail.getGuideMaterialCost());
//        materielSelection.setSupplier(detail.getSupplier());
//        materielSelection.setGuideProductionCosts(detail.getGuideProductionCosts());
//        materielSelection.setValidityDate(detail.getValidityDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
//
//        preSaleMaterielSelectionService.updateById(materielSelection);
        preSaleMaterielSelectionService.completeInquiry(List.of(detail));

        log.info("物料选型变更结束,物料选型单号:{}", detail.getSelectionId());
    }
}
