package com.dyd.di.matrix.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 业务版图
 * @TableName di_matrix_territory
 */
@TableName(value ="di_matrix_territory")
@Data
public class DiMatrixTerritory implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分类：1:系统、2:组件、3:配件、4:零件、5:服务、6:工程
     */
    private Integer cateId;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 负责人名字
     */
    private String ownerName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 所属部门
     */
    private String dept;

    /**
     * 等级
     */
    private Integer territoryLevel;

    /**
     * 版图领域
     */
    private Long territoryDomain;

    /**
     * 版图行业
     */
    private Long territoryIndustry;

    /**
     * 版图应用
     */
    private Long territoryApplication;

    /**
     * 版图对象
     */
    private Long territoryObject;

    /**
     * 版图工艺
     */
    private Long territoryProcess;

    /**
     * 配件
     */
    private Long territoryPart;

    /**
     * 配件品类
     */
    private Long territoryPartCategory;

    /**
     * 组件品类
     */
    private Long territoryComponent;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 二级领域
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String domainTwoLevel;

    /**
     * 行业描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String industryDesc;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 1删除 0 正常
     */
    @TableLogic(value = "0",delval = "2")
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
