package com.dyd.di.matrix.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 业务版图保存/编辑
 */
@Data
public class MatrixTerritorySaveAndEditRequest {

    private Long id;

    /**
     * 版图分类
     */
    private Integer cateId;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 负责人名字
     */
    private String ownerName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 所属BU
     */
    private String dept;

    /**
     * 等级
     */
    private Integer territoryLevel;

    /**
     * 版图领域
     */
    private Long territoryDomain;

    /**
     * 版图行业
     */
    private Long territoryIndustry;

    /**
     * 版图应用
     */
    private Long territoryApplication;

    /**
     * 版图对象
     */
    private Long territoryObject;

    /**
     * 版图工艺
     */
    private Long territoryProcess;

    /**
     * 组件品类
     */
    private Long territoryComponent;

    /**
     * 业务名称
     */
    @NotBlank(message = "业务名称不能为空")
    private String bizName;

    /**
     * 配件
     */
    private Long territoryPart;

    /**
     * 配件品类
     */
    private Long territoryPartCategory;

    /**
     * 二级领域
     */
    private String domainTwoLevel;

    /**
     * 行业描述
     */
    private String industryDesc;

    /**
     * 图侦关联
     */
    private List<DiMatrixLabel> diMatrixLabels;

    /**
     * 文件对象
     */
//    private List<DiMatrixFileExtend> fileVo;

    /**
     * 文件分类（版图介绍）
     */
    private List<DiMatrixFileType> introductionFileTypes;

    /**
     * 文件分类（业务照片）
     */
    private List<DiMatrixFileType> photosFileTypes;

    /**
     * 文件分类（烧嘴介绍）
     */
    private List<DiMatrixFileType> mouthFileTypes;

    /**
     * 竞品分析
     */
    private MatrixTerritoryInfoResponse.MatrixTerritoryCompetitorAnalysis matrixTerritoryCompetitorAnalysis;


    /**
     * 销售策略
     */
    private MatrixTerritoryInfoResponse.MatrixTerritoryMarketingStrategy matrixTerritoryMarketingStrategy;


    /**
     * 销售策略
     */
    @Data
    public static class MatrixTerritoryMarketingStrategy{


        /**
         * id
         */
        private Long id;

        /**
         * 销售渠道
         */
        private Integer distributionChannel;

        /**
         * 执行周期
         */
        private LocalDate executionCycle;

        /**
         * 负责人
         */
        private String owner;

        /**
         * 销售策略
         */
        private String salesStrategy;

        /**
         * 价格策略
         */
        private String priceStrategy;

        /**
         * 资源匹配策略
         */
        private String resourceMatchingStrategy;

        /**
         * 产品策略
         */
        private String productStrategy;
    }


    /**
     * 竞品分析
     */
    @Data
    public static class MatrixTerritoryCompetitorAnalysis{
        /**
         * id
         */
        private Long id;

        /**
         * 市场规模
         */
        private Integer marketSize;

        /**
         * 行业趋势
         */
        private Integer industryTrends;

        /**
         * GP2利润空间
         */
        private Integer gp2ProfitMargin;

        /**
         * 客户分析
         */
        private String customerAnalysis;
    }
}
