package com.dyd.di.matrix.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.OssSysCodeEnum;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.marketing.domain.DiMarketingFile;
import com.dyd.di.matrix.domain.DiMatrixFile;
import com.dyd.di.matrix.domain.DiMatrixFileExtend;
import com.dyd.di.matrix.domain.DiMatrixFileType;
import com.dyd.di.matrix.service.DiMatrixFileExtendService;
import com.dyd.di.matrix.service.DiMatrixFileService;
import com.dyd.di.matrix.service.DiMatrixFileTypeService;
import com.dyd.di.matrix.vo.FileAllVo;
import com.dyd.di.matrix.vo.FileTypeVo;
import com.dyd.di.oss.FileVo;
import com.dyd.di.oss.OssService;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图纸资料&产品素菜
 */
@Slf4j
@RestController()
@RequestMapping("/matrixFile")
public class DiMatrixFileTypeController {

    @Autowired
    private DiMatrixFileService diMatrixFileService;

    @Autowired
    private DiMatrixFileTypeService diMatrixFileTypeService;
    @Autowired
    private DiMatrixFileExtendService diMatrixFileExtendService;

    @Autowired
    private OssService ossService;

    /**
     * 根据矩阵ID跟tag下所有文件
     *
     * @return 实体
     */
    @PostMapping(value = "/fileAllList")
    public R<List<FileVo>> fileAllList(@RequestBody FileAllVo vo) {
        List<FileVo> fileAllVoList = new ArrayList<>();
        QueryWrapper<DiMatrixFileType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("belonging", vo.getBelonging());
        queryWrapper.eq("belonging_id", vo.getBelongingId());
        queryWrapper.eq("del_flag", "0");
        queryWrapper.orderByDesc("create_time");
        List<DiMatrixFileType> diMatrixFileTypes = diMatrixFileTypeService.list(queryWrapper);
        for (DiMatrixFileType diMatrixFileType : diMatrixFileTypes){
            fileAllVoList.addAll(getFileList(diMatrixFileType.getId()));
        }
        return R.ok(fileAllVoList);
    }

    /**
     * 分类ID
     * @param id
     * @return
     */
    public List<FileVo> getFileList(Long id){
        List<FileVo> fileVoList = new ArrayList<>();
        QueryWrapper<DiMatrixFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("belonging_id", id);
        queryWrapper.eq("belonging", "0");
        queryWrapper.eq("del_flag", "0");
        queryWrapper.orderByDesc("create_time");
        List<DiMatrixFile> diMatrixFiles = diMatrixFileService.list(queryWrapper);
        Map<String, DiMatrixFile> fileKeyMap = Maps.newHashMap();
        fileKeyMap = diMatrixFiles.stream()
                .collect(Collectors.groupingBy(DiMatrixFile::getFileKey))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get(0)
                ));
        //附件处理
        if (!CollectionUtils.isEmpty(diMatrixFiles)) {
            List<String> fileList = diMatrixFiles.stream()
                    .map(DiMatrixFile::getFileKey)
                    .collect(Collectors.toList());
            List<OssPreviewDTO> previewDTOMap = ossService.getOssFileByList(OssSysCodeEnum.MATRIX.getType(), fileList, 0);
            for (OssPreviewDTO ossPreviewDTO : previewDTOMap) {
                FileVo fileVo = new FileVo();
                fileVo.setId(fileKeyMap.get(ossPreviewDTO.getOssKey()).getId());
                fileVo.setFileKey(ossPreviewDTO.getOssKey());
                fileVo.setFileName(ossPreviewDTO.getMimeName());
                fileVo.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                fileVo.setFileUrl(ossPreviewDTO.getShowUrl());
                //扩展字段处理
                DiMatrixFileExtend diMatrixFileExtend = diMatrixFileExtendService.getBaseMapper().selectOne(Wrappers.<DiMatrixFileExtend>lambdaQuery()
                        .eq(DiMatrixFileExtend::getFileKey, ossPreviewDTO.getOssKey())
                        .last("limit 1"));
                if(Objects.nonNull(diMatrixFileExtend)){
                    List<String> ossKeys = new ArrayList<>();
                    ossKeys.add(diMatrixFileExtend.getThumbnailFileKey());
                    List<OssPreviewDTO> ossFileList = ossService.getOssFileByList(OssSysCodeEnum.MATRIX.getType(), ossKeys, 0);
                    FileVo thumbnailFileVo = new FileVo();
                    thumbnailFileVo.setFileKey(ossFileList.get(0).getOssKey());
                    thumbnailFileVo.setFileName(ossFileList.get(0).getMimeName());
                    thumbnailFileVo.setIsPic(ossFileList.get(0).getMimeType().contains("image") ? 1 : 0);
                    thumbnailFileVo.setFileUrl(ossFileList.get(0).getShowUrl());
                    diMatrixFileExtend.setFileVo(thumbnailFileVo);
                }
                fileVo.setFileExtend(diMatrixFileExtend);
                fileVoList.add(fileVo);
            }
        }
        return fileVoList;
    }
    /**
     * 分类文件列表
     *
     * @return 实体
     */
    @PostMapping(value = "/fileList")
    public R<List<FileVo>> fileList(@RequestBody FileTypeVo vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return R.fail("分类ID不能为空!");
        }
        List<FileVo> fileVoList = new ArrayList<>();
        QueryWrapper<DiMatrixFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("belonging_id", vo.getId());
        queryWrapper.eq("belonging", "0");
        queryWrapper.eq("del_flag", "0");
        queryWrapper.orderByDesc("create_time");
        List<DiMatrixFile> diMatrixFiles = diMatrixFileService.list(queryWrapper);
        Map<String, DiMatrixFile> fileKeyMap = Maps.newHashMap();
        fileKeyMap = diMatrixFiles.stream()
                .collect(Collectors.groupingBy(DiMatrixFile::getFileKey))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get(0)
                ));
        //附件处理
        if (!CollectionUtils.isEmpty(diMatrixFiles)) {
            List<String> fileList = diMatrixFiles.stream()
                    .map(DiMatrixFile::getFileKey)
                    .collect(Collectors.toList());
            List<OssPreviewDTO> previewDTOMap = ossService.getOssFileByList(OssSysCodeEnum.MATRIX.getType(), fileList, 0);
            for (OssPreviewDTO ossPreviewDTO : previewDTOMap) {
                FileVo fileVo = new FileVo();
                fileVo.setId(fileKeyMap.get(ossPreviewDTO.getOssKey()).getId());
                fileVo.setFileKey(ossPreviewDTO.getOssKey());
                fileVo.setFileName(ossPreviewDTO.getMimeName());
                fileVo.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                fileVo.setFileUrl(ossPreviewDTO.getShowUrl());
                //扩展字段处理
                DiMatrixFileExtend diMatrixFileExtend = diMatrixFileExtendService.getBaseMapper().selectOne(Wrappers.<DiMatrixFileExtend>lambdaQuery()
                        .eq(DiMatrixFileExtend::getFileKey, ossPreviewDTO.getOssKey())
                        .last("limit 1"));
                if(Objects.nonNull(diMatrixFileExtend)){
                    List<String> ossKeys = new ArrayList<>();
                    ossKeys.add(diMatrixFileExtend.getThumbnailFileKey());
                    List<OssPreviewDTO> ossFileList = ossService.getOssFileByList(OssSysCodeEnum.MATRIX.getType(), ossKeys, 0);
                    FileVo thumbnailFileVo = new FileVo();
                    thumbnailFileVo.setFileKey(ossFileList.get(0).getOssKey());
                    thumbnailFileVo.setFileName(ossFileList.get(0).getMimeName());
                    thumbnailFileVo.setIsPic(ossFileList.get(0).getMimeType().contains("image") ? 1 : 0);
                    thumbnailFileVo.setFileUrl(ossFileList.get(0).getShowUrl());
                    diMatrixFileExtend.setFileVo(thumbnailFileVo);
                }
                fileVo.setFileExtend(diMatrixFileExtend);
                fileVoList.add(fileVo);
            }
        }
        return R.ok(fileVoList);
    }

    /**
     * 新增分类文件
     *
     * @param diMatrixFile diMatrixFile
     * @return R
     */
    @PostMapping("/addFile")
    public R addFile(@RequestBody DiMatrixFile diMatrixFile) {
        log.info("请求参数{}", JSON.toJSONString(diMatrixFile));
        diMatrixFile.setBelonging("0");
        diMatrixFile.setCreateBy(SecurityUtils.getUsername());
        diMatrixFile.setCreateTime(DateUtils.getNowDate());
        diMatrixFileService.save(diMatrixFile);

        //新增扩展字段
        if(Objects.nonNull(diMatrixFile.getFileExtend())){
            diMatrixFile.getFileExtend().setFileKey(diMatrixFile.getFileKey());
            diMatrixFileExtendService.save(diMatrixFile.getFileExtend());
        }
        return R.ok();
    }

    /**
     * 编辑分类文件
     *
     * @param diMatrixFile diMatrixFile
     * @return R
     */
    @PostMapping("/updateFile")
    public R updateFile(@RequestBody DiMatrixFile diMatrixFile) {
        log.info("请求参数{}", JSON.toJSONString(diMatrixFile));
        diMatrixFile.setBelonging("0");
        diMatrixFile.setUpdateBy(SecurityUtils.getUsername());
        diMatrixFile.setUpdateTime(DateUtils.getNowDate());
        diMatrixFileService.updateById(diMatrixFile);

        //编辑扩展字段
        if(Objects.nonNull(diMatrixFile.getFileExtend())){
            diMatrixFile.getFileExtend().setFileKey(diMatrixFile.getFileKey());
            diMatrixFileExtendService.updateById(diMatrixFile.getFileExtend());
        }
        return R.ok();
    }

    /**
     * 删除文件
     *
     * @param vo 文件key
     * @return R
     */
    @PostMapping("/deleteFile")
    public R deleteFile(@RequestBody FileTypeVo vo) {
        if (StringUtils.isBlank(vo.getFileKey())) {
            return R.fail("文件key不能为空");
        }
        QueryWrapper<DiMatrixFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_key", vo.getFileKey());
        DiMatrixFile diMatrixFile = new DiMatrixFile();
        diMatrixFile.setDelFlag(2);
        diMatrixFile.setUpdateBy(SecurityUtils.getUsername());
        diMatrixFile.setUpdateTime(DateUtils.getNowDate());
        diMatrixFileService.update(diMatrixFile,queryWrapper);
        return R.ok();
    }


    /**
     * 文件分类列表
     *
     * @return 实体
     */
    @PostMapping(value = "/fileTypeList")
    public R<List<DiMatrixFileType>> fileTypeList(@RequestBody FileTypeVo vo) {
        if (StringUtils.isBlank(vo.getBelonging())) {
            return R.fail("所属业务不能为空！");
        }
        if (StringUtils.isBlank(vo.getBelongingId())) {
            return R.fail("关联ID不能为空！");
        }
        QueryWrapper<DiMatrixFileType> queryWrapper = new QueryWrapper<>();
        if(null != vo.getTypeSwitch()){
            queryWrapper.eq("type_switch", vo.getTypeSwitch());
        }
        queryWrapper.eq("belonging", vo.getBelonging());
        queryWrapper.eq("belonging_id", vo.getBelongingId());
        queryWrapper.eq("del_flag", "0");
        queryWrapper.orderByDesc("create_time");
        List<DiMatrixFileType> diMatrixFileTypes = diMatrixFileTypeService.list(queryWrapper);
        return R.ok(diMatrixFileTypes);
    }

    /**
     * 新增文件分类
     *
     * @param diMatrixFileType diMatrixFileType
     * @return R
     */
    @PostMapping("/addFileType")
    public R addFileType(@RequestBody DiMatrixFileType diMatrixFileType) {
        log.info("请求参数{}", JSON.toJSONString(diMatrixFileType));
        Long count = diMatrixFileTypeService.
                lambdaQuery()
                .eq(DiMatrixFileType::getTypeName, diMatrixFileType.getTypeName())
                .eq(DiMatrixFileType::getBelonging, diMatrixFileType.getBelonging())
                .eq(DiMatrixFileType::getBelongingId, diMatrixFileType.getBelongingId())
                .eq(DiMatrixFileType::getDelFlag, 0)
                .count();

        if (count > 0) {
            return R.fail("分类名称不允许重复");
        }
        diMatrixFileType.setCreateBy(SecurityUtils.getUsername());
        diMatrixFileType.setCreateTime(DateUtils.getNowDate());
        diMatrixFileTypeService.save(diMatrixFileType);
        return R.ok();
    }

    /**
     * 编辑文件分类
     *
     * @param diMatrixFileType diMatrixFileType
     * @return R
     */
    @PostMapping("/updateFileType")
    public R updateFileType(@RequestBody DiMatrixFileType diMatrixFileType) {
        log.info("请求参数{}", JSON.toJSONString(diMatrixFileType));
        Long count = diMatrixFileTypeService.
                lambdaQuery()
                .ne(DiMatrixFileType::getId, diMatrixFileType.getId())
                .eq(DiMatrixFileType::getTypeName, diMatrixFileType.getTypeName())
                .eq(DiMatrixFileType::getBelonging, diMatrixFileType.getBelonging())
                .eq(DiMatrixFileType::getBelongingId, diMatrixFileType.getBelongingId())
                .eq(DiMatrixFileType::getDelFlag, 0)
                .count();

        if (count > 0) {
            return R.fail("分类名称不允许重复");
        }
        diMatrixFileType.setUpdateBy(SecurityUtils.getUsername());
        diMatrixFileType.setUpdateTime(DateUtils.getNowDate());
        diMatrixFileTypeService.updateById(diMatrixFileType);
        return R.ok();
    }

    /**
     * 删除文件分类
     *
     * @param vo vo
     * @return R
     */
    @PostMapping("/deleteFileType")
    public R deleteFileType(@RequestBody FileTypeVo vo) {
        log.info("请求参数{}", JSON.toJSONString(vo));
        if (StringUtils.isBlank(vo.getId())) {
            return R.fail("ID不能为空");
        }
        DiMatrixFileType diMatrixFileType = new DiMatrixFileType();
        diMatrixFileType.setId(Long.valueOf(vo.getId()));
        diMatrixFileType.setDelFlag(2);
        diMatrixFileType.setUpdateBy(SecurityUtils.getUsername());
        diMatrixFileType.setUpdateTime(DateUtils.getNowDate());
        diMatrixFileTypeService.updateById(diMatrixFileType);
        return R.ok();
    }

}
