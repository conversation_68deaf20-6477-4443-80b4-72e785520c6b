-- 修复 di_territory_product_mapping 表的唯一索引问题
-- 问题：当前的 udex 索引可能只包含 (territory_id, ref_id)，导致无法为同一个业务版图和引用ID插入多个产品
-- 解决方案：修改唯一索引，包含 product_id 字段

-- 1. 查看当前表结构和索引
-- SHOW CREATE TABLE di_territory_product_mapping;
-- SHOW INDEX FROM di_territory_product_mapping;

-- 2. 删除现有的 udex 唯一索引（如果存在）
-- 注意：在生产环境执行前，请先备份数据并在测试环境验证
DROP INDEX IF EXISTS udex ON di_territory_product_mapping;

-- 3. 创建新的唯一索引，包含所有必要字段
-- 方案A：如果业务允许同一个 ref_id + territory_id 对应多个产品
CREATE UNIQUE INDEX udex ON di_territory_product_mapping (ref_id, territory_id, product_id);

-- 方案B：如果业务只允许同一个 ref_id + territory_id 对应一个产品（不推荐，因为代码逻辑支持多产品）
-- CREATE UNIQUE INDEX udex ON di_territory_product_mapping (ref_id, territory_id);

-- 4. 可选：添加其他有用的索引
-- 为常用查询添加索引
CREATE INDEX idx_ref_id ON di_territory_product_mapping (ref_id);
CREATE INDEX idx_territory_id ON di_territory_product_mapping (territory_id);
CREATE INDEX idx_product_id ON di_territory_product_mapping (product_id);

-- 5. 验证索引创建结果
-- SHOW INDEX FROM di_territory_product_mapping;

-- 注意事项：
-- 1. 在执行此脚本前，请确保没有重复数据
-- 2. 如果存在重复数据，需要先清理重复记录
-- 3. 建议在维护窗口期间执行，避免影响业务
-- 4. 执行前请备份相关数据

-- 清理重复数据的SQL（如果需要）：
/*
-- 查找重复数据
SELECT ref_id, territory_id, product_id, COUNT(*) as cnt 
FROM di_territory_product_mapping 
GROUP BY ref_id, territory_id, product_id 
HAVING COUNT(*) > 1;

-- 删除重复数据，保留ID最小的记录
DELETE t1 FROM di_territory_product_mapping t1
INNER JOIN di_territory_product_mapping t2 
WHERE t1.id > t2.id 
  AND t1.ref_id = t2.ref_id 
  AND t1.territory_id = t2.territory_id 
  AND t1.product_id = t2.product_id;
*/
