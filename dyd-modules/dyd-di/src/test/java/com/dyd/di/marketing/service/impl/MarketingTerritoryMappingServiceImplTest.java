package com.dyd.di.marketing.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.di.marketing.domain.DiTerritoryMapping;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import com.dyd.di.marketing.domain.vo.MarketingTerritoryMappingVO;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper;
import com.dyd.di.matrix.dto.MatrixProductDetailDTO;
import com.dyd.di.matrix.pojo.response.QueryProductByTerritoryResponse;
import com.dyd.di.matrix.service.DiMatrixProductService;
import com.dyd.di.matrix.service.DiMatrixTerritoryService;
import com.dyd.di.matrix.vo.OperateMatrixProductVO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * MarketingTerritoryMappingServiceImpl 测试类
 * 主要测试批量产品映射时的唯一约束问题修复
 */
@ExtendWith(MockitoExtension.class)
class MarketingTerritoryMappingServiceImplTest {

    @Mock
    private DiTerritoryMappingMapper territoryMappingMapper;

    @Mock
    private DiTerritoryProductMappingMapper diTerritoryProductMappingMapper;

    @Mock
    private DiMatrixProductService diMatrixProductService;

    @Mock
    private DiMatrixTerritoryService diMatrixTerritoryService;

    @InjectMocks
    private MarketingTerritoryMappingServiceImpl marketingTerritoryMappingService;

    private MarketingTerritoryMappingVO testVO;
    private QueryProductByTerritoryResponse testResponse;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testVO = new MarketingTerritoryMappingVO();
        testVO.setRefId("DYD-SJ250529-0008");
        testVO.setTerritoryId(22L);
        testVO.setTerritoryJson("{\"test\":\"territory\"}");

        // 模拟多个产品数据
        Map<String, String> product1 = new HashMap<>();
        product1.put("id", "100");
        product1.put("name", "产品1");

        Map<String, String> product2 = new HashMap<>();
        product2.put("id", "200");
        product2.put("name", "产品2");

        Map<String, String> product3 = new HashMap<>();
        product3.put("id", "300");
        product3.put("name", "产品3");

        testResponse = QueryProductByTerritoryResponse.builder()
                .titleMap(new HashMap<>())
                .data(Lists.newArrayList(product1, product2, product3))
                .total(3L)
                .build();

        testVO.setProductJson(JSON.toJSONString(testResponse));
    }

    /**
     * 测试批量产品映射 - 验证每个产品都有独立的productId
     * 这个测试主要验证修复后不会出现重复的productId导致的唯一约束违反
     */
    @Test
    void testAddBatchProductMapping_ShouldCreateUniqueProductMappings() {
        // Mock 依赖方法
        when(territoryMappingMapper.selectCount(any())).thenReturn(1L); // 已存在territory mapping
        when(diMatrixTerritoryService.getMatrixTerritoryInfo(any())).thenReturn(new Object());

        // Mock 产品详情服务返回
        MatrixProductDetailDTO productDetail = new MatrixProductDetailDTO();
        productDetail.setId(100L);
        when(diMatrixProductService.getMatrixProductDetail(any(OperateMatrixProductVO.class)))
                .thenReturn(R.ok(productDetail));

        // 执行测试
        marketingTerritoryMappingService.add(testVO);

        // 验证删除操作被调用（清理已存在的映射）
        verify(diTerritoryProductMappingMapper, times(1)).delete(any());

        // 验证批量插入被调用
        verify(diTerritoryProductMappingMapper, times(1)).insert(any(List.class));

        // 验证产品详情服务被调用3次（对应3个产品）
        verify(diMatrixProductService, times(3)).getMatrixProductDetail(any(OperateMatrixProductVO.class));
    }

    /**
     * 测试单个产品映射
     */
    @Test
    void testAddSingleProductMapping() {
        // 设置单个产品ID
        testVO.setProductId(100L);
        testVO.setProductJson("{\"singleProduct\":true}");

        // Mock 依赖方法
        when(territoryMappingMapper.selectCount(any())).thenReturn(1L);

        MatrixProductDetailDTO productDetail = new MatrixProductDetailDTO();
        productDetail.setId(100L);
        when(diMatrixProductService.getMatrixProductDetail(any(OperateMatrixProductVO.class)))
                .thenReturn(R.ok(productDetail));

        // 执行测试
        marketingTerritoryMappingService.add(testVO);

        // 验证单个产品的删除和插入操作
        verify(diTerritoryProductMappingMapper, times(1)).delete(any());
        verify(diTerritoryProductMappingMapper, times(1)).insert(any(DiTerritoryProductMapping.class));
        verify(diMatrixProductService, times(1)).getMatrixProductDetail(any(OperateMatrixProductVO.class));
    }

    /**
     * 测试空产品数据的处理
     */
    @Test
    void testAddWithEmptyProductData() {
        // 设置空的产品数据
        QueryProductByTerritoryResponse emptyResponse = QueryProductByTerritoryResponse.builder()
                .titleMap(new HashMap<>())
                .data(Lists.newArrayList())
                .total(0L)
                .build();
        testVO.setProductJson(JSON.toJSONString(emptyResponse));

        // Mock 依赖方法
        when(territoryMappingMapper.selectCount(any())).thenReturn(1L);

        // 执行测试
        marketingTerritoryMappingService.add(testVO);

        // 验证不会进行产品映射操作
        verify(diTerritoryProductMappingMapper, never()).insert(any(List.class));
        verify(diMatrixProductService, never()).getMatrixProductDetail(any());
    }
}
