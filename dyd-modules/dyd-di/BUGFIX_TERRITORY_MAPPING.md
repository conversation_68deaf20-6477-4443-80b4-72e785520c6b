# 营销版图映射服务唯一约束违反问题修复

## 问题描述

在 `MarketingTerritoryMappingServiceImpl.add()` 方法中，当处理批量产品映射时出现了数据库唯一约束违反异常：

```
Duplicate entry '22-DYD-SJ250529-0008' for key 'di_territory_product_mapping.udex'
```

## 问题根因分析

### 原始代码问题

在原始代码的第71-82行中：

```java
List<DiTerritoryProductMapping> mappingList = res.getData().stream().map(m -> {
    QueryProductByTerritoryResponse newR =
            QueryProductByTerritoryResponse.builder()
                    .titleMap(res.getTitleMap())
                    .data(Lists.newArrayList(m))
                    .total(1L)
                    .build();
    reqVO.setProductJson(JSON.toJSONString(newR));
    reqVO.setProductId(Long.parseLong(m.get("id")));  // 问题所在：修改共享对象
    return getTerritoryProductMapping(reqVO);
}).toList();
```

**问题根源**：
1. 在 stream 的 map 操作中，所有迭代都在修改同一个 `reqVO` 对象
2. 每次调用 `reqVO.setProductId()` 都会覆盖之前设置的值
3. 最终所有的 `DiTerritoryProductMapping` 对象都具有相同的 `productId`（最后一次设置的值）
4. 当批量插入时，由于 `refId + territoryId + productId` 的组合重复，触发了数据库的唯一约束违反

### 数据库约束

`di_territory_product_mapping` 表存在名为 `udex` 的唯一索引，可能包含以下字段组合：
- `ref_id`
- `territory_id` 
- `product_id`

## 解决方案

### 修复策略

1. **避免共享状态**：为每个产品创建独立的 VO 对象，而不是修改同一个对象
2. **代码重构**：将复杂的逻辑拆分为更小、更易理解的方法
3. **增加注释**：提高代码可读性和维护性

### 具体修改

#### 1. 重构 `add()` 方法

将原来的单一方法拆分为三个方法：
- `add()`: 主入口方法
- `handleSingleProductMapping()`: 处理单个产品映射
- `handleBatchProductMapping()`: 处理批量产品映射

#### 2. 新增 `createProductVO()` 方法

```java
private MarketingTerritoryMappingVO createProductVO(MarketingTerritoryMappingVO originalVO,
                                                   QueryProductByTerritoryResponse res,
                                                   Map<String, Object> productData) {
    // 创建独立的VO对象，避免修改原始对象
    MarketingTerritoryMappingVO productVO = new MarketingTerritoryMappingVO();
    productVO.setRefId(originalVO.getRefId());
    productVO.setTerritoryId(originalVO.getTerritoryId());
    productVO.setTerritoryJson(originalVO.getTerritoryJson());
    productVO.setProductId(Long.parseLong(productData.get("id").toString()));
    productVO.setProductJson(JSON.toJSONString(singleProductResponse));
    
    return productVO;
}
```

#### 3. 修复后的批量处理逻辑

```java
List<DiTerritoryProductMapping> mappingList = res.getData().stream().map(productData -> {
    // 为每个产品创建独立的VO副本
    MarketingTerritoryMappingVO productVO = createProductVO(reqVO, res, productData);
    return getTerritoryProductMapping(productVO);
}).toList();
```

## 修复效果

### 修复前
- 所有产品映射对象具有相同的 `productId`
- 批量插入时触发唯一约束违反异常
- 代码逻辑复杂，难以维护

### 修复后
- 每个产品映射对象具有独立的 `productId`
- 避免了唯一约束违反问题
- 代码结构清晰，易于理解和维护
- 增加了完整的方法注释

## 测试验证

创建了 `MarketingTerritoryMappingServiceImplTest` 测试类，包含以下测试用例：

1. `testAddBatchProductMapping_ShouldCreateUniqueProductMappings()`: 验证批量产品映射的唯一性
2. `testAddSingleProductMapping()`: 验证单个产品映射功能
3. `testAddWithEmptyProductData()`: 验证空数据的处理

## 注意事项

1. **数据一致性**：修复后确保每个产品都有正确的 `productId`
2. **性能影响**：创建独立对象会有轻微的内存开销，但相比解决的问题来说是可接受的
3. **向后兼容**：修改不影响现有的API接口和调用方式

## 相关文件

- `MarketingTerritoryMappingServiceImpl.java`: 主要修复文件
- `MarketingTerritoryMappingServiceImplTest.java`: 测试文件
- `DiTerritoryProductMapping.java`: 实体类
- `MarketingTerritoryMappingVO.java`: VO类
