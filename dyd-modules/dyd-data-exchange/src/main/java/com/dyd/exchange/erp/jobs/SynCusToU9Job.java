package com.dyd.exchange.erp.jobs;

import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.api.RemoteDiService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 同步Di客户到u9
 */
@Component
public class SynCusToU9Job extends IJobHandler {

    @Autowired
    private RemoteDiService remoteDiService;


    @Async
    @XxlJob("SynCusToU9Job")
    @Override
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(jobParam)){
            for(String str:jobParam.split(",")){
                remoteDiService.synCusToU9(str);
            }
        }else{
            remoteDiService.synCusToU9(null);
        }

    }
}
