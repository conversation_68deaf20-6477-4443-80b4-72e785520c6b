package com.dyd.dbc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.dbc.domain.*;
import com.dyd.dbc.enums.LeaveStatusEnum;
import com.dyd.dbc.mapper.*;
import com.dyd.dbc.service.*;
import com.dyd.dingtalk.DingtalkClient;
import com.dyd.dingtalk.bean.attendance.AttendanceDetailedRequest;
import com.dyd.dingtalk.bean.attendance.AttendanceDetailedResponse;
import com.dyd.dingtalk.bean.attendance.GetleaveStatusRequest;
import com.dyd.dingtalk.bean.attendance.GetleaveStatusResponse;
import com.dyd.dingtalk.bean.dept.DeptListRequest;
import com.dyd.dingtalk.bean.dept.DeptListResponse;
import com.dyd.dingtalk.bean.dingTalkGroupMessage.DingTalkGroupMessageRequest;
import com.dyd.dingtalk.bean.user.*;
import com.dyd.dingtalk.constant.DingTalkMsgParamContants;
import com.dyd.dingtalk.constant.DingtalkConstants;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.bean.common.Cond;
import com.dyd.jdy.bean.common.Filter;
import com.dyd.jdy.bean.common.ValueNumber;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.employeeHealth.EmployeeHealthDataRequest;
import com.dyd.jdy.bean.employeeHealth.EmployeeHealthDataResponse;
import com.dyd.jdy.bean.employeeHealth.EmployeeHealthRequest;
import com.dyd.jdy.bean.userInfoJdy.*;
import com.dyd.jdy.util.ValueUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 钉钉数据同步
 *
 * <AUTHOR>
 */
@Service
public class DingtalkServiceImpl implements DingtalkService {
    private Logger logger = LoggerFactory.getLogger(DingtalkServiceImpl.class);
    @Resource
    private DingtalkMapper dingtalkMapper;

    @Autowired
    private DingtalkClient dingtalkClient;

    @Autowired
    private IDdAttendanceDetailedService iDdAttendanceDetailedService;
    @Autowired
    private IDdDeptService iDdDeptService;
    @Autowired
    private IDdUserService iDdUserService;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private IDbcJdyAfterSalesMemberService dbcJdyAfterSalesMemberService;

    @Resource
    private JdyClient jdyClient;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private DdDeptMapper ddDeptMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private DdUserDtoMapper ddUserDtoMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private DdDeptDtoMapper ddDeptDtoMapper;
    private DingtalkService dingtalkService;

    @Override
    public void synchDingtalkUser() {
        UserIdListResponse listUserByDeptResponse = getListUserByDeptResponse(null);

        //先删除数据库表中的数据,在新增
        //dingtalkMapper.deleteUserInfo();
        if (listUserByDeptResponse.getResult().getUserid_list() != null && listUserByDeptResponse.getResult().getUserid_list().size() > 0) {
            int num = listUserByDeptResponse.getResult().getUserid_list().size();
            for (int i = 0; i < num; i++) {
                //循环用户id,通过id查找用户信息
                List<String> userIds = listUserByDeptResponse.getResult().getUserid_list();
                System.out.println("=======================111111111111111111111================");
                System.out.println(userIds);
                //获取某个对应的用户信息
                UserDetailsResponse userDetailsResponse = getObjectByUserId(userIds.get(i));
                UserDetailsResponse.Result o = userDetailsResponse.getResult();
                //封装对象信息
                UserInfo userInfo = new UserInfo();
                userInfo.setDd_user_id(o.getUnionid());
                userInfo.setUser_name(o.getUserid());
                userInfo.setNick_name(o.getName());
                userInfo.setUser_type("00");
                userInfo.setEmail(o.getEmail());
                userInfo.setPhonenumber(o.getMobile());
                userInfo.setSex("0");
                userInfo.setPassword("$10$VpseGkMzuYbZr7x2AhQkaOop/oXtPThgvHUR20naXrpb.nX7Cve.G");
                userInfo.setStatus("0");
                userInfo.setDel_flag("0");
                userInfo.setRemark(o.getRemark());
                dingtalkMapper.saveuserInfo(userInfo);
            }

        }

    }


    @Override
    public void synchDingDurationCalculate() {

    }

    @Override
    public void synchAttendanceDetailed() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String strDate = sdf.format(new Date());
        String endDate = sdf.format(DateUtils.addDays(new Date(),-1));
        DdUser ddUser1 = new DdUser();
        ddUser1.setJobNumber("dyd");
        List<DdUser> lists = iDdUserService.selectDdUserList(ddUser1);
        for (DdUser ddUser : lists) {
            try {
                List<String> list = new ArrayList<>();
                list.add(ddUser.getUserId());
                AttendanceDetailedRequest attendanceDetailedRequest = AttendanceDetailedRequest.builder()
                        .userIds(list)
                        .checkDateFrom(endDate + " 08:50:00")
                        .checkDateTo(strDate + " 08:49:59")
                        .build();
                AttendanceDetailedResponse attendanceDetailedResponse = dingtalkClient.dingtalkUserCall(attendanceDetailedRequest);
                System.out.println(attendanceDetailedResponse);
                for (AttendanceDetailedResponse.Recordresult recordresult : attendanceDetailedResponse.getRecordresult()) {
                    DdAttendanceDetailed ddAttendanceDetailed = new DdAttendanceDetailed();
                    BeanUtils.copyProperties(recordresult, ddAttendanceDetailed);
                    iDdAttendanceDetailedService.insertDdAttendanceDetailed(ddAttendanceDetailed);
                }
            } catch (Exception e) {
                System.out.println("同步考勤打卡报错：" + e);
            }
        }
    }

    /**
     * 前一天缺卡人员
     */
    @Override
    public void sendingAbnormalPersonnel() {
        //判断今天是不是工作日
        LocalDate localDate1 = (DateUtils.addDays(new Date(),1)).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 =  DateUtils.getPreviousWorkingDay(localDate1);
        if(!localDate2.equals(LocalDate.now())){
            System.out.println("今天不是工作日");
            return;
        }
        //获取前一个工作日
        LocalDate previousWorkingDay = DateUtils.getPreviousWorkingDay(LocalDate.now());
        List<Cond> condList = new ArrayList<>();
        condList.add(Cond.builder()
                .field("_widget_1672025149824")
                .method("eq")
                .value(previousWorkingDay.toString())
                .build());
        Filter filter = Filter.builder()
                .cond(condList)
                .build();
        EmployeeHealthDataRequest employeeHealthDataRequest = EmployeeHealthDataRequest.builder()
                .filter(filter)
                .limit(100)
                .build();
        //查询已请假记录
        EmployeeHealthDataResponse employeeHealthDataResponse = jdyClient.jdyCall(employeeHealthDataRequest);

        List<EmployeeHealthDataResponse.FromData> fromDataList = new ArrayList<>();
        if(Objects.nonNull(employeeHealthDataResponse) && CollectionUtil.isNotEmpty(employeeHealthDataResponse.getData())) {
            //异地/居家办公打卡
//            fromDataList = employeeHealthDataResponse.getData().stream().filter(vo -> !"异地/居家办公".equals(vo.get_widget_1672053671185())).collect(Collectors.toList());
            fromDataList = employeeHealthDataResponse.getData();
        }

        String[] userIds = new String[fromDataList.size() + 8];
        for (int i = 0; i < fromDataList.size(); i++) {
            userIds[i] = fromDataList.get(i).get_widget_1672054611493().getUsername();
        }
        userIds[fromDataList.size()] = "01572312964383";//VF老板
        userIds[fromDataList.size() + 1] = "16791906966234625";//张龙 兼职不需要打卡
        userIds[fromDataList.size() + 2] = "012406554966788371";//芳姐
        userIds[fromDataList.size() + 3] = "16988001315525086";//缪健华
        userIds[fromDataList.size() + 4] = "**********-**********";//柯建平
        userIds[fromDataList.size() + 5] = "01344360062523061998";//奚蓓文
        userIds[fromDataList.size() + 6] = "4361353825976985";//曹争光
        userIds[fromDataList.size() + 7] = "16781508525009967";//陈瑞
        String startStr = previousWorkingDay + " 00:00:00";
        String endStr = previousWorkingDay + " 23:59:59";
        Map<String, Object> params = new HashMap<>();
        params.put("startStr", startStr);
        params.put("endStr", endStr);
        params.put("ids", userIds);

        //查询前一个工作日缺卡人员
        List<DdUser> userList = ddUserMapper.getDingAbnormalPersonnel(params);

        String val = "前一个工作日考勤异常人员\n";

        for (DdUser ddUser :userList){

            //当天入职的跳过
            if(StringUtils.isNotBlank(ddUser.getHiredDate()) && !"null".equals(ddUser.getHiredDate())){
                String rzrq = DateUtils.parseDateToStr("yyyy-MM-dd",new Date(Long.valueOf(ddUser.getHiredDate())));
                String dt = DateUtils.parseDateToStr("yyyy-MM-dd",new Date());
                if(dt.equals(rzrq)){
                    continue;
                }
            }

            String name = ddUser.getName().split("-")[0];
            if (2 == name.length()) {
                name = name.charAt(0) + "   " + name.charAt(1);
            }

            //查询是否为没提前申请请假
            GetleaveStatusRequest getleaveStatusRequest = GetleaveStatusRequest.builder()
                    .userid_list(ddUser.getUserId())
                    .start_time(DateUtils.parseDate(startStr).getTime())
                    .end_time(DateUtils.parseDate(endStr).getTime())
                    .offset(0)
                    .size(1)
                    .build();
            GetleaveStatusResponse leaveStatusResponse = dingtalkClient.dingtalkUserCall(getleaveStatusRequest);

            if(Objects.nonNull(leaveStatusResponse) && Objects.nonNull(leaveStatusResponse.getResult())
                  && CollectionUtil.isNotEmpty(leaveStatusResponse.getResult().getLeave_status())){

                val += name+ "|" + "未及时提交出差请假申请\n";
            }else{
                //外出人员请假
                List<String> evectionBeforDays = getEvectionBeforDay(previousWorkingDay);
                if(CollectionUtil.isNotEmpty(evectionBeforDays) && evectionBeforDays.contains(ddUser.getUserId())){
                    val += name+ "|" + "未及时提交出差请假申请\n";
                }else{
                    //商旅出差
                    List<String> businessTravelBeforeDay = getBusinessTravelBeforeDay(previousWorkingDay);
                    if(CollectionUtil.isNotEmpty(businessTravelBeforeDay) && businessTravelBeforeDay.contains(ddUser.getUserId())){
                        val += name+ "|" + "未及时提交出差请假申请\n";
                    }else{
                        if("Outside".equals(ddUser.getExtension())){
                            val += name+ "|" + "外勤流程缺失\n";
                        }else{
                            val += name+ "|" + "上下班均未打卡\n";
                        }
                    }
                }
            }
        }

        if (val.length()<14) {
//            val = "前一个工作日考勤无任何异常，太棒了！";
            return;
        }

        DingTalkGroupMessageRequest dingTalkGroupMessageRequest = new DingTalkGroupMessageRequest();
        DingTalkGroupMessageRequest.At at = new DingTalkGroupMessageRequest.At();
        at.setIsAtAll(true);
        DingTalkGroupMessageRequest.SampleTextMsg sampleTextMsg = new DingTalkGroupMessageRequest.SampleTextMsg();
        sampleTextMsg.setContent(val);
        dingTalkGroupMessageRequest.setMsgParam(JSON.toJSONString(sampleTextMsg));
        dingTalkGroupMessageRequest.setMsgKey(DingTalkMsgParamContants.SAMPLE_TEXT);
        dingTalkGroupMessageRequest.setOpenConversationId("cidQLRkG7pGiT62FELYAphhDQ==");
        dingTalkGroupMessageRequest.setRobotCode(DingtalkConstants.APP_KEY);
        dingTalkGroupMessageRequest.setAt(at);
        dingtalkClient.dingtalkCall(dingTalkGroupMessageRequest);
    }
   // @Async
    @Transactional
    @Override
    public void synchDeptAndUser(List<String> userIds,Integer type) {

        List<DeptListResponse.Result> deptList = new ArrayList<>();
        //获取所有部门
        getChildDeptList(null,deptList);

        //钉钉端存在的部门
        List<Integer> ddDeptIds = deptList.stream().map(DeptListResponse.Result::getDept_id).collect(Collectors.toList());

        //现库中的部门
        List<DdDeptDto> ddDeptDtos = ddDeptDtoMapper.selectList(null);
        Map<Integer, List<DdDeptDto>> ddDeptMap = ddDeptDtos.stream().collect(Collectors.groupingBy(DdDeptDto::getDeptId));
        //钉钉不存在的部门，标记删除
        List<DdDeptDto> noExistDept = ddDeptDtos.stream().filter(ddDeptDto -> !ddDeptIds.contains(ddDeptDto.getDeptId())).collect(Collectors.toList());
        List<Integer> noExistDeptId = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(noExistDept)){
            for(DdDeptDto ddDeptDto:noExistDept){
                ddDeptDto.setDelFlag("1");
                ddDeptDtoMapper.updateById(ddDeptDto);

                noExistDeptId.add(ddDeptDto.getDeptId());
            }
        }


        List<DeptListResponse.Result> existDeptId = deptList.stream().filter(result -> !noExistDeptId.contains(result.getDept_id())).collect(Collectors.toList());
        for(DeptListResponse.Result result:existDeptId){
            DdDeptDto ddDept = new DdDeptDto();
            ddDept.setDeptId(result.getDept_id());
            ddDept.setDeptName(result.getName());
            ddDept.setParentDeptId(result.getParent_id());
            ddDept.setDelFlag("0");
            if(CollectionUtil.isNotEmpty(ddDeptMap.get(result.getDept_id()))){
                ddDept.setId(ddDeptMap.get(result.getDept_id()).get(0).getId());
                ddDeptDtoMapper.updateById(ddDept);
            }else{
                ddDeptDtoMapper.insert(ddDept);
            }
        }

        //用户详情
        List<DeptUserResponse.DeptUserResult.DeptUserData> dataList = new ArrayList<>();
        //遍历部门list 获取用户list
        for (Integer deptId : ddDeptIds) {
            //根据部门查询用户list
            getDeptUserInfo(deptId,0,50,dataList);
        }
        logger.info("所有用户详情{}",JSON.toJSONString(dataList));

        List<String> userIdList = dataList.stream().map(DeptUserResponse.DeptUserResult.DeptUserData::getUserid).collect(Collectors.toList());

        //用户map
        Map<String, List<DeptUserResponse.DeptUserResult.DeptUserData>> deptUserMap = dataList.stream().collect(Collectors.groupingBy(DeptUserResponse.DeptUserResult.DeptUserData::getUserid));

        //钉钉用户数据
        List<DdUserDto> ddUserDtos = ddUserDtoMapper.selectList(null);

        //钉钉用户map，key为钉钉id
        Map<String, List<DdUserDto>> ddUserMap = ddUserDtos.stream().collect(Collectors.groupingBy(DdUserDto::getUserId));

        //已经存在的
        //List<String> existUserIds = ddUserDtos.stream().map(DdUserDto::getUserId).collect(Collectors.toList());

        //新增用户
        List<String> addUserIds = new ArrayList<>();
        if(type == 0) {
            addUserIds.addAll(userIdList);
        }else if(type == 1){
            addUserIds.addAll(userIds);
        }
        //筛选部门的主管
        List<DeptUserResponse.DeptUserResult.DeptUserData> deptUserDataList = dataList.stream().filter(deptUserData -> deptUserData.getLeader()).collect(Collectors.toList());

        //部门主管map，key为部门id,value为部门主管userid
        Map<Long, String> deptUserLeaderMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(deptUserDataList)){
            for(DeptUserResponse.DeptUserResult.DeptUserData deptUserData:deptUserDataList){
                //所属部门id
                List<Long> dept_id_list = deptUserData.getDept_id_list();
                for(Long deptId:dept_id_list){
                    deptUserLeaderMap.put(deptId,deptUserData.getUserid());
                }
            }
        }


        for(DeptListResponse.Result result:existDeptId){
            Integer deptId = result.getDept_id();
            DdDeptDto ddDeptDto = new DdDeptDto();

            if(StringUtils.isEmpty(deptUserLeaderMap.get(Long.parseLong(deptId.toString())))){
                List<String> leaders = new ArrayList<>();
                getDeptLeader(deptId,deptUserLeaderMap,leaders);
                if(CollectionUtil.isNotEmpty(leaders)){
                    ddDeptDto.setLeader(leaders.get(0));
                }
            }else{
                ddDeptDto.setLeader(deptUserLeaderMap.get(Long.parseLong(deptId.toString())));
            }
            if(StringUtils.isNotEmpty(ddDeptDto.getLeader())) {
                ddDeptDtoMapper.update(ddDeptDto, Wrappers.<DdDeptDto>lambdaUpdate().eq(DdDeptDto::getDeptId, deptId));
            }
        }


        if(CollectionUtil.isNotEmpty(addUserIds)) {

            //遍历用户list
            for (String userId : addUserIds) {
                addUserNew(userId, ddUserMap,deptUserMap,deptUserLeaderMap);
            }
        }

        //钉钉不存在的用户
        List<String> deleteUsers = new ArrayList<>();
        if(type == 0) {
            List<DdUserDto> dtos = ddUserDtos.stream().filter(ddUserDto -> !userIdList.contains(ddUserDto.getUserId())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(dtos)){
                deleteUsers = dtos.stream().map(DdUserDto::getUserId).collect(Collectors.toList());
            }
        }else if(type == 2){
            deleteUsers.addAll(userIds);
        }
        if(CollectionUtil.isNotEmpty(deleteUsers)){
            for(String userId:deleteUsers){
                DdUserDto ddUserDto = new DdUserDto();
                ddUserDto.setDelFlag("1");
                ddUserDtoMapper.update(ddUserDto,Wrappers.<DdUserDto>lambdaUpdate().eq(DdUserDto::getUserId,userId));
            }
        }
        logger.info("===============同步钉钉用户结束====================");

        //同步系统部门
        synSysDept();

        //同步系统用户
        synSysUser(null);
    }

    /**
     * 新增钉钉用户
     * @param ddAddUserDto
     */
    @Override
    public void addDdUser(DdAddUserDto ddAddUserDto) {
        for(String userId:ddAddUserDto.getUserId()) {
            UserDetailsResponse userDetailsResponse = getObjectByUserId(userId);
            if (null != userDetailsResponse && null != userDetailsResponse.getResult()) {
                addUser(userDetailsResponse.getResult(), new HashMap<>());
            }
        }
    }

    /**
     * 离职用户
     * @param userIds
     */
    @Override
    public void leaveUser(List<String> userIds) {

    }


    /**
     * 同步系统部门
     */
    @Transactional
    public void synSysDept(){
        //钉钉部门
        List<DdDeptDto> ddDeptDtos = ddDeptDtoMapper.selectList(null);
        if(CollectionUtil.isNotEmpty(ddDeptDtos)){

            //钉钉部门，key为部门id
            Map<Integer, List<DdDeptDto>> ddDeptMap = ddDeptDtos.stream().collect(Collectors.groupingBy(DdDeptDto::getDeptId));

            //系统部门
            List<SysDept> sysDeptS = sysDeptMapper.selectList(null);
            Map<Long, List<SysDept>> sysDeptMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(sysDeptS)) {
                sysDeptMap = sysDeptS.stream().collect(Collectors.groupingBy(SysDept::getDeptId));
            }

            for(DdDeptDto ddDeptDto:ddDeptDtos){
                List<SysDept> sysDeptsList = sysDeptMap.get(Long.parseLong(String.valueOf(ddDeptDto.getDeptId())));

                List<Integer> deptIds = new ArrayList<>();
                //获取父级部门
                getDdDeptIter(deptIds,ddDeptDto.getDeptId(),ddDeptMap);
                if(CollectionUtil.isNotEmpty(sysDeptsList)){
                    SysDept sysDeptDTO = sysDeptsList.get(0);
                    if(1 == ddDeptDto.getParentDeptId()) {
                        sysDeptDTO.setParentId(Long.parseLong("100"));
                    }else{
                        sysDeptDTO.setParentId(Long.parseLong(String.valueOf(ddDeptDto.getParentDeptId())));

                    }
                    sysDeptDTO.setAncestors(deptIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    sysDeptDTO.setDeptName(ddDeptDto.getDeptName());
                    sysDeptDTO.setLeader(ddDeptDto.getLeader());
                    if("1".equals(ddDeptDto.getDelFlag())){
                        sysDeptDTO.setDelFlag("1");
                        sysDeptDTO.setStatus("1");
                    }
                    sysDeptMapper.updateById(sysDeptDTO);
                }else{
                    SysDept sysDept = new SysDept();
                    sysDept.setDeptId(Long.parseLong(String.valueOf(ddDeptDto.getDeptId())));
                    if(1 == ddDeptDto.getParentDeptId()) {
                        sysDept.setParentId(Long.parseLong("100"));

                    }else{
                        sysDept.setParentId(Long.parseLong(String.valueOf(ddDeptDto.getParentDeptId())));

                    }
                    sysDept.setDeptName(ddDeptDto.getDeptName());
                    sysDept.setAncestors(deptIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    sysDept.setLeader(ddDeptDto.getLeader());
                    sysDeptMapper.insert(sysDept);
                }
            }
        }

    }

    /**
     * 递归获取部门所有父部门
     * @param deptIds
     * @param deptId
     * @param ddDeptMap
     */
    public void getDdDeptIter(List<Integer> deptIds,Integer deptId,Map<Integer, List<DdDeptDto>> ddDeptMap){
        List<DdDeptDto> ddDeptDtos = ddDeptMap.get(deptId);
        //父类id
        Integer parentDeptId = ddDeptDtos.get(0).getParentDeptId();
        //没有父类
        if(parentDeptId == 1){
            deptIds.add(0);
            deptIds.add(100);
        }else{
            deptIds.add(parentDeptId);
            getDdDeptIter(deptIds,parentDeptId,ddDeptMap);
        }
    }

    @Override
    public void getLeaveStatus() {
        //请假
        getHoliday();

        //外出
        getEvection();

        //商旅出差
        getBusinessTravel();

        //异地/居家办公
        getOffsite();

    }

    /**
     * 外出人员请假
     * @return
     */
    public List<String> getEvectionBeforDay(LocalDate previousWorkDay) {
        List<String> list = new ArrayList<>();
        try {
            String startStr = previousWorkDay.toString() + " 00:00:00";
            String endStr = previousWorkDay.toString() + " 23:59:59";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String processCode = "PROC-EF6YCS6WO2-LBO32GWWMPD5Q38L02P52-TMW1TKEI-2U";
            List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(), simpleDateFormat.parse(endStr).getTime());
            for (String id : idList) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
                String userId = processInstanceTopVo.getOriginatorUserid();
                list.add(userId);
            }
        }catch (Exception e){
            logger.error("getEvectionBeforDay错误{}",e);
        }
        return list;
    }

    public void getHoliday(){
        String startStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date()) + " 00:00:00";
        String endStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date()) + " 22:59:59";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //请假
        List<DdUser> list = iDdUserService.selectDdUserList(null);
        for (DdUser ddUser : list) {
            try {
                GetleaveStatusRequest getleaveStatusRequest = GetleaveStatusRequest.builder()
                        .userid_list(ddUser.getUserId())
                        .start_time(simpleDateFormat.parse(startStr).getTime())
                        .end_time(simpleDateFormat.parse(endStr).getTime())
                        .offset(0)
                        .size(1)
                        .build();
                GetleaveStatusResponse getleaveStatusResponse = dingtalkClient.dingtalkUserCall(getleaveStatusRequest);
                for (GetleaveStatusResponse.LeaveStatusVO leaveStatusVO : getleaveStatusResponse.getResult().getLeave_status()) {
                    String leaveStatus = LeaveStatusEnum.getDesc(leaveStatusVO.getLeave_code());
                    if (StrUtil.isBlank(leaveStatus)) {
                        leaveStatus = leaveStatusVO.getLeave_code();
                    }
                    String durationUnit = "全天";
                    //小时
                    if ("percent_hour".equals(leaveStatusVO.getDuration_unit()) && 800 > leaveStatusVO.getDuration_percent()) {
                        //半天
                        durationUnit = "半天";
                    }
                    //天
                    if ("percent_day".equals(leaveStatusVO.getDuration_unit()) && 100 > leaveStatusVO.getDuration_percent()) {
                        //半天
                        durationUnit = "半天";
                    }
                    createEmployeeHealth(leaveStatus, ddUser.getUserId(), ddUser.getDeptIdList(), DateUtils.parseDateToStr("yyyy-MM-dd", new Date()), durationUnit);
                }
            } catch (Exception e) {
                System.out.println(e);
            }
        }
    }

    public void getEvection() {
        try {
            String startStr = DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(new Date(), -1)) + " 08:55:00";
            String endStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date()) + " 08:54:59";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String processCode = "PROC-EF6YCS6WO2-LBO32GWWMPD5Q38L02P52-TMW1TKEI-2U";
            List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(), simpleDateFormat.parse(endStr).getTime());
            for (String id : idList) {
                try {
                    OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
                    JSONArray jsonArray = JSONArray.parseArray(processInstanceTopVo.getFormComponentValues().get(8).getValue());
                    String userId = processInstanceTopVo.getOriginatorUserid();
                    String DeptId = processInstanceTopVo.getOriginatorDeptId();
                    String startTime = jsonArray.get(0).toString().substring(0, 10);
                    String endTime = jsonArray.get(1).toString().substring(0, 10);
                    Double sc = Double.valueOf(jsonArray.get(2).toString());
                    int days = diffDate(startTime, endTime);
                    for (int i = 0; i <= days; i++) {
                        String durationUnit = "全天";
                        if (0 == days && 8.0 > sc) {
                            //半天
                            durationUnit = "半天";
                        }
                        Date date = DateUtils.parseDate(startTime);
                        createEmployeeHealth("外出", userId, DeptId, DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(date, i)), durationUnit);
                    }
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }


    public void getOffsite() {
        try {
            String startStr = DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(new Date(), -1)) + " 08:55:00";
            String endStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date()) + " 08:54:59";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String processCode = "PROC-FF6YRLE1N2-WMEJFHP0USR34653KV9M1-K1VK4N0J-3";
            List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(), simpleDateFormat.parse(endStr).getTime());
            for (String id : idList) {
                try {
                    OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
                    JSONArray infoJsonArray = JSONArray.parseArray((JSONArray.parseArray(processInstanceTopVo
                            .getFormComponentValues().get(3).getValue())).toString());
                    String userId = processInstanceTopVo.getOriginatorUserid();
                    String DeptId = processInstanceTopVo.getOriginatorDeptId();
                    String startTime = infoJsonArray.get(0).toString();
                    String endTime = infoJsonArray.get(1).toString();
                    int days = diffDate(startTime, endTime);
                    for (int i = 0; i <= days; i++) {
                        Date date = DateUtils.parseDate(startTime);
                        createEmployeeHealth("异地/居家办公", userId, DeptId, DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(date, i)), "全天");
                    }
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    /**
     *商旅出差
     * @return
     */
    public List<String> getBusinessTravelBeforeDay(LocalDate previousWorkDay) {
        List<String> list = new ArrayList<>();
        try {
            String startStr = previousWorkDay.toString() + " 00:00:00";
            String endStr = previousWorkDay.toString() + " 23:59:59";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String processCode = "PROC-919A7F8C-2B1F-4BAA-9417-EC42CDE56E83";
            List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(), simpleDateFormat.parse(endStr).getTime());
            for (String id : idList) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
                String userId = processInstanceTopVo.getOriginatorUserid();
                list.add(userId);
            }
        } catch (Exception e) {
            System.out.println(e);
        }
        return list;
    }

    public void getBusinessTravel() {
        try {
            String startStr = DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(new Date(), -1)) + " 08:55:00";
            String endStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date()) + " 08:54:59";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String processCode = "PROC-919A7F8C-2B1F-4BAA-9417-EC42CDE56E83";
            List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(), simpleDateFormat.parse(endStr).getTime());
            for (String id : idList) {
                try {
                    OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
                    String userId = processInstanceTopVo.getOriginatorUserid();
                    String DeptId = processInstanceTopVo.getOriginatorDeptId();
                    JSONArray infoJsonArray = JSONArray.parseArray(((JSONObject) JSONArray.parseArray(processInstanceTopVo
                            .getFormComponentValues().get(2).getValue()).get(2)).get("value").toString());
                    for (int i = 0; i < infoJsonArray.size(); i++) {
                        String startTime = ((JSONObject) JSONArray.parseArray(((JSONObject) infoJsonArray.get(i))
                                .get("rowValue").toString()).get(4)).get("value").toString();
                        String endTime = ((JSONObject) JSONArray.parseArray(((JSONObject) infoJsonArray.get(i))
                                .get("rowValue").toString()).get(5)).get("value").toString();
                        int days = diffDate(startTime, endTime);
                        for (int j = 0; j <= days; j++) {
                            Date date = DateUtils.parseDate(startTime);
                            createEmployeeHealth("商旅出差", userId, DeptId, DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(date, j)), "全天");
                        }
                    }
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    /**
     * 计算两个字符串日期之间的天数差
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public int diffDate(String startDate, String endDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 获取日历对象
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        Date date1;
        Date date2;
        try {
            // 字符串转Date
            date1 = simpleDateFormat.parse(startDate);
            date2 = simpleDateFormat.parse(endDate);
            // 设置日历
            calendar1.setTime(date1);
            calendar2.setTime(date2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        long time1 = calendar1.getTimeInMillis();
        long time2 = calendar2.getTimeInMillis();
        // 计算相差天数
        return (int) ((time2 - time1) / (1000 * 3600 * 24));

    }

    @Override
    public void synchAfterSalesMember() {
        dbcJdyAfterSalesMemberService.synchAfterSalesMember();
    }

    @Override
    public void syncUserInfoJdy() {
        //清空销售绩效表单数据
        String dataId = null;
        Boolean sign = true;
        while (sign) {
            try {
                Thread.sleep(200);
                //分页查询订单基础数据列表
                UserInfoJdyDataRequest request = UserInfoJdyDataRequest.builder()
                        .data_id(dataId)
                        .limit(100)
                        .build();
                UserInfoJdyDataResponse response = jdyClient.jdyCall(request);

                if (null != response && null != response.getData() && 0 < response.getData().size()) {
                    //拿到ID删除
                    String [] ids = new String[response.getData().size()];
                    for(int i = 0; i<response.getData().size();i++){
                        ids[i] = response.getData().get(i).get_id();
                    }
                    UserInfoJdyBatchDeleteRequest deleteRequest =  new UserInfoJdyBatchDeleteRequest();
                    deleteRequest.setData_ids(ids);
                    deleteRequest.setApp_id("6593d0432abdc58177625c64");
                    deleteRequest.setEntry_id("6593d076278c38ec38d72028");
                    deleteRequest.setUrlName("/v5/app/entry/data/batch_delete");
                    jdyClient.jdyCallV5(deleteRequest);
                    dataId = response.getData().get(response.getData().size() - 1).get_id();
                    if (response.getData().size() < 100) {
                        sign = false;
                    }
                } else {
                    sign = false;
                }
            }catch (Exception e){
                System.out.println(e);
            }
        }
        //同步到简道云
        List<UserInfoJdy> userList = ddUserMapper.getUserInfoJdy();
        List<UserInfoJdyBatchAddRequest.DataList> dataList = new ArrayList<>();
        for (UserInfoJdy userInfoJdy : userList){
            try {
                ValueString valueString = null;
                ValueNumber valueNumber = null;
                if(null != userInfoJdy.getHiredDate() && !"null".equals(userInfoJdy.getHiredDate())){
                    String rzrq = DateUtils.parseDateToStr("yyyy-MM-dd",new Date(Long.valueOf(userInfoJdy.getHiredDate())));
                    valueString = ValueUtil.getString(rzrq);
                    valueNumber = ValueUtil.getNumber(Double.valueOf(getLife(rzrq)));
                }

                UserInfoJdyBatchAddRequest.DataList dataList1 = UserInfoJdyBatchAddRequest.DataList.builder()
                        ._widget_1704186119123(ValueUtil.getString(userInfoJdy.getJobNumber()))
                        ._widget_1704186119124(ValueUtil.getString(userInfoJdy.getName().split("-")[0]))
                        ._widget_1704186119125(ValueUtil.getString(userInfoJdy.getDeptName()))
                        ._widget_1704186119126(ValueUtil.getString(userInfoJdy.getTitle()))
                        ._widget_1704186119127(valueString)
                        ._widget_1704186119129(valueNumber)
                        ._widget_1704350410845(ValueUtil.getString(userInfoJdy.getUserId()))
                        ._widget_1704350410846(ValueUtil.getNumber(Double.valueOf(userInfoJdy.getDeptId())))
                        .build();
                dataList.add(dataList1);
            }catch (Exception e){
                System.out.println(e);
            }
        }
        //批量插入消息
        if (null != dataList && 0 < dataList.size()) {
            int addQuantity = dataList.size();
            int addPages = 0;
            if (addQuantity % 100 != 0) {
                addPages = addQuantity / 100 + 1;
            } else {
                addPages = addQuantity / 100;
            }
            for (int i = 0; i < addPages; i++) {
                try {
                    Thread.sleep(200);
                    int currentQuantity = 0;
                    if (i == addPages - 1) {
                        currentQuantity = dataList.size();
                    } else {
                        currentQuantity = (i + 1) * 100;
                    }
                    UserInfoJdyBatchAddRequest batchAddRequest = UserInfoJdyBatchAddRequest.builder()
                            .is_start_workflow(true)
                            .data_list(dataList.subList(i * 100, currentQuantity)).build();
                    UserInfoJdyBatchAddResponse reminderResponse = jdyClient.jdyCall(batchAddRequest);
                    System.out.println("批量插入消息结果：》" + reminderResponse);
                }catch (Exception e){

                }
            }
        }
    }

    /**
     * 同步系统用户
     */
    @Transactional
    @Override
    public void synSysUser(String userId) {
        //查询系统用户
        List<com.dyd.dbc.domain.SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery().eq(StringUtils.isNotEmpty(userId),SysUser::getDdUserId,userId));
        //查询钉钉用户表
        List<DdUserDto> ddUsers = ddUserDtoMapper.selectList(null);
        logger.info("{}",JSON.toJSONString(ddUsers));
        //系统用户map
        Map<String, List<com.dyd.dbc.domain.SysUser>> sysUserMap = sysUsers.stream().filter(sysUser -> StringUtils.isNotEmpty(sysUser.getDdUserId())).collect(Collectors.groupingBy(com.dyd.dbc.domain.SysUser::getDdUserId));

        for(DdUserDto ddUser:ddUsers){

            List<SysUser> sysUsersT = sysUserMap.get(ddUser.getUserId());

            if(CollectionUtil.isNotEmpty(sysUsersT)){
                if(ddUser.getDelFlag().equals("1")){
                    for(SysUser sysUserT:sysUsersT) {
                        com.dyd.dbc.domain.SysUser sysUser = new com.dyd.dbc.domain.SysUser();
                        sysUser.setDelFlag("1");
                        sysUser.setStatus("1");
                        sysUserMapper.update(sysUser,Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getUserId,sysUserT.getUserId()));
                    }
                }else{
                    //用户大于1个才处理
                    if(sysUsersT.size() > 1) {
                        int i = 1;
                        for (SysUser sysUserT : sysUsersT) {
                            if (!ddUser.getDeptIdList().equals(String.valueOf(sysUserT.getDeptId()))) {
                                if(sysUsersT.size() > i) {
                                    com.dyd.dbc.domain.SysUser sysUser = new com.dyd.dbc.domain.SysUser();
                                    sysUser.setDelFlag("1");
                                    sysUser.setStatus("1");
                                    sysUserMapper.update(sysUser, Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getUserId, sysUserT.getUserId()));
                                }
                                i++;
                            }else{

                                sysUserT.setDelFlag("0");
                                sysUserT.setStatus("0");
                                sysUserT.setDeptId(Long.parseLong(ddUser.getDeptIdList()));
                                sysUserT.setUserName(ddUser.getJobNumber());
                                sysUserT.setNickName(ddUser.getName());
                                sysUserT.setPhonenumber(ddUser.getMobile());
                                if(StringUtils.isNotEmpty(ddUser.getManagerUserid()) && ddUser.getUserId().equals(ddUser.getManagerUserid())){
                                    sysUserT.setManager("1");
                                }else{
                                    sysUserT.setManager("0");
                                }
                                sysUserMapper.update(sysUserT, Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getUserId, sysUsersT.get(0).getUserId()));
                            }
                        }
                    }else{
                        SysUser sysUser = sysUsersT.get(0);
                        sysUser.setDelFlag("0");
                        sysUser.setStatus("0");
                        sysUser.setDeptId(Long.parseLong(ddUser.getDeptIdList()));
                        sysUser.setUserName(ddUser.getJobNumber());
                        sysUser.setNickName(ddUser.getName());
                        sysUser.setPhonenumber(ddUser.getMobile());
                        if(StringUtils.isNotEmpty(ddUser.getManagerUserid()) && ddUser.getUserId().equals(ddUser.getManagerUserid())){
                            sysUser.setManager("1");
                        }else{
                            sysUser.setManager("0");
                        }
                        sysUserMapper.update(sysUser, Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getUserId, sysUsersT.get(0).getUserId()));
                    }
                }
            }else{
                com.dyd.dbc.domain.SysUser sysUser = new com.dyd.dbc.domain.SysUser();
                sysUser.setDdUserId(ddUser.getUserId());

                List<DdDept> ddDepts = ddDeptMapper.selectList(Wrappers.<DdDept>lambdaQuery().eq(DdDept::getDeptId, ddUser.getDeptIdList()));
                if(CollectionUtil.isEmpty(ddDepts)){
                    continue;
                }

                List<com.dyd.dbc.domain.SysDept> sysDepts = sysDeptMapper.selectList(Wrappers.<com.dyd.dbc.domain.SysDept>lambdaQuery().like(SysDept::getDeptName, ddDepts.get(0).getDeptName()));
                if(CollectionUtil.isEmpty(sysDepts)){
                    continue;
                }

                if("1".equals(ddUser.getDelFlag())){
                    sysUser.setDelFlag("1");
                    sysUser.setStatus("1");
                }
                sysUser.setDeptId(sysDepts.get(0).getDeptId());
                sysUser.setUserName(ddUser.getJobNumber());
                sysUser.setNickName(ddUser.getName().split("-")[0]);
                sysUser.setEmail(ddUser.getEmail());
                sysUser.setPhonenumber(ddUser.getMobile());
                sysUser.setPassword("$2a$10$VpseGkMzuYbZr7x2AhQkaOop/oXtPThgvHUR20naXrpb.nX7Cve.G");
                sysUser.setCreateTime(LocalDateTime.now());
                sysUser.setPreviousJoinedDate(ddUser.getHiredDate());
                if(StringUtils.isNotEmpty(ddUser.getManagerUserid()) && ddUser.getUserId().equals(ddUser.getManagerUserid())){
                    sysUser.setManager("1");
                }else{
                    sysUser.setManager("0");
                }
                sysUserMapper.insert(sysUser);

                //保存用户角色
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getUserId());
                sysUserRole.setRoleId(2L);
                sysUserRoleMapper.insert(sysUserRole);
            }

        }

        //钉钉用户表钉钉ids
        List<String> ddUserIds = ddUsers.stream().map(DdUserDto::getUserId).collect(Collectors.toList());
        List<SysUser> sysUserFilter = sysUsers.stream().filter(sysUser -> !ddUserIds.contains(sysUser.getDdUserId())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(sysUserFilter)){
            for(SysUser sysUser:sysUserFilter){
                sysUser.setStatus("1");
                sysUser.setDelFlag("1");
                sysUserMapper.updateById(sysUser);
            }
        }
    }

    public String getLife(String dateStr) {
        try {
            if (StrUtil.isNotBlank(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String time = dateStr;
                Date Time = sdf.parse(time);
                Date now = new Date();
                long TimeTime = Time.getTime();
                long NowTime = now.getTime();
                long N_T = NowTime - TimeTime;
                long interval1 = N_T / (24 * 60 * 60 * 1000);
                double one22 = Double.valueOf(interval1) / 361;
                String str22 = String.format("%.2f", one22);
                double four22 = Double.parseDouble(str22);
                System.out.println(four22);
                return four22 + "";
            }
        } catch (Exception e) {
            System.out.println(e);
        }
        return null;
    }
    public static void main(String[] args) {
        // 假设入职日期为2020年1月1日
        LocalDate hireDate = LocalDate.of(2020, 6, 1);
        long tenureInYears = ChronoUnit.YEARS.between(hireDate, LocalDate.now());
        System.out.println("司龄为: " + tenureInYears + " 年");
    }
    public void createEmployeeHealth(String leaveStatus, String userName, String userDept, String date, String durationUnit) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        EmployeeHealthRequest.FromData fromData = EmployeeHealthRequest.FromData.builder()
                ._widget_1672025149824(ValueUtil.getString(date))//请假日期
                ._widget_1672025149830(ValueUtil.getNumber(new Double(userDept)))//部门
                ._widget_1672053671185(ValueUtil.getString(leaveStatus))//异常类型
                ._widget_1672054611493(ValueUtil.getString(userName))//人员（主管可代为填写）
                ._widget_1685087377907(ValueUtil.getString(durationUnit))//请假时长
                ._widget_1672283139157(ValueUtil.getString("系统自动抓取"))
                .build();
        EmployeeHealthRequest employeeHealthRequest = EmployeeHealthRequest.builder()
                .data(fromData)
                .build();
        jdyClient.jdyCall(employeeHealthRequest);
    }

    public void addUserNew(String userId,Map<String, List<DdUserDto>> ddUserMap,
                           Map<String, List<DeptUserResponse.DeptUserResult.DeptUserData>> deptUserMap,
                           Map<Long, String> deptUserLeaderMap) {

        DeptUserResponse.DeptUserResult.DeptUserData deptUserData = deptUserMap.get(userId).get(0);
        DdUserDto ddUser = new DdUserDto();
        ddUser.setUserId(deptUserData.getUserid());
        ddUser.setUnionid(deptUserData.getUnionid());
        ddUser.setName(deptUserData.getName());
        ddUser.setAvatar(deptUserData.getAvatar());
        UserDetailsResponse objectByUserId = getObjectByUserId(deptUserData.getUserid());

        ddUser.setManagerUserid(objectByUserId.getResult().getManager_userid());
        ddUser.setMobile(deptUserData.getMobile());
        if(StringUtils.isEmpty(deptUserData.getJob_number())){
            ddUser.setJobNumber("");
        }else{
            ddUser.setJobNumber(deptUserData.getJob_number());
        }

        ddUser.setTitle(deptUserData.getTitle());
        ddUser.setEmail(deptUserData.getEmail());
        ddUser.setWorkPlace(deptUserData.getWork_place());

        String deptList = String.valueOf(deptUserData.getDept_id_list().get(0));

        if("01572312964383".equals(deptUserData.getUserid())){
            deptList = "550931290";
        }
        ddUser.setDeptIdList(deptList);

        String leader = deptUserLeaderMap.get(Long.parseLong(deptList));
        if(StringUtils.isNotEmpty(leader)){
            ddUser.setManagerUserid(leader);
        }else{
            List<String> leaders = new ArrayList<>();
            getDeptLeader(Integer.parseInt(deptList),deptUserLeaderMap,leaders);
            if(CollectionUtil.isNotEmpty(leaders)){
                ddUser.setManagerUserid(leaders.get(0));
            }
        }

        ddUser.setExtension(deptUserData.getExtension());

        ddUser.setHiredDate(String.valueOf(deptUserData.getHired_date()));
        ddUser.setSenior(deptUserData.getLeader().toString());
        ddUser.setAdmin(deptUserData.getAdmin().toString());
        ddUser.setBoss(deptUserData.getBoss().toString());

        ddUser.setDelFlag("0");

        if (CollectionUtil.isNotEmpty(ddUserMap.get(deptUserData.getUserid()))) {
            ddUserDtoMapper.update(ddUser,Wrappers.<DdUserDto>lambdaUpdate().eq(DdUserDto::getUserId,deptUserData.getUserid()));
        } else {
            ddUserDtoMapper.insert(ddUser);
        }
    }

    private void getDeptLeader(Integer deptId,Map<Long, String> deptUserLeaderMap,List<String> leader){
        DdDeptDto ddDeptDto = ddDeptDtoMapper.selectOne(Wrappers.<DdDeptDto>lambdaQuery().eq(DdDeptDto::getDelFlag, 0).eq(DdDeptDto::getDeptId, deptId));
        if(ddDeptDto.getParentDeptId() != 1){
            if (StringUtils.isNotEmpty(deptUserLeaderMap.get(Long.parseLong(ddDeptDto.getParentDeptId().toString())))) {
                leader.add(deptUserLeaderMap.get(Long.parseLong(ddDeptDto.getParentDeptId().toString())));
                return;
            }else {
                getDeptLeader(ddDeptDto.getParentDeptId(),deptUserLeaderMap,leader);
            }
        }
    }

    public void addUser(UserDetailsResponse.Result result,Map<String, List<DdUserDto>> ddUserMap) {
        DdUserDto ddUser = new DdUserDto();
        ddUser.setUserId(result.getUserid());
        ddUser.setUnionid(result.getUnionid());
        ddUser.setName(result.getName());
        ddUser.setAvatar(result.getAvatar());
        ddUser.setManagerUserid(result.getManager_userid());
        ddUser.setMobile(result.getMobile());
        if(null == result.getJob_number()){
            ddUser.setJobNumber("");
        }else{
            ddUser.setJobNumber(result.getJob_number());
        }

        ddUser.setTitle(result.getTitle());
        ddUser.setEmail(result.getEmail());
        ddUser.setWorkPlace(result.getWork_place());

        String deptList = null;
        for (int i = 0; i < result.getDept_id_list().length; i++) {
            deptList = result.getDept_id_list()[i].toString();
            break;
        }
        if("01572312964383".equals(result.getUserid())){
            deptList = "550931290";
        }
        ddUser.setDeptIdList(deptList);
        ddUser.setExtension(result.getExtension());
        String hiredDate = null;
        if (null != result) {
            hiredDate = String.valueOf(result.getHired_date());
        }
        ddUser.setHiredDate(hiredDate);
        ddUser.setSenior(result.getSenior().toString());
        ddUser.setAdmin(result.getAdmin().toString());
        ddUser.setBoss(result.getBoss().toString());

        ddUser.setDelFlag("0");

        if (CollectionUtil.isNotEmpty(ddUserMap.get(result.getUserid()))) {
            ddUserDtoMapper.update(ddUser,Wrappers.<DdUserDto>lambdaUpdate().eq(DdUserDto::getUserId,result.getUserid()));
        } else {
            ddUserDtoMapper.insert(ddUser);
        }
    }

    /**
     * 根据父ID获取所有子层级
     *
     * @param parentId
     */
    private void getChildDeptList(Integer parentId,List<DeptListResponse.Result> deptList) {
        DeptListRequest deptListRequest = DeptListRequest.builder().dept_id(parentId).build();
        DeptListResponse deptListResponse = dingtalkClient.dingtalkUserCall(deptListRequest);
        logger.info("钉钉返回部门{}",JSON.toJSONString(deptListResponse));

        if(Objects.isNull(deptListResponse) || deptListResponse.getErrcode() != 0){
            throw new RuntimeException("查询钉钉部门返回错误"+JSON.toJSONString(deptListResponse));
        }

        if(CollectionUtil.isNotEmpty(deptListResponse.getResult())) {
            for (DeptListResponse.Result result : deptListResponse.getResult()) {
                deptList.add(result);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                getChildDeptList(result.getDept_id(),deptList);
            }
        }
    }

    public void addDept(DeptListResponse.Result result) {
        DdDept ddDept = new DdDept();
        ddDept.setDeptId(result.getDept_id());
        List<DdDept> list = iDdDeptService.selectDdDeptList(ddDept);
        ddDept.setDeptName(result.getName());
        ddDept.setParentDeptId(result.getParent_id());
        ddDept.setDelFlag("0");
        if (null != list && list.size() > 0) {
            iDdDeptService.updateDdDept(ddDept);
        } else {
            iDdDeptService.insertDdDept(ddDept);
        }
    }

    /**
     * 获取部门用户基础信息出参
     *
     * @param deptId
     * @return
     */
    public UserIdListResponse getListUserByDeptResponse(Integer deptId) {
        UserIdListRequest userIdListRequest = UserIdListRequest.builder()
                .dept_id(deptId)
                .build();
        return dingtalkClient.dingtalkUserCall(userIdListRequest);
    }

    public void getLeaveUsers(String nextToken,String startTime,String endTime){

        DdLeaveRecordRequest ddLeaveRecordRequest = new DdLeaveRecordRequest();
        ddLeaveRecordRequest.setStartTime(startTime);
        ddLeaveRecordRequest.setEndTime(endTime);
        if(StringUtils.isEmpty(startTime)) {
            ddLeaveRecordRequest.setStartTime(LocalDate.now().plusMonths(-2L)+"T00:00:00Z");
        }
        if(StringUtils.isEmpty(endTime)) {
            ddLeaveRecordRequest.setEndTime(LocalDate.now()+"T00:00:00Z");
        }
        ddLeaveRecordRequest.setMaxResults(50);
        ddLeaveRecordRequest.setNextToken(nextToken);

        DdLeaveRecordResponse ddLeaveRecordResponse = dingtalkClient.dingtalkGetLeaveUser(ddLeaveRecordRequest);
        if(Objects.nonNull(ddLeaveRecordResponse)){


            List<DdLeaveRecordResponse.Record> records = ddLeaveRecordResponse.getRecords();
            if(CollectionUtil.isNotEmpty(records)) {
                List<DdUserDto> ddUserDtos = ddUserDtoMapper.selectList(Wrappers.<DdUserDto>lambdaQuery().in(DdUserDto::getUserId, records.stream().map(DdLeaveRecordResponse.Record::getUserId).collect(Collectors.toList())));
                if (CollectionUtil.isNotEmpty(ddUserDtos)) {

                    Map<String, List<DdLeaveRecordResponse.Record>> recordMap = records.stream().collect(Collectors.groupingBy(DdLeaveRecordResponse.Record::getUserId));
                    for (DdUserDto ddUserDtot : ddUserDtos) {
                        if (CollectionUtil.isNotEmpty(recordMap.get(ddUserDtot.getUserId())) && "1".equals(ddUserDtot.getDelFlag())) {
                            String leaveTime = recordMap.get(ddUserDtot.getUserId()).get(0).getLeaveTime();
                            ZonedDateTime zdt = ZonedDateTime.parse(leaveTime);
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String normalTime = zdt.format(formatter);
                            ddUserDtot.setFiredDate(normalTime);
                            ddUserDtoMapper.updateById(ddUserDtot);
                        }
                    }
                }
                if (StringUtils.isNotEmpty(ddLeaveRecordResponse.getNextToken())) {
                    getLeaveUsers(ddLeaveRecordResponse.getNextToken(),ddLeaveRecordRequest.getStartTime(),ddLeaveRecordRequest.getEndTime());
                }
            }
        }
    }


    /**
     * 根据部门获取用户详情
     * @param deptId
     * @param cursor
     * @param size
     * @param dataList
     */
    public void getDeptUserInfo(Integer deptId,Integer cursor,Integer size,List<DeptUserResponse.DeptUserResult.DeptUserData> dataList){
        DeptUserRequest deptUserRequest = new DeptUserRequest();
        deptUserRequest.setDept_id(deptId);
        deptUserRequest.setCursor(cursor);
        deptUserRequest.setSize(size);

        DeptUserResponse deptUserResponse = dingtalkClient.dingtalkUserCall(deptUserRequest);

        logger.info("查询部门用户返回{}",JSON.toJSONString(deptUserResponse));
        if(Objects.isNull(deptUserResponse) || deptUserResponse.getErrcode() != 0){
            throw new RuntimeException("查询钉钉部门返回错误"+JSON.toJSONString(deptUserResponse));
        }

        dataList.addAll(deptUserResponse.getResult().getList());
        if(deptUserResponse.getResult().getHas_more()){
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            getDeptUserInfo(deptId,deptUserResponse.getResult().getNext_cursor(),size,dataList);
        }
    }

    /**
     * 获取部门用户基础信息出参
     *
     * @param userid
     * @return
     */
    public UserDetailsResponse getObjectByUserId(String userid) {
        UserDetailsRequest userDetailsRequest = UserDetailsRequest.builder()
                .language("zh_CN")
                .userid(userid)
                .build();
        return dingtalkClient.dingtalkUserCall(userDetailsRequest);
    }
}
