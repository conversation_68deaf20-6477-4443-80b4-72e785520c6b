package com.dyd.dbc.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dyd.common.core.domain.R;
import com.dyd.dbc.DingdingConfig;
import com.dyd.dbc.aop.JobAnnotation;

import com.dyd.dbc.domain.DingCallbackCrypto;
import com.dyd.dbc.service.DingtalkService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 钉钉数据同步
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dingtalk")
public class DingtalkController {

    private Logger logger = LoggerFactory.getLogger(DingtalkController.class);
    @Resource
    private DingtalkService dingtalkService;

    @Autowired
    private DingdingConfig dingdingConfig;

    @PostMapping(value = "/synchDingtalkUser")
    public R<String> synchDingtalkUser() {
        dingtalkService.synchDingtalkUser();
        return R.ok("调用成功！");
    }

    @PostMapping(value = "/synchDingDurationCalculate")
    public R<String> synchDingDurationCalculate() {
        dingtalkService.synchDingDurationCalculate();
        return R.ok("调用成功！");
    }

    /**
     * 发送前一天未打卡人员
     * @return
     */
    @PostMapping(value = "/sendingAbnormalPersonnel")
    public R<String> sendingAbnormalPersonnel() {
        dingtalkService.sendingAbnormalPersonnel();
        return R.ok("调用成功！");
    }

    @PostMapping(value = "/synchAttendanceDetailed")
    public R<String> synchAttendanceDetailed() {
        dingtalkService.synchAttendanceDetailed();
        return R.ok("调用成功！");
    }

    @JobAnnotation
    @PostMapping(value = "/synchDeptAndUser")
    public R<String> synchDeptAndUser() {
        dingtalkService.synchDeptAndUser(null, 0);
        return R.ok("调用成功！");
    }

    /**
     * 钉钉事件推送
     * @param msg_signature
     * @param timeStamp
     * @param nonce
     * @param json
     * @return
     */
    @PostMapping("/synchDeptAndUserEvent")
    public Map<String, String> synchDeptAndUserEvent(@RequestParam(value = "msg_signature", required = false) String msg_signature,
                                        @RequestParam(value = "timestamp", required = false) String timeStamp,
                                        @RequestParam(value = "nonce", required = false) String nonce,
                                        @RequestBody(required = false) JSONObject json){

        try {
            // 1. 从http请求中获取加解密参数

            // 2. 使用加解密类型
            // Constant.OWNER_KEY 说明：
            // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
            //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
            logger.info("钉钉token{},aes{},appkey{}",dingdingConfig.getToken(), dingdingConfig.getAes_key(), dingdingConfig.getAppKey());
            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(dingdingConfig.getToken(), dingdingConfig.getAes_key(), dingdingConfig.getAppKey());
            String encryptMsg = json.getString("encrypt");
            String decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);

            // 3. 反序列化回调事件json数据
            JSONObject eventJson = JSON.parseObject(decryptMsg);
            String eventType = eventJson.getString("EventType");

            logger.info("接收钉钉事件,类型{},数据{}",eventType,decryptMsg);
            // 5. 返回success的加密数据
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            // 4. 根据EventType分类处理
            if ("check_url".equals(eventType)) {
                // 测试回调url的正确性

            } else if ("user_add_org".equals(eventType)) {
                //新增
                // 处理通讯录用户事件
                /*DdAddUserDto ddAddUserDto = new DdAddUserDto();
                ddAddUserDto.setUserId((List<String>)json.get("userId"));
                dingtalkService.addDdUser(ddAddUserDto);*/
                dingtalkService.synchDeptAndUser((List<String>)eventJson.get("UserId"),1);
            } else if ("user_leave_org".equals(eventType)) {
                //离职
                List<String> userIds = (List<String>)eventJson.get("UserId");
                dingtalkService.synchDeptAndUser(userIds,2);
            } else if("org_dept_create".equals(eventType)){
                // 添加其他已注册的
                dingtalkService.synchDeptAndUser(null,0);
            }else if("org_dept_modify".equals(eventType)){
                dingtalkService.synchDeptAndUser(null,0);
            }else if("org_dept_remove".equals(eventType)){
                dingtalkService.synchDeptAndUser(null,0);
            }
             return successMap;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }

    @PostMapping(value = "/synSysUser")
    public R<String> synSysUser(){
        dingtalkService.synSysUser(null);
        return R.ok("调用成功！");
    }

    @PostMapping(value = "/getLeaveStatus")
    public R<String> getLeaveStatus() {
        dingtalkService.getLeaveStatus();
        return R.ok("调用成功！");
    }

    @PostMapping(value = "/synchAfterSalesMember")
    public R<String> synchAfterSalesMember() {
        dingtalkService.synchAfterSalesMember();
        return R.ok("调用成功！");
    }
    @PostMapping(value = "/syncUserInfoJdy")
    public R<String> syncUserInfoJdy() {
        dingtalkService.syncUserInfoJdy();
        return R.ok("调用成功！");
    }



}
